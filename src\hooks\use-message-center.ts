
import { useState, useEffect } from "react";
import { useUser } from "@/contexts/UserContext";
import { Message } from "@/data/rides";

// Temporarily hardcoded for demo
const mockContacts = [
  { id: "user-1", name: "<PERSON><PERSON><PERSON>", avatar: "https://randomuser.me/api/portraits/men/32.jpg" },
  { id: "user-2", name: "<PERSON><PERSON>", avatar: "https://randomuser.me/api/portraits/women/44.jpg" },
  { id: "user-3", name: "<PERSON>", avatar: "https://randomuser.me/api/portraits/men/67.jpg" },
];

export type Contact = {
  id: string;
  name: string;
  avatar: string;
  lastMessage?: Message;
};

export function useMessageCenter() {
  const { user, getMessages, hasUnreadMessages } = useUser();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContact, setSelectedContact] = useState<string | null>(null);
  const [conversations, setConversations] = useState<Contact[]>([]);
  const [unreadMessagesExist, setUnreadMessagesExist] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    // Check for unread messages
    const checkUnreadMessages = async () => {
      if (user) {
        try {
          const hasUnread = await hasUnreadMessages();
          setUnreadMessagesExist(hasUnread);
        } catch (error) {
          console.error("Error checking for unread messages:", error);
        }
      }
    };
    
    checkUnreadMessages();
  }, [user, hasUnreadMessages]);

  const loadConversations = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      const updatedConversations = await Promise.all(
        mockContacts.map(async (contact) => {
          const fetchedMessages = await getMessages(contact.id);
          const lastMessage = fetchedMessages.length > 0 ? fetchedMessages[fetchedMessages.length - 1] : undefined;
          
          return {
            ...contact,
            lastMessage
          };
        })
      );
      
      setConversations(updatedConversations.filter(
        conv => conv.lastMessage || searchTerm ? conv.name.toLowerCase().includes(searchTerm.toLowerCase()) : false
      ));
    } catch (error) {
      console.error("Error fetching conversations:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getLastMessagePreview = (message?: Message) => {
    if (!message) return "";
    
    const isSentByMe = message.senderId === user?.id;
    const preview = message.content.length > 25 
      ? message.content.substring(0, 25) + "..." 
      : message.content;
      
    return isSentByMe ? `You: ${preview}` : preview;
  };
  
  const formatTime = (timestamp?: string) => {
    if (!timestamp) return "";
    
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return "Yesterday";
    }
    
    return date.toLocaleDateString();
  };

  return {
    searchTerm,
    setSearchTerm,
    selectedContact,
    setSelectedContact,
    conversations,
    unreadMessagesExist,
    isLoading,
    loadConversations,
    getLastMessagePreview,
    formatTime
  };
}
