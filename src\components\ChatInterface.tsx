
import { useEffect, useState, useRef } from 'react';
import { useUser } from '@/contexts/UserContext';
import { Message } from '@/data/rides';

const ChatInterface = ({ recipientId, recipientName }: { recipientId: string, recipientName: string }) => {
  const { getMessages, sendMessage, markMessageAsRead } = useUser();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Fetch messages when the component mounts
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);
        const fetchedMessages = await getMessages(recipientId);
        setMessages(fetchedMessages);
        
        // Mark all messages from recipient as read
        const markAsReadPromises = fetchedMessages
          .filter(msg => msg.senderId === recipientId && !msg.read)
          .map(msg => markMessageAsRead(msg.id));
          
        await Promise.all(markAsReadPromises);
      } catch (error) {
        console.error('Error fetching messages:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchMessages();
  }, [recipientId, getMessages, markMessageAsRead]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Send message handler
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    
    try {
      const sentMessage = await sendMessage(recipientId, newMessage);
      if (sentMessage) {
        setMessages(prev => [...prev, sentMessage]);
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };
  
  return (
    <div className="flex flex-col h-[60vh]">
      {loading ? (
        <div className="flex justify-center items-center flex-grow py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          <div className="flex-grow overflow-y-auto p-4 space-y-4 border rounded-md mb-4">
            {messages.length === 0 ? (
              <p className="text-center text-gray-500">No messages yet. Start the conversation!</p>
            ) : (
              messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`p-3 rounded-lg max-w-[80%] ${
                    msg.senderId === recipientId
                      ? "bg-gray-100 dark:bg-gray-800"
                      : "bg-primary/10 ml-auto"
                  }`}
                >
                  <p className="text-sm">{msg.content}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(msg.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
          <div className="flex items-center gap-2">
            <input
              type="text"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              placeholder={`Message ${recipientName}...`}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && newMessage.trim()) {
                  handleSendMessage();
                }
              }}
            />
            <button
              className="h-10 px-4 py-2 bg-primary text-primary-foreground rounded"
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
            >
              Send
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatInterface;
