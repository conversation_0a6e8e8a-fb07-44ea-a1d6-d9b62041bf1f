
import React from "react";
import PageContainer from "@/components/PageContainer";

interface SimpleContentPageProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
}

const SimpleContentPage: React.FC<SimpleContentPageProps> = ({ 
  title, 
  subtitle, 
  children 
}) => {
  return (
    <PageContainer title={title} subtitle={subtitle}>
      <div className="prose prose-lg dark:prose-invert max-w-none animate-fade-in">
        {children}
      </div>
    </PageContainer>
  );
};

export default SimpleContentPage;
