import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ChatInterface from "@/components/ChatInterface";
import ConversationList from "@/components/messages/ConversationList";
import ChatHeader from "@/components/messages/ChatHeader";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { useMessageCenter } from "@/hooks/use-message-center";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageCircle, ArrowLeft, Users } from "lucide-react";

const MessagesPage = () => {
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const { isAuthenticated, user } = useUser();
  const {
    selectedContact,
    setSelectedContact,
    conversations,
    isLoading
  } = useMessageCenter();
  
  const isRTL = language === "ar";
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      navigate('/login?redirect=messages');
      return;
    }
  }, [isAuthenticated, navigate]);

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  const selectedContactDetails = conversations.find(c => c.id === selectedContact);

  const renderMobileView = () => {
    if (selectedContact) {
      return (
        <div className="h-[calc(100vh-200px)] flex flex-col">
          <div className="flex items-center gap-3 p-4 border-b">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedContact(null)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {translations.back || "Back"}
            </Button>
          </div>
          <div className="flex-1 flex flex-col">
            <ChatHeader contact={selectedContactDetails} />
            <div className="flex-1">
              <ChatInterface 
                recipientId={selectedContact} 
                recipientName={selectedContactDetails?.name || ""} 
              />
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="h-[calc(100vh-200px)]">
        <ConversationList
          isOpen={true}
          onSelectContact={(contactId) => setSelectedContact(contactId)}
        />
      </div>
    );
  };

  const renderDesktopView = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      {/* Conversations List */}
      <Card className="md:col-span-1">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {translations.conversations || "Conversations"}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ConversationList
            isOpen={true}
            onSelectContact={(contactId) => setSelectedContact(contactId)}
          />
        </CardContent>
      </Card>

      {/* Chat Area */}
      <Card className="md:col-span-2">
        {selectedContact ? (
          <div className="h-full flex flex-col">
            <CardHeader>
              <ChatHeader contact={selectedContactDetails} />
            </CardHeader>
            <CardContent className="flex-1 p-0">
              <ChatInterface 
                recipientId={selectedContact} 
                recipientName={selectedContactDetails?.name || ""} 
              />
            </CardContent>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                {translations.selectConversation || "Select a conversation"}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {translations.selectConversationDesc || "Choose a conversation from the list to start chatting"}
              </p>
            </div>
          </div>
        )}
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1">
        {/* Messages Header */}
        <div className="bg-gradient-to-r from-primary to-primary/80 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <div className="flex items-center justify-center gap-3 mb-4">
                <MessageCircle className="h-8 w-8" />
                <h1 className="text-3xl md:text-4xl font-bold">
                  {translations.messages || "Messages"}
                </h1>
              </div>
              <p className="text-lg opacity-90">
                {translations.stayConnected || "Stay connected with your fellow travelers"}
              </p>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : conversations.length === 0 ? (
              <Card>
                <CardContent className="py-12">
                  <div className="text-center">
                    <MessageCircle className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                      {translations.noConversations || "No conversations yet"}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-6">
                      {translations.noConversationsDesc || "Start by booking a ride or offering one to connect with other travelers"}
                    </p>
                    <div className="flex gap-4 justify-center">
                      <Button onClick={() => navigate('/search')}>
                        {translations.findRides || "Find Rides"}
                      </Button>
                      <Button variant="outline" onClick={() => navigate('/offer-ride')}>
                        {translations.offerRide || "Offer a Ride"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              isMobile ? renderMobileView() : renderDesktopView()
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default MessagesPage;
