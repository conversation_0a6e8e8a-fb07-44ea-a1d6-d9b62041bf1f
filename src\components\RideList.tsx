
import { useEffect, useState } from "react";
import { Ride } from "@/data/rides";
import RideCard from "@/components/RideCard";
import { useLanguage } from "@/contexts/LanguageContext";
import { Car } from "lucide-react";

const RideList = ({ rides }: { rides: Ride[] }) => {
  const [isLoading, setIsLoading] = useState(true);
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";

  useEffect(() => {
    // Simulate loading state
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4 animate-pulse">
        {[1, 2, 3].map((i) => (
          <div 
            key={i}
            className="bg-gray-100 dark:bg-gray-800 rounded-lg p-5 h-32"
          ></div>
        ))}
      </div>
    );
  }

  if (rides.length === 0) {
    return (
      <div className="text-center py-12">
        <Car className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">
          {translations.noRidesFound}
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          {translations.noRidesDescription}
        </p>
      </div>
    );
  }

  return (
    <div>
      <h2 className={`text-xl font-bold mb-4 ${isRTL ? 'text-right' : ''}`}>
        {translations.availableRides}
      </h2>
      <div className="space-y-4">
        {rides.map((ride) => (
          <RideCard key={ride.id} ride={ride} />
        ))}
      </div>
    </div>
  );
};

export default RideList;
