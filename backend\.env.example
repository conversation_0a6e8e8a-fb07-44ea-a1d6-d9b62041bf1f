# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
# For PostgreSQL (recommended for production)
DATABASE_URL=postgresql://username:password@localhost:5432/cojourneyhub

# For SQLite (development only)
# DATABASE_URL=sqlite:///db.sqlite3

# Redis Configuration (for Celery and Channels)
REDIS_URL=redis://localhost:6379

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# External API Keys (optional)
OPENROUTESERVICE_API_KEY=your-openrouteservice-key
MAPBOX_ACCESS_TOKEN=your-mapbox-token

# File Storage (for production)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=your-region
