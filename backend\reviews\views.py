from rest_framework import generics, status, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Avg, Count
from django.contrib.auth import get_user_model
from .models import Review, ReviewResponse, ReviewFlag
from .serializers import (
    ReviewSerializer, ReviewCreateSerializer, ReviewResponseSerializer,
    ReviewFlagSerializer, UserReviewStatsSerializer
)

User = get_user_model()


class ReviewListCreateView(generics.ListCreateAPIView):
    """List and create reviews"""

    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        # Get reviews for a specific user or ride
        user_id = self.request.query_params.get('user_id')
        ride_id = self.request.query_params.get('ride_id')

        queryset = Review.objects.filter(is_public=True)

        if user_id:
            queryset = queryset.filter(reviewed_id=user_id)

        if ride_id:
            queryset = queryset.filter(ride_id=ride_id)

        return queryset.select_related('reviewer', 'reviewed', 'ride')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ReviewCreateSerializer
        return ReviewSerializer


class ReviewDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Review detail, update, and delete"""

    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Review.objects.filter(reviewer=self.request.user)


@api_view(['GET'])
def user_review_stats(request, user_id):
    """Get review statistics for a user"""

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    reviews = Review.objects.filter(reviewed=user, is_public=True)

    # Calculate statistics
    total_reviews = reviews.count()
    average_rating = reviews.aggregate(Avg('rating'))['rating__avg'] or 0

    # Rating distribution
    rating_distribution = {}
    for i in range(1, 6):
        rating_distribution[str(i)] = reviews.filter(rating=i).count()

    # Recent reviews
    recent_reviews = reviews.order_by('-created_at')[:5]

    stats = {
        'total_reviews': total_reviews,
        'average_rating': round(average_rating, 2),
        'rating_distribution': rating_distribution,
        'recent_reviews': ReviewSerializer(recent_reviews, many=True).data
    }

    return Response(stats)


class ReviewResponseCreateView(generics.CreateAPIView):
    """Create a response to a review"""

    serializer_class = ReviewResponseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        review_id = self.kwargs['review_id']

        try:
            review = Review.objects.get(id=review_id)
        except Review.DoesNotExist:
            raise serializers.ValidationError("Review not found")

        # Only the reviewed user can respond
        if review.reviewed != self.request.user:
            raise permissions.PermissionDenied(
                "You can only respond to reviews about you"
            )

        # Check if response already exists
        if hasattr(review, 'response'):
            raise serializers.ValidationError(
                "You have already responded to this review"
            )

        serializer.save(review=review, responder=self.request.user)


@api_view(['POST'])
def flag_review(request, review_id):
    """Flag a review as inappropriate"""

    try:
        review = Review.objects.get(id=review_id)
    except Review.DoesNotExist:
        return Response({'error': 'Review not found'}, status=status.HTTP_404_NOT_FOUND)

    # Check if already flagged by this user
    if ReviewFlag.objects.filter(review=review, flagger=request.user).exists():
        return Response(
            {'error': 'You have already flagged this review'},
            status=status.HTTP_400_BAD_REQUEST
        )

    serializer = ReviewFlagSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    flag = serializer.save(review=review, flagger=request.user)

    # If review gets multiple flags, mark it for moderation
    flag_count = review.flags.count()
    if flag_count >= 3:  # Threshold for automatic hiding
        review.is_flagged = True
        review.save()

    return Response({'message': 'Review flagged successfully'})


@api_view(['GET'])
def my_reviews(request):
    """Get reviews given and received by current user"""

    given_reviews = Review.objects.filter(
        reviewer=request.user
    ).select_related('reviewed', 'ride')

    received_reviews = Review.objects.filter(
        reviewed=request.user,
        is_public=True
    ).select_related('reviewer', 'ride')

    return Response({
        'given': ReviewSerializer(given_reviews, many=True).data,
        'received': ReviewSerializer(received_reviews, many=True).data
    })


@api_view(['GET'])
def pending_reviews(request):
    """Get rides that need reviews from current user"""

    from rides.models import Ride, RideBooking
    from django.db import models

    # Get completed rides where user was driver
    driver_rides = Ride.objects.filter(
        driver=request.user,
        status='completed'
    ).exclude(
        reviews__reviewer=request.user
    )

    # Get completed bookings where user was passenger
    passenger_bookings = RideBooking.objects.filter(
        user=request.user,
        status='completed'
    ).exclude(
        ride__reviews__reviewer=request.user,
        ride__reviews__reviewed=models.F('ride__driver')
    )

    pending_data = []

    # Add driver rides (can review passengers)
    for ride in driver_rides:
        passengers = ride.bookings.filter(status='completed')
        for booking in passengers:
            pending_data.append({
                'ride_id': ride.id,
                'user_to_review': booking.user.id,
                'user_name': booking.user.full_name,
                'is_driver_review': True,
                'ride_info': f"{ride.origin} → {ride.destination}"
            })

    # Add passenger bookings (can review driver)
    for booking in passenger_bookings:
        pending_data.append({
            'ride_id': booking.ride.id,
            'user_to_review': booking.ride.driver.id,
            'user_name': booking.ride.driver.full_name,
            'is_driver_review': False,
            'ride_info': f"{booking.ride.origin} → {booking.ride.destination}"
        })

    return Response(pending_data)
