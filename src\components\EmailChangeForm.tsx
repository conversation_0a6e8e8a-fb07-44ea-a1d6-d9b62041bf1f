
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

type EmailChangeStep = "initial" | "otp-verification" | "new-email";

const EmailChangeForm = () => {
  const { user, setUser } = useUser();
  const { translations } = useLanguage();
  const [step, setStep] = useState<EmailChangeStep>("initial");
  const [isLoading, setIsLoading] = useState(false);
  const [newEmail, setNewEmail] = useState("");

  // Initial form schema
  const initialFormSchema = z.object({
    currentEmail: z.string().email(translations.invalidEmail || "Invalid email address"),
  });

  // OTP verification schema
  const otpFormSchema = z.object({
    otp: z.string().length(6, translations.otpMustBe6Digits || "OTP must be 6 digits"),
  });

  // New email schema
  const newEmailFormSchema = z.object({
    email: z.string().email(translations.invalidEmail || "Invalid email address"),
  });

  // Forms
  const initialForm = useForm<z.infer<typeof initialFormSchema>>({
    resolver: zodResolver(initialFormSchema),
    defaultValues: {
      currentEmail: user?.email || "",
    },
  });

  const otpForm = useForm<z.infer<typeof otpFormSchema>>({
    resolver: zodResolver(otpFormSchema),
    defaultValues: {
      otp: "",
    },
  });

  const newEmailForm = useForm<z.infer<typeof newEmailFormSchema>>({
    resolver: zodResolver(newEmailFormSchema),
    defaultValues: {
      email: "",
    },
  });

  const onInitialSubmit = async (data: z.infer<typeof initialFormSchema>) => {
    setIsLoading(true);
    
    // In a real app, we would send verification email here
    // For demo purposes, we're just showing a success toast and moving to the next step
    setTimeout(() => {
      setIsLoading(false);
      toast.success(translations.verificationCodeSent || "Verification code sent to your email");
      setStep("otp-verification");
    }, 1500); // Simulate network delay
  };

  const onOtpSubmit = async (data: z.infer<typeof otpFormSchema>) => {
    setIsLoading(true);
    
    // In a real app, we would verify the OTP here
    // For demo purposes, we're just checking if the OTP is "123456"
    setTimeout(() => {
      setIsLoading(false);
      
      if (data.otp === "123456") {
        toast.success(translations.otpVerified || "Verification successful");
        setStep("new-email");
      } else {
        toast.error(translations.invalidOtp || "Invalid verification code");
        otpForm.setError("otp", { 
          type: "manual", 
          message: translations.invalidOtp || "Invalid verification code"
        });
      }
    }, 1500); // Simulate network delay
  };

  const onNewEmailSubmit = async (data: z.infer<typeof newEmailFormSchema>) => {
    setIsLoading(true);
    setNewEmail(data.email);
    
    // In a real app, we would update the email in the database
    // For demo purposes, we're just updating the local state
    setTimeout(() => {
      if (user) {
        const updatedUser = {
          ...user,
          email: data.email,
        };
        setUser(updatedUser);
        
        setIsLoading(false);
        toast.success(translations.emailUpdated || "Email updated successfully");
        setStep("initial");
      }
    }, 1500); // Simulate network delay
  };

  // Reset the whole flow
  const resetFlow = () => {
    setStep("initial");
    otpForm.reset();
    newEmailForm.reset();
  };

  return (
    <div className="space-y-6">
      {step === "initial" && (
        <>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{translations.changeEmail || "Change Email"}</h3>
            <p className="text-sm text-muted-foreground">
              {translations.changeEmailDesc || "To change your email, we'll send a verification code to your current email"}
            </p>
          </div>
          
          <Form {...initialForm}>
            <form onSubmit={initialForm.handleSubmit(onInitialSubmit)} className="space-y-4">
              <FormField
                control={initialForm.control}
                name="currentEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{translations.currentEmail || "Current Email"}</FormLabel>
                    <FormControl>
                      <Input {...field} disabled className="bg-muted" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {translations.sendingCode || "Sending code..."}
                  </>
                ) : (
                  translations.sendVerificationCode || "Send Verification Code"
                )}
              </Button>
            </form>
          </Form>
        </>
      )}
      
      {step === "otp-verification" && (
        <>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{translations.verifyYourEmail || "Verify Your Email"}</h3>
            <p className="text-sm text-muted-foreground">
              {translations.enterVerificationCode || "Enter the verification code sent to your email"}
            </p>
          </div>
          
          <Form {...otpForm}>
            <form onSubmit={otpForm.handleSubmit(onOtpSubmit)} className="space-y-4">
              <FormField
                control={otpForm.control}
                name="otp"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <InputOTP maxLength={6} {...field}>
                        <InputOTPGroup>
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={resetFlow}>
                  {translations.back || "Back"}
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {translations.verifying || "Verifying..."}
                    </>
                  ) : (
                    translations.verify || "Verify"
                  )}
                </Button>
              </div>
            </form>
          </Form>
          
          <div className="text-center text-sm">
            <Button variant="link" onClick={() => {
              toast.info(translations.codeSentAgain || "Verification code sent again");
            }}>
              {translations.resendCode || "Resend code"}
            </Button>
          </div>
        </>
      )}
      
      {step === "new-email" && (
        <>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{translations.enterNewEmail || "Enter New Email"}</h3>
            <p className="text-sm text-muted-foreground">
              {translations.enterNewEmailDesc || "Enter your new email address"}
            </p>
          </div>
          
          <Form {...newEmailForm}>
            <form onSubmit={newEmailForm.handleSubmit(onNewEmailSubmit)} className="space-y-4">
              <FormField
                control={newEmailForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{translations.newEmail || "New Email"}</FormLabel>
                    <FormControl>
                      <Input {...field} type="email" placeholder="<EMAIL>" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={() => setStep("otp-verification")}>
                  {translations.back || "Back"}
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {translations.updating || "Updating..."}
                    </>
                  ) : (
                    translations.updateEmail || "Update Email"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </>
      )}
    </div>
  );
};

export default EmailChangeForm;
