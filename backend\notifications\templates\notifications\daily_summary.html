<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Summary - CoJourneyHub</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .notification-item {
            background-color: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 6px;
            border-left: 4px solid #2563eb;
        }
        .notification-time {
            color: #6b7280;
            font-size: 12px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>CoJourneyHub</h1>
        <p>Daily Summary for {{ date|date:"F d, Y" }}</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ user.full_name }},</h2>
        
        <p>Here's what happened in your CoJourneyHub account today:</p>
        
        {% for notification in notifications %}
        <div class="notification-item">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.content }}</p>
            <div class="notification-time">{{ notification.created_at|date:"g:i A" }}</div>
        </div>
        {% endfor %}
        
        <a href="http://localhost:3000/notifications" class="button">View All Notifications</a>
    </div>
    
    <div class="footer">
        <p>This daily summary was sent to {{ user.email }}.</p>
        <p>You can manage your notification preferences in your account settings.</p>
        <p>&copy; 2024 CoJourneyHub. All rights reserved.</p>
    </div>
</body>
</html>
