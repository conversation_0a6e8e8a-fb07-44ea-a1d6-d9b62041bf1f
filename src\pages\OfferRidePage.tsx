
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import RideOfferForm from "@/components/ride-offer/RideOfferForm";

const OfferRidePage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { translations } = useLanguage();
  const { isAuthenticated } = useUser();

  // Check if user is authenticated
  useEffect(() => {
    if (!isAuthenticated && !location.pathname.includes("/login")) {
      toast.info(translations.loginRequiredForOfferRide || "Please login to offer a ride");
      navigate("/login?redirect=offer-ride");
    }
  }, [isAuthenticated, navigate, translations, location.pathname]);

  if (!isAuthenticated) {
    // Don't render the full form if not authenticated
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-1 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-12">
          <RideOfferForm />
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default OfferRidePage;
