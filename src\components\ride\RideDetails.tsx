
import { MapPin, Clock, Car } from "lucide-react";
import { Ride } from "@/data/rides";
import { useLanguage } from "@/contexts/LanguageContext";

interface RideDetailsProps {
  ride: Ride;
}

export const RideDetails = ({ ride }: RideDetailsProps) => {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  
  const formattedDate = new Date(ride.departureTime).toLocaleDateString(
    language === "ar" ? "ar-TN" : "en-US", 
    { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }
  );
  
  const formattedTime = new Date(ride.departureTime).toLocaleTimeString(
    language === "ar" ? "ar-TN" : "en-US", 
    { hour: '2-digit', minute: '2-digit' }
  );

  return (
    <div className={`space-y-2 w-full lg:w-3/5 ${isRTL ? 'lg:pr-0 lg:pl-4' : 'lg:pl-0 lg:pr-4'}`}>
      <h3 className="font-bold text-lg md:text-xl flex items-center">
        <MapPin className={`h-5 w-5 text-primary flex-shrink-0 ${isRTL ? 'ml-2' : 'mr-2'}`} />
        <span>{ride.origin} → {ride.destination}</span>
      </h3>
      
      <div className={`flex items-center ${isRTL ? 'flex-row-reverse justify-end' : ''}`}>
        <Clock className={`h-4 w-4 text-gray-500 flex-shrink-0 ${isRTL ? 'ml-2' : 'mr-2'}`} />
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {formattedDate} · {formattedTime}
        </span>
      </div>
      
      <div className={`flex items-center ${isRTL ? 'flex-row-reverse justify-end' : ''}`}>
        <Car className={`h-4 w-4 text-gray-500 flex-shrink-0 ${isRTL ? 'ml-2' : 'mr-2'}`} />
        <span className="text-sm text-gray-700 dark:text-gray-300">{ride.carModel} · {ride.carColor}</span>
      </div>
      
      <div className={`flex items-center flex-wrap gap-2 ${isRTL ? 'justify-end' : ''}`}>
        {ride.features.map((feature, index) => (
          <span key={index} className="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded">
            {feature}
          </span>
        ))}
      </div>
    </div>
  );
};
