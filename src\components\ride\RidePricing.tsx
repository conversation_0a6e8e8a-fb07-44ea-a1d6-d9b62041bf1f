
import { CreditCard, Users } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

interface RidePricingProps {
  price: number;
  seatsAvailable: number;
}

export const RidePricing = ({ price, seatsAvailable }: RidePricingProps) => {
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";

  return (
    <div className={`mt-4 lg:mt-0 w-full lg:w-2/5 flex lg:flex-col ${isRTL ? 'lg:items-start' : 'lg:items-end'} gap-4 justify-between`}>
      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <CreditCard className={`h-5 w-5 text-primary ${isRTL ? 'ml-2' : 'mr-2'}`} />
        <span className="text-xl font-bold">{price} {translations.dnt || "DNT"}</span>
        <span className="text-xs text-gray-500 ml-1">{translations.perSeat}</span>
      </div>
      
      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <Users className={`h-5 w-5 text-primary ${isRTL ? 'ml-2' : 'mr-2'}`} />
        <span className="text-sm">
          {seatsAvailable} {seatsAvailable > 1 
            ? translations.seatsAvailable 
            : translations.seatAvailable}
        </span>
      </div>
    </div>
  );
};
