const API_BASE_URL = 'http://localhost:8000/api';

export interface BookingRequest {
  ride: string;
  seats_requested: number;
  pickup_location?: string;
  dropoff_location?: string;
  passenger_name: string;
  passenger_phone: string;
  special_requests?: string;
}

export interface PendingBooking {
  id: string;
  ride: {
    id: string;
    origin: string;
    destination: string;
    departure_time: string;
    price: number;
    driver: {
      id: string;
      full_name: string;
      rating: number;
    };
  };
  passenger: {
    id: string;
    full_name: string;
    phone_number: string;
    rating: number;
  };
  seats_requested: number;
  pickup_location: string;
  dropoff_location: string;
  passenger_name: string;
  passenger_phone: string;
  special_requests: string;
  status: 'pending' | 'confirmed' | 'rejected' | 'cancelled';
  created_at: string;
  updated_at: string;
  responded_at?: string;
  driver_response?: string;
}

class BookingService {
  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || errorData.detail || 'Request failed');
    }

    return response.json();
  }

  // Create a pending booking request
  async requestBooking(rideId: string, bookingData: Omit<BookingRequest, 'ride'>): Promise<PendingBooking> {
    return this.makeRequest(`/rides/${rideId}/request-booking/`, {
      method: 'POST',
      body: JSON.stringify({
        ...bookingData,
        ride: rideId,
      }),
    });
  }

  // Get pending bookings (for both drivers and passengers)
  async getPendingBookings(): Promise<PendingBooking[]> {
    return this.makeRequest('/rides/pending/');
  }

  // Driver responds to a pending booking
  async respondToBooking(
    pendingId: string, 
    action: 'confirm' | 'reject', 
    driverResponse?: string
  ): Promise<any> {
    return this.makeRequest(`/rides/pending/${pendingId}/respond/`, {
      method: 'POST',
      body: JSON.stringify({
        action,
        driver_response: driverResponse || '',
      }),
    });
  }

  // Get confirmed bookings
  async getConfirmedBookings(): Promise<any[]> {
    return this.makeRequest('/rides/bookings/');
  }

  // Cancel a confirmed booking
  async cancelBooking(bookingId: string): Promise<void> {
    return this.makeRequest(`/rides/bookings/${bookingId}/cancel/`, {
      method: 'POST',
    });
  }

  // Get ride statistics
  async getRideStats(): Promise<any> {
    return this.makeRequest('/rides/stats/');
  }

  // Search rides
  async searchRides(searchParams: any): Promise<any[]> {
    return this.makeRequest('/rides/search/', {
      method: 'POST',
      body: JSON.stringify(searchParams),
    });
  }

  // Get user's rides (offered or booked)
  async getUserRides(): Promise<any[]> {
    return this.makeRequest('/rides/my-rides/');
  }

  // Create a new ride (for drivers)
  async createRide(rideData: any): Promise<any> {
    return this.makeRequest('/rides/', {
      method: 'POST',
      body: JSON.stringify(rideData),
    });
  }

  // Update a ride
  async updateRide(rideId: string, rideData: any): Promise<any> {
    return this.makeRequest(`/rides/${rideId}/`, {
      method: 'PATCH',
      body: JSON.stringify(rideData),
    });
  }

  // Delete a ride
  async deleteRide(rideId: string): Promise<void> {
    return this.makeRequest(`/rides/${rideId}/`, {
      method: 'DELETE',
    });
  }

  // Get ride details
  async getRideDetails(rideId: string): Promise<any> {
    return this.makeRequest(`/rides/${rideId}/`);
  }

  // Get nearby rides
  async getNearbyRides(lat: number, lng: number, radius: number = 50): Promise<any[]> {
    return this.makeRequest(`/rides/nearby/?lat=${lat}&lng=${lng}&radius=${radius}`);
  }
}

export const bookingService = new BookingService();
