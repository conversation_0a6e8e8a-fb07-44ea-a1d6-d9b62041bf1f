
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Car, Menu, X, User as UserIcon, Settings, LogOut, Star } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import ThemeSwitcher from "@/components/ThemeSwitcher";
import LoyaltyPoints from "@/components/LoyaltyPoints";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useIsMobile } from "@/hooks/use-mobile";
import NotificationBell from "./notifications/NotificationBell";
import MessageBell from "./messages/MessageBell";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";

const Navbar = () => {
  const { isAuthenticated, user, logout } = useUser();
  const { translations, language } = useLanguage();
  const location = useLocation();
  const isMobile = useIsMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const isRTL = language === "ar";
  const [loyaltyDialogOpen, setLoyaltyDialogOpen] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  
  useEffect(() => {
    // Close mobile menu when route changes
    setIsMenuOpen(false);
  }, [location.pathname]);
  
  const getInitials = (name: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map(part => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };
  
  return (
    <header 
      className={`sticky top-0 z-50 w-full transition-all duration-200 bg-white dark:bg-gray-900 ${
        scrolled ? "shadow-md" : ""
      }`}
      dir={isRTL ? "rtl" : "ltr"}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <Car className="h-6 w-6 text-primary" />
              <span className={`font-bold text-xl ${isRTL ? "mr-2" : ""}`}>CoSesameHub</span>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <nav className={`hidden md:flex space-x-8 ${isRTL ? "space-x-reverse" : ""}`}>
            <Link to="/" className="nav-link font-medium">
              {translations.home}
            </Link>
            <Link to="/about" className="nav-link font-medium">
              {translations.aboutUsTitle}
            </Link>
            <Link to="/how-it-works" className="nav-link font-medium">
              {translations.howItWorks}
            </Link>
            <Link to="/safety" className="nav-link font-medium">
              {translations.safetyTitle}
            </Link>
          </nav>
          
          <div className={`hidden md:flex items-center space-x-4 ${isRTL ? "space-x-reverse" : ""}`}>
            {isAuthenticated ? (
              <>
                <NotificationBell />
                <MessageBell />
                
                <Dialog open={loyaltyDialogOpen} onOpenChange={setLoyaltyDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span>{user?.loyaltyPoints || 0}</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl">
                    <LoyaltyPoints isDialog={true} />
                  </DialogContent>
                </Dialog>
                
                <ThemeSwitcher />
                <LanguageSwitcher />
                
                {user?.role === 'driver' && (
                  <Link to="/offer-ride">
                    <Button className="font-medium">
                      {translations.offerRide}
                    </Button>
                  </Link>
                )}
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="font-medium flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user?.profilePicture || `https://ui-avatars.com/api/?name=${user?.fullName || user?.email}&background=0D8ABC&color=fff`} />
                        <AvatarFallback>{getInitials(user?.fullName || user?.email)}</AvatarFallback>
                      </Avatar>
                      <span>{user?.fullName || translations.profile}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link to="/profile" className="w-full cursor-pointer flex items-center">
                        <UserIcon className="mr-2 h-4 w-4" />
                        <span>{translations.profile || "Profile"}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/settings" className="w-full cursor-pointer flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>{translations.settings || "Settings"}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout} className="cursor-pointer flex items-center">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>{translations.logout || "Disconnect"}</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <ThemeSwitcher />
                <LanguageSwitcher />
                <Link to="/login">
                  <Button variant="outline" size="sm">{translations.login}</Button>
                </Link>
                <Link to="/signup">
                  <Button size="sm">{translations.signup}</Button>
                </Link>
              </>
            )}
          </div>
          
          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-4">
            {isAuthenticated && (
              <>
                <NotificationBell />
                <MessageBell />
                
                <Dialog open={loyaltyDialogOpen} onOpenChange={setLoyaltyDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-1 p-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span>{user?.loyaltyPoints || 0}</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl">
                    <LoyaltyPoints isDialog={true} />
                  </DialogContent>
                </Dialog>
              </>
            )}
            <ThemeSwitcher />
            <LanguageSwitcher />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle Menu"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-900 shadow-lg">
          <div className="container mx-auto px-4 py-3 space-y-1">
            <Link to="/" className="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
              {translations.home}
            </Link>
            <Link to="/about" className="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
              {translations.aboutUsTitle}
            </Link>
            <Link to="/how-it-works" className="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
              {translations.howItWorks}
            </Link>
            <Link to="/safety" className="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
              {translations.safetyTitle}
            </Link>
            
            {isAuthenticated ? (
              <>
                {user?.role === 'driver' && (
                  <Link to="/offer-ride" className="block py-2 px-3 rounded-md bg-primary text-white dark:text-primary-foreground my-2">
                    {translations.offerRide}
                  </Link>
                )}
                <Link to="/profile" className="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
                  {translations.profile}
                </Link>
                <Link to="/settings" className="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
                  {translations.settings || "Settings"}
                </Link>
                <button
                  onClick={logout}
                  className="block w-full text-left py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  {translations.logout}
                </button>
              </>
            ) : (
              <div className="grid grid-cols-2 gap-2 py-2">
                <Link to="/login" className="py-2 px-3 text-center rounded-md border border-gray-300 dark:border-gray-600">
                  {translations.login}
                </Link>
                <Link to="/signup" className="py-2 px-3 text-center rounded-md bg-primary text-white">
                  {translations.signUp}
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
