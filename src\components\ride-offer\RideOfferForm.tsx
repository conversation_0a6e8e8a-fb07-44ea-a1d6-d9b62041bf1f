
import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Car } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import LocationFields from "./LocationFields";
import DepartureFields from "./DepartureFields";
import RideDetailFields from "./RideDetailFields";
import CarDetailFields from "./CarDetailFields";
import Map from "@/components/Map";
import type { PendingRideOffer } from "@/types/user";
import { apiService } from "@/services/apiService";

const RideOfferForm = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [originCoords, setOriginCoords] = useState<{ lat: number; lng: number } | undefined>();
  const [destinationCoords, setDestinationCoords] = useState<{ lat: number; lng: number } | undefined>();
  const [vehicles, setVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const { translations, language } = useLanguage();
  const { isAuthenticated, pendingRideOffer, setPendingRideOffer, addLoyaltyPoints, user } = useUser();
  const isRTL = language === "ar";

  // Fetch user's vehicles
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const userVehicles = await apiService.getVehicles();
        setVehicles(userVehicles);
        if (userVehicles.length > 0) {
          setSelectedVehicle(userVehicles[0]);
        }
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        toast.error('Failed to load your vehicles');
      }
    };

    if (isAuthenticated) {
      fetchVehicles();
    }
  }, [isAuthenticated]);

  // Create dynamic form schema based on current language
  const formSchema = z.object({
    origin: z.string().min(2, { message: translations.pleaseEnterStartingPoint }),
    destination: z.string().min(2, { message: translations.pleaseEnterDestination }),
    departureDate: z.string().min(1, { message: translations.pleaseSelectTravelDate }),
    departureTime: z.string().min(1, { message: translations.departureTime + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    availableSeats: z.string().min(1, { message: translations.availableSeats + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    price: z.string().min(1, { message: translations.pricePerSeat + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    vehicleId: z.string().min(1, { message: "Please select a vehicle" }),
    notes: z.string().optional(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      origin: pendingRideOffer?.origin || "",
      destination: pendingRideOffer?.destination || "",
      departureDate: pendingRideOffer?.departureDate || "",
      departureTime: pendingRideOffer?.departureTime || "",
      availableSeats: selectedVehicle?.seats?.toString() || "4",
      price: pendingRideOffer?.price || "",
      vehicleId: selectedVehicle?.id?.toString() || "",
      notes: "",
    },
  });

  // Update form when selected vehicle changes
  useEffect(() => {
    if (selectedVehicle) {
      form.setValue('vehicleId', selectedVehicle.id.toString());
      form.setValue('availableSeats', selectedVehicle.seats.toString());
    }
  }, [selectedVehicle, form]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!isAuthenticated) {
      // Save form data and redirect to login - ensure all fields are provided
      const rideOffer: PendingRideOffer = {
        origin: values.origin,
        destination: values.destination,
        departureDate: values.departureDate,
        departureTime: values.departureTime,
        availableSeats: values.availableSeats,
        price: values.price,
        carModel: values.carModel,
        carColor: values.carColor
      };

      setPendingRideOffer(rideOffer);
      toast.info(translations.loginRequiredForOfferRide || "Please login to offer a ride");
      navigate("/login?redirect=offer-ride");
      return;
    }

    if (!user?.id) {
      toast.error("User information not available. Please try logging in again.");
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculate arrival time (estimated as 30 min per 100km)
      const departureDateTime = new Date(`${values.departureDate}T${values.departureTime}`);
      const estimatedDistance = Math.floor(Math.random() * 200) + 50; // Random distance between 50-250km
      const travelTimeMinutes = Math.floor(estimatedDistance * 0.3); // 0.3 minutes per km
      const arrivalDateTime = new Date(departureDateTime.getTime() + travelTimeMinutes * 60000);

      // Create ride data
      const rideData = {
        origin: values.origin,
        destination: values.destination,
        departure_time: departureDateTime.toISOString(),
        price: parseFloat(values.price),
        seats_available: parseInt(values.availableSeats),
        vehicle: parseInt(values.vehicleId),
        notes: values.notes || "",
        origin_lat: originCoords?.lat,
        origin_lng: originCoords?.lng,
        destination_lat: destinationCoords?.lat,
        destination_lng: destinationCoords?.lng
      };

      // Create the ride using the API service
      const newRide = await apiService.createRide(rideData);

      if (newRide) {
        // Add loyalty points for offering a ride
        addLoyaltyPoints(50);

        // Success notification
        toast.success(translations.ridePostedSuccessfully, {
          description: translations.loyaltyPointsAdded?.replace("{points}", "50") || "50 loyalty points added to your account!",
        });

        // Clear pending offer
        setPendingRideOffer(null);

        navigate("/");
      } else {
        toast.error("Failed to create ride. Please try again.");
      }
    } catch (error) {
      console.error('Error creating ride:', error);
      toast.error("Failed to create ride. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
      <div className={`flex items-center gap-2 mb-6 ${isRTL ? 'justify-end' : 'justify-start'}`}>
        <Car className="h-6 w-6 text-primary" />
        <h1 className={`text-2xl font-bold dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
          {translations.offerRideTitle}
        </h1>
      </div>

      <p className={`text-gray-500 dark:text-gray-400 mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
        {translations.offerRideDescription}
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <LocationFields
              control={form.control}
              isRTL={isRTL}
              onOriginChange={setOriginCoords}
              onDestinationChange={setDestinationCoords}
            />
            <DepartureFields control={form.control} isRTL={isRTL} />
            <RideDetailFields control={form.control} isRTL={isRTL} />
            <CarDetailFields
              control={form.control}
              isRTL={isRTL}
              vehicles={vehicles}
              selectedVehicle={selectedVehicle}
              onVehicleSelect={setSelectedVehicle}
            />
          </div>

          <div className={`pt-4 ${isRTL ? 'text-right' : 'text-left'}`}>
            <Button type="submit" className="w-full md:w-auto" disabled={isSubmitting}>
              {isSubmitting ? translations.publishing : translations.publishRide}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RideOfferForm;
