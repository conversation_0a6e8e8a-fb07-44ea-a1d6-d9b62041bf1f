import { describe, it, expect, beforeEach, vi } from 'vitest';
import { rideService } from '@/services/rideService';
import { supabase } from '@/lib/supabase';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn(() => ({ data: [], error: null }))
        })),
        gte: vi.fn(() => ({
          ilike: vi.fn(() => ({
            order: vi.fn(() => ({ data: [], error: null }))
          }))
        })),
        ilike: vi.fn(() => ({
          gte: vi.fn(() => ({
            order: vi.fn(() => ({ data: [], error: null }))
          }))
        })),
        order: vi.fn(() => ({ data: [], error: null }))
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => ({ data: null, error: null }))
        }))
      }))
    }))
  },
  isSupabaseConfigured: vi.fn(() => true)
}));

describe('RideService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('searchRides', () => {
    it('should search rides with basic parameters', async () => {
      const mockRides = [
        {
          id: '1',
          origin: 'Tunis',
          destination: 'Sousse',
          departure_time: '2024-01-01T10:00:00Z',
          arrival_time: '2024-01-01T12:00:00Z',
          price: 25,
          distance: '140 km',
          seats_available: 3,
          car_model: 'Peugeot 308',
          car_color: 'White',
          driver_id: 'driver-1',
          features: ['AC', 'Music'],
          driver: {
            id: 'driver-1',
            full_name: 'Ahmed Ben Ali',
            avatar_url: '',
            rating: 4.8,
            ride_count: 45
          }
        }
      ];

      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        ilike: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockRides, error: null })
      };

      const mockSelect = vi.fn().mockReturnValue(mockQuery);
      const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });
      
      (supabase.from as any).mockReturnValue({ select: mockSelect });

      const result = await rideService.searchRides({
        origin: 'Tunis',
        destination: 'Sousse',
        passengers: 2
      });

      expect(result).toHaveLength(1);
      expect(result[0].origin).toBe('Tunis');
      expect(result[0].destination).toBe('Sousse');
    });

    it('should handle search errors gracefully', async () => {
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: null, error: new Error('Database error') })
      };

      (supabase.from as any).mockReturnValue({ select: vi.fn().mockReturnValue(mockQuery) });

      const result = await rideService.searchRides({});
      expect(result).toEqual([]);
    });
  });

  describe('createRide', () => {
    it('should create a new ride successfully', async () => {
      const mockRideData = {
        origin: 'Tunis',
        destination: 'Sousse',
        departureTime: '2024-01-01T10:00:00Z',
        arrivalTime: '2024-01-01T12:00:00Z',
        price: 25,
        distance: '140 km',
        seatsAvailable: 3,
        carModel: 'Peugeot 308',
        carColor: 'White'
      };

      const mockCreatedRide = {
        id: 'new-ride-1',
        ...mockRideData,
        departure_time: mockRideData.departureTime,
        arrival_time: mockRideData.arrivalTime,
        seats_available: mockRideData.seatsAvailable,
        car_model: mockRideData.carModel,
        car_color: mockRideData.carColor,
        driver_id: 'driver-1',
        driver: {
          full_name: 'Test Driver',
          avatar_url: '',
          rating: 5.0,
          ride_count: 0
        }
      };

      const mockInsert = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockCreatedRide, error: null })
        })
      });

      (supabase.from as any).mockReturnValue({ insert: mockInsert });

      const result = await rideService.createRide(mockRideData, 'driver-1');

      expect(result).toBeTruthy();
      expect(result?.origin).toBe('Tunis');
      expect(result?.destination).toBe('Sousse');
    });

    it('should handle creation errors', async () => {
      const mockRideData = {
        origin: 'Tunis',
        destination: 'Sousse',
        departureTime: '2024-01-01T10:00:00Z',
        arrivalTime: '2024-01-01T12:00:00Z',
        price: 25,
        distance: '140 km',
        seatsAvailable: 3,
        carModel: 'Peugeot 308',
        carColor: 'White'
      };

      const mockInsert = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: new Error('Creation failed') })
        })
      });

      (supabase.from as any).mockReturnValue({ insert: mockInsert });

      const result = await rideService.createRide(mockRideData, 'driver-1');
      expect(result).toBeNull();
    });
  });

  describe('getRideById', () => {
    it('should fetch a ride by ID', async () => {
      const mockRide = {
        id: 'ride-1',
        origin: 'Tunis',
        destination: 'Sousse',
        departure_time: '2024-01-01T10:00:00Z',
        arrival_time: '2024-01-01T12:00:00Z',
        price: 25,
        distance: '140 km',
        seats_available: 3,
        car_model: 'Peugeot 308',
        car_color: 'White',
        driver_id: 'driver-1',
        driver: {
          full_name: 'Test Driver',
          avatar_url: '',
          rating: 4.5,
          ride_count: 10
        }
      };

      const mockQuery = {
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockRide, error: null })
        })
      };

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue(mockQuery)
      });

      const result = await rideService.getRideById('ride-1');

      expect(result).toBeTruthy();
      expect(result?.id).toBe('ride-1');
      expect(result?.origin).toBe('Tunis');
    });
  });

  describe('getUserRides', () => {
    it('should fetch offered rides for a user', async () => {
      const mockRides = [
        {
          id: 'ride-1',
          origin: 'Tunis',
          destination: 'Sousse',
          departure_time: '2024-01-01T10:00:00Z',
          arrival_time: '2024-01-01T12:00:00Z',
          price: 25,
          distance: '140 km',
          seats_available: 3,
          car_model: 'Peugeot 308',
          car_color: 'White',
          driver_id: 'user-1',
          driver: {
            full_name: 'Test User',
            avatar_url: '',
            rating: 4.5,
            ride_count: 5
          }
        }
      ];

      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockRides, error: null })
      };

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue(mockQuery)
      });

      const result = await rideService.getUserRides('user-1', 'offered');

      expect(result).toHaveLength(1);
      expect(result[0].driverId).toBe('user-1');
    });

    it('should fetch booked rides for a user', async () => {
      const mockBookings = [
        {
          ride: {
            id: 'ride-1',
            origin: 'Tunis',
            destination: 'Sousse',
            departure_time: '2024-01-01T10:00:00Z',
            arrival_time: '2024-01-01T12:00:00Z',
            price: 25,
            distance: '140 km',
            seats_available: 3,
            car_model: 'Peugeot 308',
            car_color: 'White',
            driver_id: 'driver-1',
            driver: {
              full_name: 'Other Driver',
              avatar_url: '',
              rating: 4.8,
              ride_count: 20
            }
          }
        }
      ];

      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockBookings, error: null })
      };

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue(mockQuery)
      });

      const result = await rideService.getUserRides('user-1', 'booked');

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('ride-1');
    });
  });
});
