# CoJourneyHub - Complete Ride-Sharing Platform

A modern, full-stack ride-sharing platform built with **React + TypeScript frontend** and **Django REST API backend**. CoJourneyHub connects drivers and passengers for convenient, affordable, and eco-friendly transportation across Tunisia.

## 🚀 Quick Start

```bash
# Complete setup (both frontend and backend)
python setup_complete_app.py

# Or manual setup:
# 1. Backend setup
cd backend && python setup.py

# 2. Frontend setup
npm install && npm run dev
```

## ✨ Features

### 🚗 Core Functionality
- **Ride Offering**: Drivers can post rides with route, time, price, and car details
- **Smart Booking**: Advanced search and booking with real-time availability
- **Real-time Messaging**: WebSocket-powered chat between drivers and passengers
- **User Profiles**: Comprehensive profiles with ratings, statistics, and verification
- **Review System**: Community-driven trust through detailed ratings and reviews

### 🗺️ Advanced Map Integration (FREE)
- **OpenStreetMap Integration**: No API costs, fully functional maps
- **Tunisia Location Database**: 100+ pre-loaded locations with coordinates
- **Interactive Route Planning**: Visual route selection and optimization
- **Location Autocomplete**: Smart search for cities, universities, airports, landmarks
- **Distance Calculation**: Accurate distance and duration estimates
- **Real-time Tracking**: Live location sharing during rides

### 💰 Smart Pricing & Payments
- **Dynamic Pricing**: Market-driven pricing with driver flexibility
- **Multiple Payment Methods**: Cash, mobile money, digital payments
- **Automatic Calculations**: Smart fare splitting and cost estimation
- **Loyalty System**: Points for frequent users and good ratings

### 🔒 Enterprise-Grade Security
- **JWT Authentication**: Secure token-based authentication
- **Row-Level Security**: Database-level access control
- **Input Validation**: Comprehensive data validation and sanitization
- **CORS Protection**: Secure cross-origin resource sharing
- **SQL Injection Protection**: Parameterized queries and ORM protection

### 📱 Modern User Experience
- **Responsive Design**: Perfect on desktop, tablet, and mobile
- **Multi-language Support**: English, French, and Arabic
- **Dark Mode**: Eye-friendly interface with theme switching
- **Progressive Web App**: App-like experience in browsers
- **Offline Capability**: Basic functionality without internet

## 🏗️ Technology Stack

### Frontend
- **React 18** + **TypeScript** for type-safe development
- **Vite** for lightning-fast development and optimized builds
- **Tailwind CSS** for responsive, utility-first styling
- **Shadcn/ui** for consistent, accessible components
- **React Router** for client-side navigation
- **React Hook Form** + **Zod** for robust form handling
- **Leaflet** + **OpenStreetMap** for free mapping

### Backend
- **Django 4.2** with **Django REST Framework**
- **PostgreSQL** with **PostGIS** for spatial data
- **Redis** for caching and real-time features
- **Django Channels** for WebSocket support
- **Celery** for background tasks
- **JWT Authentication** with refresh tokens

### DevOps & Deployment
- **Docker** + **Docker Compose** for containerization
- **GitHub Actions** for CI/CD
- **Pytest** + **Coverage** for backend testing
- **Vitest** + **Jest** for frontend testing
- **Black** + **ESLint** for code formatting

## 📋 Prerequisites

- **Python 3.11+**
- **Node.js 18+**
- **PostgreSQL 13+** (optional, SQLite works for development)
- **Redis** (for real-time features)

## 🛠️ Installation

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/yourusername/cojourneyhub.git
cd cojourneyhub

# Run complete setup
python setup_complete_app.py
```

### Option 2: Manual Setup

#### Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup database
python manage.py migrate
python populate_locations.py

# Create superuser
python manage.py createsuperuser

# Start Django server
python manage.py runserver
```

#### Frontend Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Configuration

#### Backend (.env in backend/)
```env
SECRET_KEY=your-secret-key
DEBUG=True
DATABASE_URL=sqlite:///db.sqlite3
REDIS_URL=redis://localhost:6379
```

#### Frontend (.env in root)
```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_APP_NAME=CoJourneyHub
```

## 🚀 Development

### Start Development Servers

```bash
# Terminal 1: Django Backend
cd backend
source venv/bin/activate
python manage.py runserver

# Terminal 2: React Frontend
npm run dev

# Terminal 3: Redis (optional, for real-time features)
redis-server
```

### Access Points
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000/api
- **Django Admin**: http://localhost:8000/admin

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py
```

### Individual Test Suites
```bash
# Backend tests
cd backend
python manage.py test
pytest

# Frontend tests
npm test
npm run lint
```

## 📊 Project Structure

```
cojourneyhub/
├── backend/                 # Django backend
│   ├── cojourneyhub/       # Main Django project
│   ├── accounts/           # User management
│   ├── rides/              # Ride management
│   ├── messaging/          # Real-time messaging
│   ├── notifications/      # Notification system
│   ├── reviews/            # Review system
│   ├── locations/          # Location & map services
│   └── tests/              # Backend tests
├── src/                    # React frontend
│   ├── components/         # Reusable components
│   ├── pages/              # Page components
│   ├── services/           # API services
│   ├── contexts/           # React contexts
│   └── lib/                # Utilities
├── docs/                   # Documentation
└── deployment/             # Deployment configs
```

## 🗺️ Map Features (100% Free)

### Included Locations
- **24 Major Cities**: Tunis, Sfax, Sousse, Kairouan, etc.
- **Universities**: INSAT, ESPRIT, University of Tunis, etc.
- **Airports**: Tunis-Carthage, Enfidha-Hammamet, etc.
- **Landmarks**: Sidi Bou Said, Carthage, El Jem, etc.
- **Hospitals**: Charles Nicolle, La Rabta, etc.

### Map Capabilities
- ✅ Interactive maps with zoom/pan
- ✅ Route visualization between points
- ✅ Location markers with popups
- ✅ Distance and duration calculation
- ✅ Mobile-responsive map controls
- ✅ No API keys required

## 🔌 API Endpoints

### Authentication
```
POST /api/auth/register/     # User registration
POST /api/auth/login/        # User login
POST /api/auth/logout/       # User logout
GET  /api/auth/profile/      # Get user profile
```

### Rides
```
GET  /api/rides/             # List active rides
POST /api/rides/             # Create new ride
GET  /api/rides/{id}/        # Get ride details
POST /api/rides/search/      # Advanced ride search
POST /api/rides/{id}/book/   # Book a ride
```

### Locations
```
GET  /api/locations/                    # List locations
GET  /api/locations/autocomplete/       # Location search
POST /api/locations/routes/calculate/   # Calculate route
```

### Messaging
```
GET  /api/messaging/conversations/      # List conversations
POST /api/messaging/conversations/      # Start conversation
WS   /ws/chat/{conversation_id}/        # WebSocket chat
```

## 🚀 Deployment

### Using Docker (Recommended)

```bash
cd backend
docker-compose up --build
```

### Manual Production Deployment

#### Backend
```bash
# Install production dependencies
pip install gunicorn psycopg2-binary

# Collect static files
python manage.py collectstatic

# Run with Gunicorn
gunicorn cojourneyhub.wsgi:application
```

#### Frontend
```bash
# Build for production
npm run build

# Deploy dist/ folder to your web server
```

### Environment Variables for Production

#### Backend
```env
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com
DATABASE_URL=********************************/db
REDIS_URL=redis://redis:6379
```

## 📈 Performance Features

### Backend Optimizations
- **Database Indexing**: Optimized queries with proper indexes
- **Connection Pooling**: Efficient database connections
- **Caching**: Redis caching for frequent queries
- **Query Optimization**: select_related/prefetch_related usage

### Frontend Optimizations
- **Code Splitting**: Lazy loading of routes and components
- **Bundle Optimization**: Tree shaking and minification
- **Image Optimization**: Responsive images and lazy loading
- **Service Worker**: Caching for offline functionality

## 🔒 Security Features

- ✅ **JWT Authentication** with refresh tokens
- ✅ **CORS Protection** with whitelist
- ✅ **Input Validation** with Django serializers
- ✅ **SQL Injection Protection** with ORM
- ✅ **XSS Protection** with Django middleware
- ✅ **CSRF Protection** for state-changing operations
- ✅ **Rate Limiting** for API endpoints
- ✅ **Secure Headers** with Django security middleware

## 🌍 Internationalization

### Supported Languages
- **English** (default)
- **French** (Français)
- **Arabic** (العربية)

### Features
- RTL support for Arabic
- Localized date/time formats
- Currency formatting (TND)
- Location names in multiple languages

## 📱 Mobile Support

### Progressive Web App
- App-like experience in browsers
- Offline functionality
- Push notifications (ready for implementation)
- Home screen installation

### Responsive Design
- Mobile-first approach
- Touch-friendly interfaces
- Optimized for various screen sizes
- Fast loading on mobile networks

## 🔮 Roadmap

### Phase 1 ✅ (Completed)
- Full-stack application with Django + React
- User authentication and profiles
- Ride creation and booking
- Real-time messaging
- Map integration with free OpenStreetMap
- Review and rating system

### Phase 2 🔄 (In Progress)
- Payment integration (Stripe/PayPal)
- SMS notifications
- Advanced route optimization
- Mobile app (React Native)
- Push notifications

### Phase 3 📋 (Planned)
- Driver verification system
- Insurance integration
- Corporate accounts
- Advanced analytics
- Machine learning recommendations

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md).

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `python run_tests.py`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) for details.

## 🆘 Support & Documentation

- 📖 **Setup Guide**: [DJANGO_SETUP_GUIDE.md](DJANGO_SETUP_GUIDE.md)
- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/yourusername/cojourneyhub/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/yourusername/cojourneyhub/discussions)
- 📧 **Email**: <EMAIL>

## 🙏 Acknowledgments

- **Django** and **React** communities for excellent frameworks
- **OpenStreetMap** for free, open-source mapping
- **Tunisia tech community** for inspiration and feedback
- **Contributors** who help make this project better

---

**Made with ❤️ for the Tunisia tech community**

*Ready for production deployment with enterprise-grade features!* 🚀
