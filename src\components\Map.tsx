import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in Leaflet with Webpack
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface MapProps {
  center?: [number, number];
  zoom?: number;
  markers?: Array<{
    position: [number, number];
    popup?: string;
    type?: 'origin' | 'destination' | 'waypoint';
  }>;
  route?: Array<[number, number]>;
  onMapClick?: (lat: number, lng: number) => void;
  className?: string;
  height?: string;
}

const Map: React.FC<MapProps> = ({
  center = [36.8065, 10.1815], // Default to Tunis, Tunisia
  zoom = 7,
  markers = [],
  route = [],
  onMapClick,
  className = '',
  height = '400px'
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);
  const routeRef = useRef<L.Polyline | null>(null);

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Initialize map
    const map = L.map(mapRef.current).setView(center, zoom);

    // Add OpenStreetMap tiles (free)
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 19,
    }).addTo(map);

    // Add click handler if provided
    if (onMapClick) {
      map.on('click', (e) => {
        onMapClick(e.latlng.lat, e.latlng.lng);
      });
    }

    mapInstanceRef.current = map;

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // Update markers
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current?.removeLayer(marker);
    });
    markersRef.current = [];

    // Add new markers
    markers.forEach(({ position, popup, type = 'waypoint' }) => {
      let icon;
      
      // Create different icons for different marker types
      switch (type) {
        case 'origin':
          icon = L.divIcon({
            className: 'custom-marker origin-marker',
            html: '<div style="background-color: #22c55e; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
          });
          break;
        case 'destination':
          icon = L.divIcon({
            className: 'custom-marker destination-marker',
            html: '<div style="background-color: #ef4444; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
          });
          break;
        default:
          icon = undefined; // Use default Leaflet marker
      }

      const marker = L.marker(position, icon ? { icon } : {}).addTo(mapInstanceRef.current!);
      
      if (popup) {
        marker.bindPopup(popup);
      }
      
      markersRef.current.push(marker);
    });
  }, [markers]);

  // Update route
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing route
    if (routeRef.current) {
      mapInstanceRef.current.removeLayer(routeRef.current);
      routeRef.current = null;
    }

    // Add new route
    if (route.length > 1) {
      const polyline = L.polyline(route, {
        color: '#3b82f6',
        weight: 4,
        opacity: 0.8
      }).addTo(mapInstanceRef.current);
      
      routeRef.current = polyline;

      // Fit map to route bounds
      mapInstanceRef.current.fitBounds(polyline.getBounds(), {
        padding: [20, 20]
      });
    }
  }, [route]);

  // Update center and zoom
  useEffect(() => {
    if (!mapInstanceRef.current) return;
    mapInstanceRef.current.setView(center, zoom);
  }, [center, zoom]);

  return (
    <div 
      ref={mapRef} 
      className={`w-full ${className}`}
      style={{ height }}
    />
  );
};

export default Map;
