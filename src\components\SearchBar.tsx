
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, Users, Search, X } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import LocationSearch from "./LocationSearch";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { rideService, RideSearchParams } from "@/services/rideService";

const SearchBar = ({ onSearch }: { onSearch?: (params: SearchParams) => void }) => {
  const [origin, setOrigin] = useState("");
  const [destination, setDestination] = useState("");
  const [date, setDate] = useState("");
  const [passengers, setPassengers] = useState("1");
  const [isSearching, setIsSearching] = useState(false);
  const [originCoords, setOriginCoords] = useState<{ lat: number; lng: number } | undefined>();
  const [destinationCoords, setDestinationCoords] = useState<{ lat: number; lng: number } | undefined>();
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const { isAuthenticated, setPendingRideRequest } = useUser();
  const isRTL = language === "ar";

  // Load from pending ride request if available
  useEffect(() => {
    const pendingSearch = localStorage.getItem("pendingSearch");
    if (pendingSearch) {
      try {
        const { origin, destination, date, passengers } = JSON.parse(pendingSearch);
        setOrigin(origin || "");
        setDestination(destination || "");
        setDate(date || "");
        setPassengers(passengers || "1");
        localStorage.removeItem("pendingSearch");
      } catch (error) {
        console.error("Error parsing pending search", error);
      }
    }
  }, []);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!origin) {
      toast.error(translations.pleaseEnterStartingPoint);
      return;
    }

    if (!destination) {
      toast.error(translations.pleaseEnterDestination);
      return;
    }

    if (!date) {
      toast.error(translations.pleaseSelectTravelDate);
      return;
    }

    const searchParams: SearchParams = {
      origin,
      destination,
      date,
      passengers: parseInt(passengers),
      originCoords,
      destinationCoords
    };

    if (!isAuthenticated) {
      // Save search parameters
      setPendingRideRequest(searchParams);
      localStorage.setItem("pendingSearch", JSON.stringify(searchParams));

      toast.info(translations.pleaseLoginToSearch || "Please login to search for rides");
      navigate("/login?redirect=search");
      return;
    }

    setIsSearching(true);

    try {
      if (onSearch) {
        // Use the provided onSearch callback
        onSearch(searchParams);
      } else {
        // Perform the search and navigate with results
        const rideSearchParams: RideSearchParams = {
          origin,
          destination,
          departureDate: date,
          passengers: parseInt(passengers)
        };

        const rides = await rideService.searchRides(rideSearchParams);

        // Navigate with search results
        navigate(`/?origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&date=${encodeURIComponent(date)}&passengers=${passengers}`, {
          state: { rides, searchParams }
        });
      }

      toast.success(translations.searchingForRides);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Failed to search for rides. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleClearFilters = async () => {
    // Clear all search fields
    setOrigin("");
    setDestination("");
    setDate("");
    setPassengers("1");
    setOriginCoords(undefined);
    setDestinationCoords(undefined);

    // If onSearch callback is provided, call it with empty params to show all rides
    if (onSearch) {
      try {
        const allRides = await rideService.searchRides({});
        onSearch({
          origin: "",
          destination: "",
          date: "",
          passengers: 1
        });
        toast.success(translations.filtersCleared || "Filters cleared - showing all available rides");
      } catch (error) {
        console.error('Error loading all rides:', error);
        toast.error('Failed to load rides. Please try again.');
      }
    } else {
      // Navigate to home page without search parameters
      navigate('/');
      toast.success(translations.filtersCleared || "Filters cleared - showing all available rides");
    }
  };

  const hasActiveFilters = origin || destination || date || passengers !== "1";

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <form onSubmit={handleSearch}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <LocationSearch
              placeholder={translations.from}
              value={origin}
              onChange={(location, coords) => {
                setOrigin(location);
                setOriginCoords(coords);
              }}
              className="bg-gray-50 dark:bg-gray-700"
            />
          </div>

          <div className="relative">
            <LocationSearch
              placeholder={translations.to}
              value={destination}
              onChange={(location, coords) => {
                setDestination(location);
                setDestinationCoords(coords);
              }}
              className="bg-gray-50 dark:bg-gray-700"
            />
          </div>

          <div className="relative">
            <div className={`absolute inset-y-0 ${isRTL ? 'right-3' : 'left-3'} flex items-center pointer-events-none`}>
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className={`${isRTL ? 'pr-10 text-right' : 'pl-10'} block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary bg-gray-50 dark:bg-gray-700 dark:text-white dark:border-gray-600 py-3 px-4`}
            />
          </div>

          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <div className={`absolute inset-y-0 ${isRTL ? 'right-3' : 'left-3'} flex items-center pointer-events-none`}>
                <Users className="h-5 w-5 text-gray-400" />
              </div>
              <select
                value={passengers}
                onChange={(e) => setPassengers(e.target.value)}
                className={`${isRTL ? 'pr-10 text-right' : 'pl-10'} block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary bg-gray-50 dark:bg-gray-700 dark:text-white dark:border-gray-600 py-3 px-4`}
              >
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <option key={num} value={num}>{num} {num !== 1 ? translations.passengers : translations.passengers.slice(0, -1)}</option>
                ))}
              </select>
            </div>

            <div className="flex gap-2">
              {hasActiveFilters && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClearFilters}
                  className="h-full px-4 flex gap-2 items-center"
                >
                  <X className="h-4 w-4" />
                  <span>{translations.clear || "Clear"}</span>
                </Button>
              )}

              <Button
                type="submit"
                className="h-full px-6 flex gap-2 items-center"
                disabled={isSearching}
              >
                <Search className="h-4 w-4" />
                <span>{isSearching ? 'Searching...' : translations.search}</span>
              </Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export interface SearchParams {
  origin: string;
  destination: string;
  date: string;
  passengers: number;
  originCoords?: { lat: number; lng: number };
  destinationCoords?: { lat: number; lng: number };
}

export default SearchBar;
