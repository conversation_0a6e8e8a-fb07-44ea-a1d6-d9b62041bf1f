
import SimpleContentPage from "@/components/SimpleContentPage";
import { useLanguage } from "@/contexts/LanguageContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ShieldCheck, Users, UserCheck, AlertCircle } from "lucide-react";

const TrustPage = () => {
  const { translations } = useLanguage();
  
  return (
    <SimpleContentPage title={translations.trustTitle || "Trust & Safety"} subtitle="How we build trust in our community">
      <p>At CoSesameHub, trust and safety are fundamental to our mission. We've built our platform with several layers of protection to create a secure environment for all Sesame University students using our services.</p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
        <Card className="hover-lift">
          <CardHeader className="pb-2">
            <ShieldCheck className="h-10 w-10 text-primary mb-4" />
            <CardTitle>Verified University Community</CardTitle>
          </CardHeader>
          <CardContent>
            <p>CoSesameHub is exclusively for Sesame University students. We verify university email addresses during registration to ensure all users are part of our trusted academic community.</p>
          </CardContent>
        </Card>
        
        <Card className="hover-lift">
          <CardHeader className="pb-2">
            <Users className="h-10 w-10 text-primary mb-4" />
            <CardTitle>Community Ratings & Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Our transparent rating system allows users to make informed decisions. After each ride, both drivers and passengers rate their experience, building a history of trustworthiness.</p>
          </CardContent>
        </Card>
        
        <Card className="hover-lift">
          <CardHeader className="pb-2">
            <UserCheck className="h-10 w-10 text-primary mb-4" />
            <CardTitle>Driver Verification</CardTitle>
          </CardHeader>
          <CardContent>
            <p>We verify all driver information, including driver's licenses and vehicle details. Drivers must meet our quality and safety standards to offer rides on the platform.</p>
          </CardContent>
        </Card>
        
        <Card className="hover-lift">
          <CardHeader className="pb-2">
            <AlertCircle className="h-10 w-10 text-primary mb-4" />
            <CardTitle>Secure Reporting System</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Our platform includes a robust reporting system for users to flag any concerns. We promptly investigate all reports and take appropriate action to maintain community standards.</p>
          </CardContent>
        </Card>
      </div>
      
      <h2>Our Trust Principles</h2>
      
      <h3>Transparency</h3>
      <p>We believe in clear communication about how our platform works, how we use your data, and the steps we take to ensure safety. Before each ride, users can see important information about their co-riders, including ratings, verification status, and ride history.</p>
      
      <h3>Accountability</h3>
      <p>We hold ourselves and our users accountable for maintaining a respectful and safe environment. Users who violate our community guidelines may lose access to the platform. We regularly review and update our policies based on user feedback and evolving best practices.</p>
      
      <h3>Privacy Protection</h3>
      <p>We're committed to protecting your personal information. We only share the data necessary to facilitate rides, and we implement robust security measures to prevent unauthorized access. For complete details, please review our Privacy Policy.</p>
      
      <h3>Continuous Improvement</h3>
      <p>We constantly work to enhance our safety features and processes. We analyze patterns, listen to user feedback, and stay updated on industry standards to provide the most secure ridesharing experience possible.</p>
      
      <h2>Before, During, and After the Ride</h2>
      
      <h3>Before the Ride</h3>
      <p>Users can view profiles, ratings, and ride history before accepting a ride. We encourage users to communicate through our platform to clarify pickup locations and any special requirements.</p>
      
      <h3>During the Ride</h3>
      <p>Our platform includes features that allow users to share their ride details with friends or family. We also provide emergency assistance access directly through the app.</p>
      
      <h3>After the Ride</h3>
      <p>Users rate and review their experience, contributing to our community trust system. Any issues can be reported immediately through our reporting system.</p>
      
      <div className="bg-primary/10 p-6 rounded-lg mt-8">
        <h3 className="text-xl font-semibold mb-2">Our Commitment to You</h3>
        <p>We are dedicated to creating a trusted platform where Sesame University students can safely share rides. If you ever have concerns about safety or observe behavior that violates our community guidelines, please contact us immediately. Your safety is our priority.</p>
      </div>
    </SimpleContentPage>
  );
};

export default TrustPage;
