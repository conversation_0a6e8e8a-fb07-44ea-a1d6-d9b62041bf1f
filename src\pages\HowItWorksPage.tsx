
import { CheckCircle } from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const HowItWorksPage = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-1">
        <div className="bg-primary text-white py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-4xl font-bold mb-4">How CoJourneyHub Works</h1>
              <p className="text-xl opacity-90">
                Simple, affordable, and safe ridesharing for Sesame University students
              </p>
            </div>
          </div>
        </div>
        
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <div className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="inline-flex items-center justify-center h-14 w-14 rounded-full bg-primary/10 text-primary mb-4">
                  <span className="text-2xl font-bold">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Search for a Ride</h3>
                <p className="text-gray-600">
                  Enter your origin, destination and travel date to find available rides offered by fellow students.
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="inline-flex items-center justify-center h-14 w-14 rounded-full bg-primary/10 text-primary mb-4">
                  <span className="text-2xl font-bold">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Book Your Seat</h3>
                <p className="text-gray-600">
                  Choose the ride that works best for you, check driver details, and book your seat in just a few clicks.
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="inline-flex items-center justify-center h-14 w-14 rounded-full bg-primary/10 text-primary mb-4">
                  <span className="text-2xl font-bold">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Enjoy Your Trip</h3>
                <p className="text-gray-600">
                  Meet your driver at the pickup point, enjoy your journey, and arrive at your destination safely.
                </p>
              </div>
            </div>
            
            <div className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-center">Why Choose CoJourneyHub?</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                  <div>
                    <h4 className="text-lg font-semibold mb-1">Exclusive to Sesame University</h4>
                    <p className="text-gray-600">
                      Connect only with fellow students, ensuring a safe and trusted community.
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                  <div>
                    <h4 className="text-lg font-semibold mb-1">Save Money</h4>
                    <p className="text-gray-600">
                      Split travel costs and reduce expenses on your regular commutes to campus.
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                  <div>
                    <h4 className="text-lg font-semibold mb-1">Eco-Friendly</h4>
                    <p className="text-gray-600">
                      Reduce carbon footprint by sharing rides and having fewer cars on the road.
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                  <div>
                    <h4 className="text-lg font-semibold mb-1">Build Connections</h4>
                    <p className="text-gray-600">
                      Network with students from different programs and make new friends.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-8 rounded-lg">
              <h2 className="text-2xl font-bold mb-4 text-center">Ready to Get Started?</h2>
              <p className="text-center text-gray-600 mb-6">
                Join CoJourneyHub today and transform your commute to and from Sesame University.
              </p>
              <div className="flex justify-center gap-4">
                <a href="/" className="bg-primary text-white px-6 py-2 rounded-md font-medium hover:bg-primary/90 transition-colors">
                  Find a Ride
                </a>
                <a href="/offer-ride" className="bg-white border border-gray-300 px-6 py-2 rounded-md font-medium hover:bg-gray-50 transition-colors">
                  Offer a Ride
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default HowItWorksPage;
