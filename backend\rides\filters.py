import django_filters
from django.db.models import Q
from .models import Ride


class RideFilter(django_filters.FilterSet):
    """Filter class for ride search"""
    
    origin = django_filters.CharFilter(lookup_expr='icontains')
    destination = django_filters.CharFilter(lookup_expr='icontains')
    departure_date = django_filters.DateFilter(field_name='departure_time__date')
    departure_time_from = django_filters.TimeFilter(field_name='departure_time__time', lookup_expr='gte')
    departure_time_to = django_filters.TimeFilter(field_name='departure_time__time', lookup_expr='lte')
    max_price = django_filters.NumberFilter(field_name='price', lookup_expr='lte')
    min_seats = django_filters.NumberFilter(field_name='seats_available', lookup_expr='gte')
    
    # Custom filter for features
    features = django_filters.CharFilter(method='filter_features')
    
    class Meta:
        model = Ride
        fields = [
            'origin', 'destination', 'departure_date',
            'departure_time_from', 'departure_time_to',
            'max_price', 'min_seats', 'features'
        ]
    
    def filter_features(self, queryset, name, value):
        """Filter rides by features"""
        if value:
            features_list = value.split(',')
            for feature in features_list:
                queryset = queryset.filter(features__contains=[feature.strip()])
        return queryset
