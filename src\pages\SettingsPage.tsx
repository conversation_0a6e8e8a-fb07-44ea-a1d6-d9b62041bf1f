import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import EmailChangeForm from "@/components/EmailChangeForm";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Hover<PERSON>ard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { Upload, Info, Shield, Bell, Globe, Moon, Phone } from "lucide-react";

const SettingsPage = () => {
  const { user, setUser } = useUser();
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  
  const [profilePicture, setProfilePicture] = useState(
    user?.profilePicture || `https://ui-avatars.com/api/?name=${user?.fullName || user?.email}&background=0D8ABC&color=fff`
  );
  
  const [settings, setSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    darkMode: false,
    language: language,
    twoFactorAuth: false
  });

  if (!user) {
    navigate("/login");
    return null;
  }

  const getInitials = (name: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map(part => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSettingChange = (setting: string, value: boolean | string) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
    
    toast.success(`${setting} ${value ? "enabled" : "disabled"}`);
  };

  const handleProfilePictureChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        const newProfilePicture = reader.result as string;
        setProfilePicture(newProfilePicture);
        
        if (user) {
          setUser({
            ...user,
            profilePicture: newProfilePicture
          });
          toast.success(translations.profilePictureUpdated || "Profile picture updated");
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePasswordChange = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success(translations.passwordUpdated || "Password updated successfully");
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-1 bg-gray-50 dark:bg-gray-900 py-12">
        <div className="container mx-auto px-4 max-w-5xl">
          <h1 className="text-3xl font-bold mb-6">
            {translations.settings || "Settings"}
          </h1>
          
          <Tabs defaultValue="account" className="w-full">
            <div className="flex flex-col md:flex-row gap-6">
              <div className="md:w-64">
                <TabsList className="flex flex-col h-auto bg-transparent p-0 justify-start">
                  <TabsTrigger 
                    value="account" 
                    className="justify-start mb-1 data-[state=active]:bg-gray-100 dark:data-[state=active]:bg-gray-800"
                  >
                    {translations.accountSettings || "Account Settings"}
                  </TabsTrigger>
                  <TabsTrigger 
                    value="security" 
                    className="justify-start mb-1 data-[state=active]:bg-gray-100 dark:data-[state=active]:bg-gray-800"
                  >
                    {translations.securityPrivacy || "Security & Privacy"}
                  </TabsTrigger>
                  <TabsTrigger 
                    value="notifications" 
                    className="justify-start mb-1 data-[state=active]:bg-gray-100 dark:data-[state=active]:bg-gray-800"
                  >
                    {translations.notifications || "Notifications"}
                  </TabsTrigger>
                  <TabsTrigger 
                    value="appearance" 
                    className="justify-start mb-1 data-[state=active]:bg-gray-100 dark:data-[state=active]:bg-gray-800"
                  >
                    {translations.appearance || "Appearance"}
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <div className="flex-1">
                <TabsContent value="account" className="mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.profilePicture || "Profile Picture"}</CardTitle>
                      <CardDescription>
                        {translations.updateYourProfilePicture || "Update your profile picture"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <Avatar className="w-20 h-20 border">
                          <AvatarImage src={profilePicture} />
                          <AvatarFallback>{getInitials(user.fullName || user.email)}</AvatarFallback>
                        </Avatar>
                        
                        <div>
                          <Label 
                            htmlFor="picture" 
                            className="cursor-pointer inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                          >
                            <Upload className="h-4 w-4" />
                            {translations.uploadImage || "Upload Image"}
                          </Label>
                          <Input 
                            id="picture" 
                            type="file" 
                            accept="image/*" 
                            className="hidden" 
                            onChange={handleProfilePictureChange}
                          />
                          <p className="text-sm text-gray-500 mt-2">
                            {translations.recommendedSize || "Recommended size: 300x300px"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <div className="mt-6">
                    <EmailChangeForm />
                  </div>
                  
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>{translations.changePassword || "Change Password"}</CardTitle>
                      <CardDescription>
                        {translations.updateYourPassword || "Update your password"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handlePasswordChange} className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="currentPassword">
                            {translations.currentPassword || "Current Password"}
                          </Label>
                          <Input 
                            id="currentPassword" 
                            type="password" 
                            placeholder="••••••••" 
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="newPassword">
                            {translations.newPassword || "New Password"}
                          </Label>
                          <Input 
                            id="newPassword" 
                            type="password" 
                            placeholder="••••••••" 
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword">
                            {translations.confirmPassword || "Confirm Password"}
                          </Label>
                          <Input 
                            id="confirmPassword" 
                            type="password" 
                            placeholder="••••••••" 
                          />
                        </div>
                        
                        <Button type="submit">
                          {translations.updatePassword || "Update Password"}
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                  
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>{translations.phoneNumber || "Phone Number"}</CardTitle>
                      <CardDescription>
                        {translations.updateYourPhoneNumber || "Update your phone number for account recovery and notifications"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <Phone className="h-5 w-5 text-gray-500" />
                        <div className="flex-1">
                          <p className="font-medium">
                            {user.phoneNumber || translations.noPhoneNumber || "No phone number added"}
                          </p>
                        </div>
                        <Button variant="outline">
                          {user.phoneNumber ? (translations.update || "Update") : (translations.add || "Add")}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="security" className="mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.securitySettings || "Security Settings"}</CardTitle>
                      <CardDescription>
                        {translations.manageSecuritySettings || "Manage your security settings and preferences"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Label htmlFor="twoFactor">{translations.twoFactorAuthentication || "Two-Factor Authentication"}</Label>
                            <HoverCard>
                              <HoverCardTrigger>
                                <Info className="h-4 w-4 text-gray-500" />
                              </HoverCardTrigger>
                              <HoverCardContent>
                                {translations.twoFactorAuthDescription || "Adds an extra layer of security to your account by requiring a verification code along with your password."}
                              </HoverCardContent>
                            </HoverCard>
                          </div>
                          <p className="text-sm text-gray-500">
                            {translations.enhanceYourAccountSecurity || "Enhance your account security with 2FA"}
                          </p>
                        </div>
                        <Switch 
                          id="twoFactor" 
                          checked={settings.twoFactorAuth}
                          onCheckedChange={(checked) => handleSettingChange('twoFactorAuth', checked)}
                        />
                      </div>
                      
                      <Separator />
                      
                      <div>
                        <h3 className="font-medium mb-2 flex items-center gap-2">
                          <Shield className="h-5 w-5 text-primary" />
                          {translations.loginActivity || "Login Activity"}
                        </h3>
                        <p className="text-sm text-gray-500 mb-4">
                          {translations.monitorRecentLogins || "Monitor recent login activity on your account"}
                        </p>
                        <Button variant="outline">
                          {translations.viewLoginHistory || "View Login History"}
                        </Button>
                      </div>
                      
                      <Separator />
                      
                      <div>
                        <h3 className="font-medium mb-2 text-red-600">
                          {translations.dangerZone || "Danger Zone"}
                        </h3>
                        <p className="text-sm text-gray-500 mb-4">
                          {translations.irreversibleActions || "These actions are irreversible"}
                        </p>
                        <Button variant="destructive">
                          {translations.deleteAccount || "Delete Account"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="notifications" className="mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.notificationPreferences || "Notification Preferences"}</CardTitle>
                      <CardDescription>
                        {translations.manageHowYouReceiveNotifications || "Manage how you receive notifications"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="emailNotifications">
                            {translations.emailNotifications || "Email Notifications"}
                          </Label>
                          <p className="text-sm text-gray-500">
                            {translations.receiveEmailsAbout || "Receive emails about your account activity"}
                          </p>
                        </div>
                        <Switch 
                          id="emailNotifications" 
                          checked={settings.emailNotifications}
                          onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                        />
                      </div>
                      
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="smsNotifications">
                            {translations.smsNotifications || "SMS Notifications"}
                          </Label>
                          <p className="text-sm text-gray-500">
                            {translations.receiveTextMessages || "Receive text messages for important updates"}
                          </p>
                        </div>
                        <Switch 
                          id="smsNotifications" 
                          checked={settings.smsNotifications}
                          onCheckedChange={(checked) => handleSettingChange('smsNotifications', checked)}
                        />
                      </div>
                      
                      <Separator />
                      
                      <div>
                        <h3 className="font-medium mb-2 flex items-center gap-2">
                          <Bell className="h-5 w-5 text-primary" />
                          {translations.notificationCategories || "Notification Categories"}
                        </h3>
                        <div className="space-y-3 mt-4">
                          <div className="flex items-center space-x-2">
                            <Switch id="rideupdates" checked />
                            <Label htmlFor="rideupdates">
                              {translations.rideUpdates || "Ride Updates"}
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch id="messages" checked />
                            <Label htmlFor="messages">
                              {translations.messages || "Messages"}
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch id="promotions" />
                            <Label htmlFor="promotions">
                              {translations.promotions || "Promotions"}
                            </Label>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="appearance" className="mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.appearanceSettings || "Appearance Settings"}</CardTitle>
                      <CardDescription>
                        {translations.customizeYourExperience || "Customize your experience"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Label htmlFor="darkMode">
                              {translations.darkMode || "Dark Mode"}
                            </Label>
                            <Moon className="h-4 w-4 text-gray-500" />
                          </div>
                          <p className="text-sm text-gray-500">
                            {translations.toggleDarkMode || "Toggle between light and dark mode"}
                          </p>
                        </div>
                        <Switch 
                          id="darkMode" 
                          checked={settings.darkMode}
                          onCheckedChange={(checked) => handleSettingChange('darkMode', checked)}
                        />
                      </div>
                      
                      <Separator />
                      
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <Globe className="h-5 w-5 text-primary" />
                          <h3 className="font-medium">
                            {translations.language || "Language"}
                          </h3>
                        </div>
                        <p className="text-sm text-gray-500 mb-4">
                          {translations.chooseYourPreferredLanguage || "Choose your preferred language"}
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          <Button 
                            variant={settings.language === "en" ? "default" : "outline"}
                            onClick={() => handleSettingChange('language', 'en')}
                            className="justify-start"
                          >
                            English
                          </Button>
                          <Button 
                            variant={settings.language === "ar" ? "default" : "outline"}
                            onClick={() => handleSettingChange('language', 'ar')}
                            className="justify-start"
                          >
                            العربية
                          </Button>
                          <Button 
                            variant={settings.language === "fr" ? "default" : "outline"}
                            onClick={() => handleSettingChange('language', 'fr')}
                            className="justify-start"
                          >
                            Français
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </div>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default SettingsPage;
