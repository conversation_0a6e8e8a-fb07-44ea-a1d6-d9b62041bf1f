# Generated by Django 4.2.7 on 2025-05-27 17:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TunisiaLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('name_ar', models.CharField(blank=True, max_length=255)),
                ('name_fr', models.CharField(blank=True, max_length=255)),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('location_type', models.CharField(choices=[('city', 'City'), ('university', 'University'), ('hospital', 'Hospital'), ('airport', 'Airport'), ('landmark', 'Landmark'), ('station', 'Station'), ('mall', 'Shopping Mall'), ('hotel', 'Hotel')], default='city', max_length=20)),
                ('governorate', models.CharField(blank=True, max_length=100)),
                ('delegation', models.CharField(blank=True, max_length=100)),
                ('population', models.PositiveIntegerField(blank=True, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=10)),
                ('is_popular', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'tunisia_locations',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='tunisia_loc_name_db9fad_idx'), models.Index(fields=['location_type'], name='tunisia_loc_locatio_381af7_idx'), models.Index(fields=['governorate'], name='tunisia_loc_governo_86647b_idx'), models.Index(fields=['is_popular'], name='tunisia_loc_is_popu_95485e_idx')],
            },
        ),
        migrations.CreateModel(
            name='LocationSearch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=255)),
                ('result_count', models.PositiveIntegerField(default=0)),
                ('user_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('selected_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='locations.tunisialocation')),
            ],
            options={
                'db_table': 'location_searches',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Route',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('distance_km', models.PositiveIntegerField()),
                ('estimated_duration_minutes', models.PositiveIntegerField()),
                ('route_coordinates', models.JSONField(blank=True, null=True)),
                ('search_count', models.PositiveIntegerField(default=0)),
                ('is_popular', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('destination', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes_to', to='locations.tunisialocation')),
                ('origin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes_from', to='locations.tunisialocation')),
            ],
            options={
                'db_table': 'routes',
                'ordering': ['-search_count'],
                'unique_together': {('origin', 'destination')},
            },
        ),
    ]
