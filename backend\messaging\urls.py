from django.urls import path
from . import views

urlpatterns = [
    # Conversations
    path('conversations/', views.ConversationListCreateView.as_view(), name='conversation-list-create'),
    path('conversations/<int:pk>/', views.ConversationDetailView.as_view(), name='conversation-detail'),
    path('conversations/<int:conversation_id>/messages/', views.MessageListCreateView.as_view(), name='message-list-create'),
    path('conversations/<int:conversation_id>/mark-read/', views.mark_conversation_read, name='mark-conversation-read'),
    path('conversations/<int:conversation_id>/delete/', views.delete_conversation, name='delete-conversation'),

    # Start conversation with user
    path('start/<int:user_id>/', views.start_conversation, name='start-conversation'),
    path('ride/<int:ride_id>/', views.start_ride_conversation, name='start-ride-conversation'),

    # Unread count
    path('unread-count/', views.unread_count, name='unread-count'),

    # Blocking
    path('blocked/', views.BlockedUserListView.as_view(), name='blocked-users'),
    path('block/', views.block_user, name='block-user'),
    path('unblock/<int:user_id>/', views.unblock_user, name='unblock-user'),
]
