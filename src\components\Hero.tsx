
import { Button } from "@/components/ui/button";
import { Car } from "lucide-react";
import { Link } from "react-router-dom";
import { useLanguage } from "@/contexts/LanguageContext";

const Hero = () => {
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";

  return (
    <div className="relative bg-gradient-to-br from-primary/90 to-primary px-6 py-16 md:py-24">
      <div className="container mx-auto">
        <div className={`max-w-2xl ${isRTL ? 'mr-auto text-right' : 'ml-0'}`}>
          <div className={`flex items-center gap-2 text-white mb-6 ${isRTL ? 'justify-end' : 'justify-start'}`}>
            <img 
              src="/sesame.jpg" 
              alt="Sesame University" 
              className="h-12 mr-2 bg-white p-1 rounded" 
            />
            <Car className="h-12 w-12" />
            <span className="font-bold text-3xl">CoSesameHub</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in">
            {translations.travelTogether}
          </h1>
          
          <p className="text-lg text-white/90 mb-8 animate-fade-in animation-delay-150">
            {translations.travelDescription}
          </p>
          
          <div className={`flex flex-wrap gap-4 ${isRTL ? 'justify-end' : 'justify-start'} animate-fade-in animation-delay-300`}>
            <Button 
              size="lg" 
              variant="default" 
              className="bg-white text-primary hover:bg-gray-100 transition-transform hover:scale-105" 
              asChild
            >
              <Link to="/">{translations.findRide}</Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="text-white border-white hover:bg-white/10 hover:text-white transition-transform hover:scale-105"
              asChild
            >
              <Link to="/offer-ride">{translations.offerRide}</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
