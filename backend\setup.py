#!/usr/bin/env python
"""
Setup script for CoJourneyHub Django Backend
Run this script to set up the development environment
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def setup_django():
    """Set up Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cojourneyhub.settings')
    django.setup()

def main():
    """Main setup function"""
    print("🚀 Setting up CoJourneyHub Django Backend")
    print("=" * 50)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. It's recommended to use a virtual environment.")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled. Please activate a virtual environment and try again.")
            return
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        return
    
    # Setup Django
    setup_django()
    
    # Run migrations
    print("\n📊 Setting up database...")
    try:
        execute_from_command_line(['manage.py', 'makemigrations'])
        execute_from_command_line(['manage.py', 'migrate'])
        print("✓ Database migrations completed")
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return
    
    # Populate locations
    print("\n🗺️  Populating location data...")
    try:
        exec(open('populate_locations.py').read())
        print("✓ Location data populated")
    except Exception as e:
        print(f"✗ Location population failed: {e}")
        print("You can run 'python populate_locations.py' manually later")
    
    # Create superuser
    print("\n👤 Creating superuser...")
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if not User.objects.filter(is_superuser=True).exists():
            print("Please create a superuser account:")
            execute_from_command_line(['manage.py', 'createsuperuser'])
        else:
            print("✓ Superuser already exists")
    except Exception as e:
        print(f"✗ Superuser creation failed: {e}")
        print("You can create a superuser later with: python manage.py createsuperuser")
    
    # Collect static files (for production)
    if os.environ.get('DJANGO_SETTINGS_MODULE') != 'cojourneyhub.settings':
        print("\n📁 Collecting static files...")
        try:
            execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
            print("✓ Static files collected")
        except Exception as e:
            print(f"✗ Static file collection failed: {e}")
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Update the .env file with your configuration")
    print("2. Start the development server: python manage.py runserver")
    print("3. Visit http://127.0.0.1:8000/admin/ to access the admin panel")
    print("4. API documentation will be available at http://127.0.0.1:8000/api/")
    print("\nFor production deployment:")
    print("1. Set DEBUG=False in .env")
    print("2. Configure PostgreSQL database")
    print("3. Set up Redis for Celery and Channels")
    print("4. Configure email settings")

if __name__ == '__main__':
    main()
