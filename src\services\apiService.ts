const API_BASE_URL = 'http://localhost:8000/api';

interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

class ApiService {
  private async makeRequest<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    // Get CSRF token from cookies
    const getCsrfToken = () => {
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
          return value;
        }
      }
      return null;
    };

    const csrfToken = getCsrfToken();

    const defaultOptions: RequestInit = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...(csrfToken && { 'X-CSRFToken': csrfToken }),
        ...options.headers,
      },
    };

    const response = await fetch(url, { ...defaultOptions, ...options });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || errorData.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Authentication
  async login(email: string, password: string) {
    return this.makeRequest('/auth/login/', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async logout() {
    return this.makeRequest('/auth/logout/', {
      method: 'POST',
    });
  }

  async registerDriver(data: any) {
    return this.makeRequest('/auth/register/driver/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async registerPassenger(data: any) {
    return this.makeRequest('/auth/register/passenger/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // User Profile
  async getCurrentUser() {
    return this.makeRequest('/auth/user/');
  }

  async updateProfile(data: any) {
    return this.makeRequest('/auth/user/', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  // Rides
  async getRides(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.makeRequest(`/rides/${queryString}`);
  }

  async getRide(rideId: string) {
    return this.makeRequest(`/rides/${rideId}/`);
  }

  async createRide(data: any) {
    return this.makeRequest('/rides/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateRide(rideId: string, data: any) {
    return this.makeRequest(`/rides/${rideId}/`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteRide(rideId: string) {
    return this.makeRequest(`/rides/${rideId}/`, {
      method: 'DELETE',
    });
  }

  async searchRides(searchData: any) {
    return this.makeRequest('/rides/search/', {
      method: 'POST',
      body: JSON.stringify(searchData),
    });
  }

  // Bookings
  async getBookings() {
    return this.makeRequest('/rides/bookings/');
  }

  async createBooking(rideId: string, bookingData: any) {
    return this.makeRequest(`/rides/${rideId}/book/`, {
      method: 'POST',
      body: JSON.stringify(bookingData),
    });
  }

  async cancelBooking(bookingId: string) {
    return this.makeRequest(`/rides/bookings/${bookingId}/cancel/`, {
      method: 'POST',
    });
  }

  // Pending Bookings
  async getPendingBookings() {
    return this.makeRequest('/rides/pending/');
  }

  async requestBooking(rideId: string, bookingData: any) {
    return this.makeRequest(`/rides/${rideId}/request-booking/`, {
      method: 'POST',
      body: JSON.stringify(bookingData),
    });
  }

  async respondToPendingBooking(pendingId: string, action: 'confirm' | 'reject', response?: string) {
    return this.makeRequest(`/rides/pending/${pendingId}/respond/`, {
      method: 'POST',
      body: JSON.stringify({
        action,
        driver_response: response || '',
      }),
    });
  }

  // Vehicles
  async getVehicles() {
    return this.makeRequest('/auth/vehicles/');
  }

  async createVehicle(vehicleData: any) {
    return this.makeRequest('/auth/vehicles/', {
      method: 'POST',
      body: JSON.stringify(vehicleData),
    });
  }

  async updateVehicle(vehicleId: string, vehicleData: any) {
    return this.makeRequest(`/auth/vehicles/${vehicleId}/`, {
      method: 'PATCH',
      body: JSON.stringify(vehicleData),
    });
  }

  async deleteVehicle(vehicleId: string) {
    return this.makeRequest(`/auth/vehicles/${vehicleId}/`, {
      method: 'DELETE',
    });
  }

  // Notifications
  async getNotifications() {
    return this.makeRequest('/notifications/');
  }

  async markNotificationAsRead(notificationId: string) {
    return this.makeRequest(`/notifications/${notificationId}/mark-read/`, {
      method: 'POST',
    });
  }

  async markAllNotificationsAsRead() {
    return this.makeRequest('/notifications/mark-all-read/', {
      method: 'POST',
    });
  }

  // Reviews and Ratings
  async createReview(data: any) {
    return this.makeRequest('/reviews/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getReviews(userId?: string) {
    const queryString = userId ? `?user=${userId}` : '';
    return this.makeRequest(`/reviews/${queryString}`);
  }

  // Statistics
  async getUserStats() {
    return this.makeRequest('/auth/stats/');
  }

  async getRideStats() {
    return this.makeRequest('/rides/stats/');
  }

  // Messages
  async getConversations() {
    return this.makeRequest('/messages/conversations/');
  }

  async getMessages(conversationId: string) {
    return this.makeRequest(`/messages/conversations/${conversationId}/messages/`);
  }

  async sendMessage(conversationId: string, content: string) {
    return this.makeRequest(`/messages/conversations/${conversationId}/messages/`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    });
  }

  // File uploads
  async uploadFile(file: File, endpoint: string = '/upload/') {
    const formData = new FormData();
    formData.append('file', file);

    return this.makeRequest(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it with boundary
    });
  }

  // Utility methods
  async healthCheck() {
    return this.makeRequest('/health/');
  }

  // WebSocket connection helper
  getWebSocketUrl(endpoint: string): string {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = 'localhost:8000'; // Change this for production
    return `${wsProtocol}//${wsHost}/ws${endpoint}`;
  }
}

export const apiService = new ApiService();
export default apiService;
