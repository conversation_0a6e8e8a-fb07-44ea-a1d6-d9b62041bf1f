from django.urls import path
from . import views

urlpatterns = [
    # Rides
    path('', views.RideListCreateView.as_view(), name='ride-list-create'),
    path('<int:pk>/', views.RideDetailView.as_view(), name='ride-detail'),
    path('search/', views.search_rides, name='ride-search'),
    path('my-rides/', views.UserRidesView.as_view(), name='user-rides'),
    path('nearby/', views.nearby_rides, name='nearby-rides'),
    path('stats/', views.ride_stats, name='ride-stats'),
    
    # Bookings
    path('bookings/', views.RideBookingListCreateView.as_view(), name='booking-list-create'),
    path('bookings/<int:pk>/', views.RideBookingDetailView.as_view(), name='booking-detail'),
    path('<int:ride_id>/book/', views.book_ride, name='book-ride'),
    path('bookings/<int:booking_id>/cancel/', views.cancel_booking, name='cancel-booking'),
    
    # Ride requests
    path('requests/', views.RideRequestListCreateView.as_view(), name='ride-request-list-create'),
]
