
// Django API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

// API Client for Django Backend
class APIClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('access_token');
  }

  setToken(token: string) {
    this.token = token;
    localStorage.setItem('access_token', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (response.status === 401) {
        // Token expired, try to refresh
        const refreshed = await this.refreshToken();
        if (refreshed) {
          // Retry the request with new token
          headers.Authorization = `Bearer ${this.token}`;
          const retryResponse = await fetch(url, {
            ...options,
            headers,
          });
          return retryResponse;
        } else {
          // Refresh failed, clear tokens and redirect to login
          this.clearToken();
          window.location.href = '/login';
          throw new Error('Authentication failed');
        }
      }

      return response;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async refreshToken(): Promise<boolean> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) return false;

    try {
      const response = await fetch(`${this.baseURL}/token/refresh/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        this.setToken(data.access);
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    return false;
  }

  async get(endpoint: string) {
    const response = await this.request(endpoint);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  async post(endpoint: string, data: any) {
    const response = await this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorText = await response.text();
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }

      const error = new Error(`HTTP error! status: ${response.status}`);
      (error as any).response = { data: errorData, status: response.status };
      throw error;
    }
    return response.json();
  }

  async put(endpoint: string, data: any) {
    const response = await this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async patch(endpoint: string, data: any) {
    const response = await this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async delete(endpoint: string) {
    const response = await this.request(endpoint, {
      method: 'DELETE',
    });
    return response.ok;
  }
}

export const apiClient = new APIClient(API_BASE_URL);

// Legacy Supabase compatibility layer for existing code
export const supabase = {
  auth: {
    getSession: async () => {
      const user = localStorage.getItem('user');
      const isAuthenticated = localStorage.getItem('isAuthenticated');

      if (user && isAuthenticated === 'true') {
        return {
          data: {
            session: {
              user: JSON.parse(user)
            }
          },
          error: null
        };
      }

      return { data: { session: null }, error: null };
    },

    onAuthStateChange: (callback: any) => {
      // Simple implementation - in a real app you'd want proper event handling
      const checkAuth = () => {
        const user = localStorage.getItem('user');
        const isAuthenticated = localStorage.getItem('isAuthenticated');

        if (user && isAuthenticated === 'true') {
          callback('SIGNED_IN', { user: JSON.parse(user) });
        } else {
          callback('SIGNED_OUT', null);
        }
      };

      // Check immediately
      checkAuth();

      // Listen for storage changes
      window.addEventListener('storage', checkAuth);

      return {
        data: {
          subscription: {
            unsubscribe: () => window.removeEventListener('storage', checkAuth)
          }
        }
      };
    },

    signOut: async () => {
      try {
        await apiClient.post('/auth/logout/', {});
      } catch (error) {
        console.error('Logout error:', error);
      }

      localStorage.removeItem('user');
      localStorage.removeItem('isAuthenticated');
      return { error: null };
    },

    signUp: async (credentials: { email: string; password: string; options?: any }) => {
      try {
        const response = await apiClient.post('/auth/register/', {
          email: credentials.email,
          password: credentials.password,
          password_confirm: credentials.password,
          username: credentials.email.split('@')[0],
          full_name: credentials.options?.data?.full_name || '',
          city: credentials.options?.data?.city || '',
          ...credentials.options?.data
        });

        // Store user data
        localStorage.setItem('user', JSON.stringify(response.user));
        localStorage.setItem('isAuthenticated', 'true');

        return {
          data: { user: response.user, session: { user: response.user } },
          error: null
        };
      } catch (error: any) {
        console.error('Registration error:', error);
        return {
          data: { user: null, session: null },
          error: { message: error.message || 'Registration failed' }
        };
      }
    },

    signInWithPassword: async (credentials: { email: string; password: string }) => {
      // This method is deprecated - use useAuth hook instead
      console.warn('supabase.auth.signInWithPassword is deprecated. Use useAuth hook instead.');
      return {
        data: { user: null, session: null },
        error: { message: 'Use useAuth hook for authentication' }
      };
    }
  }
};

export const isSupabaseConfigured = () => true; // Always true for Django backend
