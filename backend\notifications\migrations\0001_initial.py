# Generated by Django 4.2.7 on 2025-05-27 17:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rides', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_ride_booked', models.BooleanField(default=True)),
                ('email_ride_cancelled', models.BooleanField(default=True)),
                ('email_new_message', models.BooleanField(default=True)),
                ('email_ride_reminder', models.BooleanField(default=True)),
                ('email_payment_received', models.BooleanField(default=True)),
                ('email_review_received', models.BooleanField(default=True)),
                ('email_system', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('push_ride_booked', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('push_ride_cancelled', models.<PERSON>oleanField(default=True)),
                ('push_new_message', models.BooleanField(default=True)),
                ('push_ride_reminder', models.BooleanField(default=True)),
                ('push_payment_received', models.BooleanField(default=True)),
                ('push_review_received', models.BooleanField(default=True)),
                ('push_system', models.BooleanField(default=True)),
                ('sms_ride_reminder', models.BooleanField(default=False)),
                ('sms_ride_cancelled', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'notification_preferences',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('notification_type', models.CharField(choices=[('ride_booked', 'Ride Booked'), ('ride_cancelled', 'Ride Cancelled'), ('booking_cancelled', 'Booking Cancelled'), ('new_message', 'New Message'), ('ride_reminder', 'Ride Reminder'), ('payment_received', 'Payment Received'), ('review_received', 'Review Received'), ('system', 'System Notification')], max_length=20)),
                ('action_url', models.CharField(blank=True, max_length=255)),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('related_booking', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='rides.ridebooking')),
                ('related_ride', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='rides.ride')),
                ('related_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications_about', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='notificatio_user_id_611c58_idx'), models.Index(fields=['is_read'], name='notificatio_is_read_3f8c44_idx'), models.Index(fields=['notification_type'], name='notificatio_notific_19df93_idx')],
            },
        ),
    ]
