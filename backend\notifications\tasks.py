from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from .models import Notification
from django.contrib.auth import get_user_model

User = get_user_model()


@shared_task
def send_notification_email(user_id, title, content, notification_type='system'):
    """Send email notification to user"""
    try:
        user = User.objects.get(id=user_id)
        
        if not user.email_notifications:
            return "User has disabled email notifications"
        
        # Create email content
        html_message = render_to_string('notifications/email_notification.html', {
            'user': user,
            'title': title,
            'content': content,
            'notification_type': notification_type,
        })
        plain_message = strip_tags(html_message)
        
        send_mail(
            subject=f"CoJourneyHub - {title}",
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        return f"Email sent to {user.email}"
    except User.DoesNotExist:
        return f"User with id {user_id} not found"
    except Exception as e:
        return f"Failed to send email: {str(e)}"


@shared_task
def send_ride_booking_notification(driver_id, passenger_name, ride_origin, ride_destination):
    """Send notification to driver about new booking request"""
    try:
        driver = User.objects.get(id=driver_id)
        
        # Create notification in database
        Notification.objects.create(
            user=driver,
            title="New Ride Booking Request",
            content=f"{passenger_name} wants to book your ride from {ride_origin} to {ride_destination}",
            notification_type='ride_booked'
        )
        
        # Send email notification
        if driver.email_notifications:
            send_notification_email.delay(
                driver_id,
                "New Ride Booking Request",
                f"{passenger_name} wants to book your ride from {ride_origin} to {ride_destination}",
                'ride_booked'
            )
        
        return f"Notification sent to driver {driver.full_name}"
    except User.DoesNotExist:
        return f"Driver with id {driver_id} not found"
    except Exception as e:
        return f"Failed to send notification: {str(e)}"


@shared_task
def send_booking_confirmation_notification(passenger_id, ride_origin, ride_destination, status):
    """Send notification to passenger about booking status"""
    try:
        passenger = User.objects.get(id=passenger_id)
        
        if status == 'confirmed':
            title = "Ride Booking Confirmed"
            content = f"Your booking for the ride from {ride_origin} to {ride_destination} has been confirmed!"
            notification_type = 'ride_booked'
        else:
            title = "Ride Booking Rejected"
            content = f"Your booking request for the ride from {ride_origin} to {ride_destination} was not accepted."
            notification_type = 'booking_cancelled'
        
        # Create notification in database
        Notification.objects.create(
            user=passenger,
            title=title,
            content=content,
            notification_type=notification_type
        )
        
        # Send email notification
        if passenger.email_notifications:
            send_notification_email.delay(
                passenger_id,
                title,
                content,
                notification_type
            )
        
        return f"Notification sent to passenger {passenger.full_name}"
    except User.DoesNotExist:
        return f"Passenger with id {passenger_id} not found"
    except Exception as e:
        return f"Failed to send notification: {str(e)}"


@shared_task
def send_ride_reminder(user_id, ride_id, hours_before=2):
    """Send ride reminder notification"""
    try:
        user = User.objects.get(id=user_id)
        
        # Create notification
        Notification.objects.create(
            user=user,
            title="Ride Reminder",
            content=f"Your ride is starting in {hours_before} hours. Don't forget to prepare!",
            notification_type='ride_reminder'
        )
        
        # Send email if enabled
        if user.email_notifications:
            send_notification_email.delay(
                user_id,
                "Ride Reminder",
                f"Your ride is starting in {hours_before} hours. Don't forget to prepare!",
                'ride_reminder'
            )
        
        return f"Reminder sent to {user.full_name}"
    except User.DoesNotExist:
        return f"User with id {user_id} not found"
    except Exception as e:
        return f"Failed to send reminder: {str(e)}"


@shared_task
def cleanup_old_notifications():
    """Clean up old notifications (older than 30 days)"""
    from django.utils import timezone
    from datetime import timedelta
    
    cutoff_date = timezone.now() - timedelta(days=30)
    deleted_count = Notification.objects.filter(created_at__lt=cutoff_date).delete()[0]
    
    return f"Deleted {deleted_count} old notifications"


@shared_task
def send_daily_summary_email(user_id):
    """Send daily summary of activities to user"""
    try:
        user = User.objects.get(id=user_id)
        
        if not user.email_notifications:
            return "User has disabled email notifications"
        
        # Get today's notifications
        from django.utils import timezone
        today = timezone.now().date()
        notifications = Notification.objects.filter(
            user=user,
            created_at__date=today
        )
        
        if not notifications.exists():
            return "No notifications to summarize"
        
        # Create summary email
        html_message = render_to_string('notifications/daily_summary.html', {
            'user': user,
            'notifications': notifications,
            'date': today,
        })
        plain_message = strip_tags(html_message)
        
        send_mail(
            subject="CoJourneyHub - Daily Summary",
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        return f"Daily summary sent to {user.email}"
    except User.DoesNotExist:
        return f"User with id {user_id} not found"
    except Exception as e:
        return f"Failed to send daily summary: {str(e)}"
