from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, UserProfile, Vehicle


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'full_name', 'password', 'password_confirm',
            'phone_number', 'city', 'country'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # Create user profile
        UserProfile.objects.create(user=user)
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    
    email = serializers.EmailField()
    password = serializers.CharField()
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include email and password')
        
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile"""
    
    class Meta:
        model = UserProfile
        fields = '__all__'
        read_only_fields = ['user', 'created_at', 'updated_at']


class VehicleSerializer(serializers.ModelSerializer):
    """Serializer for vehicle information"""
    
    full_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Vehicle
        fields = '__all__'
        read_only_fields = ['owner', 'created_at', 'updated_at']


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user information"""
    
    profile = UserProfileSerializer(read_only=True)
    vehicles = VehicleSerializer(many=True, read_only=True)
    ride_count = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'full_name', 'phone_number',
            'date_of_birth', 'bio', 'avatar', 'city', 'country',
            'rides_offered', 'rides_completed', 'loyalty_points',
            'referrals', 'rating', 'total_ratings', 'is_verified',
            'email_notifications', 'sms_notifications', 'ride_count',
            'profile', 'vehicles', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'rides_offered', 'rides_completed', 'loyalty_points',
            'referrals', 'rating', 'total_ratings', 'is_verified',
            'ride_count', 'created_at', 'updated_at'
        ]


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user information"""
    
    class Meta:
        model = User
        fields = [
            'full_name', 'phone_number', 'date_of_birth', 'bio',
            'avatar', 'city', 'country', 'email_notifications',
            'sms_notifications'
        ]


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for changing password"""
    
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class UserStatsSerializer(serializers.ModelSerializer):
    """Serializer for user statistics"""
    
    ride_count = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'rides_offered', 'rides_completed', 'loyalty_points',
            'referrals', 'rating', 'total_ratings', 'ride_count'
        ]
