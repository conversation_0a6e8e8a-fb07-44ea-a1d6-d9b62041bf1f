#!/usr/bin/env python
"""
Complete setup script for CoJourneyHub with Django backend
This script sets up both frontend and backend for development
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description, cwd=None, check=True):
    """Run a shell command and handle errors"""
    print(f"\n{'='*50}")
    print(f"🔧 {description}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=check, 
            cwd=cwd,
            text=True
        )
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            return True
        else:
            print(f"⚠️  {description} - WARNING (non-zero exit code)")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED")
        print(f"Error: {e}")
        return False

def check_prerequisites():
    """Check if required tools are installed"""
    print("🔍 Checking prerequisites...")
    
    prerequisites = [
        ("python", "Python 3.11+"),
        ("node", "Node.js 18+"),
        ("npm", "NPM"),
        ("git", "Git")
    ]
    
    missing = []
    for cmd, name in prerequisites:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, check=True)
            print(f"✅ {name} is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {name} is not installed")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing prerequisites: {', '.join(missing)}")
        print("\nPlease install the missing tools and run this script again.")
        return False
    
    return True

def setup_backend():
    """Set up Django backend"""
    print("\n🐍 Setting up Django Backend")
    
    backend_dir = Path("backend")
    
    # Create backend directory if it doesn't exist
    if not backend_dir.exists():
        print("❌ Backend directory not found. Please ensure you're in the correct directory.")
        return False
    
    # Create virtual environment
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        if not run_command("python -m venv venv", "Creating virtual environment", backend_dir):
            return False
    
    # Determine activation command based on OS
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
        python_cmd = "venv\\Scripts\\python"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_cmd = "source venv/bin/activate"
        python_cmd = "venv/bin/python"
        pip_cmd = "venv/bin/pip"
    
    # Install dependencies
    install_cmd = f"{activate_cmd} && pip install -r requirements.txt"
    if not run_command(install_cmd, "Installing Python dependencies", backend_dir):
        return False
    
    # Copy environment file if it doesn't exist
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    if not env_file.exists() and env_example.exists():
        run_command("cp .env.example .env", "Creating environment file", backend_dir)
    
    # Run migrations
    migrate_cmd = f"{activate_cmd} && python manage.py migrate"
    if not run_command(migrate_cmd, "Running database migrations", backend_dir):
        return False
    
    # Populate location data
    populate_cmd = f"{activate_cmd} && python populate_locations.py"
    run_command(populate_cmd, "Populating location data", backend_dir, check=False)
    
    # Create superuser (optional)
    print("\n👤 Creating superuser (optional - you can skip this)")
    create_superuser = input("Do you want to create a superuser now? (y/N): ")
    if create_superuser.lower() == 'y':
        superuser_cmd = f"{activate_cmd} && python manage.py createsuperuser"
        run_command(superuser_cmd, "Creating superuser", backend_dir, check=False)
    
    return True

def setup_frontend():
    """Set up React frontend"""
    print("\n⚛️ Setting up React Frontend")
    
    # Install dependencies
    if not run_command("npm install", "Installing Node.js dependencies"):
        return False
    
    # Install additional dependencies for Django integration
    additional_deps = [
        "axios",  # For API calls
    ]
    
    for dep in additional_deps:
        run_command(f"npm install {dep}", f"Installing {dep}", check=False)
    
    return True

def start_development_servers():
    """Start both backend and frontend development servers"""
    print("\n🚀 Starting Development Servers")
    
    backend_dir = Path("backend")
    activate_cmd = "source venv/bin/activate" if os.name != 'nt' else "venv\\Scripts\\activate"
    
    print("\n📋 To start the development servers manually:")
    print("\n1. Backend (Django):")
    print(f"   cd backend")
    print(f"   {activate_cmd}")
    print(f"   python manage.py runserver")
    print("\n2. Frontend (React):")
    print(f"   npm run dev")
    print("\n3. Redis (for real-time features - optional):")
    print(f"   redis-server")
    
    # Ask if user wants to start servers now
    start_now = input("\nDo you want to start the servers now? (y/N): ")
    if start_now.lower() == 'y':
        print("\n🚀 Starting Django backend...")
        django_cmd = f"{activate_cmd} && python manage.py runserver"
        
        # Start Django in background
        django_process = subprocess.Popen(
            django_cmd,
            shell=True,
            cwd=backend_dir
        )
        
        print("⏳ Waiting for Django to start...")
        time.sleep(3)
        
        print("🚀 Starting React frontend...")
        # Start React (this will block)
        try:
            subprocess.run("npm run dev", shell=True, check=True)
        except KeyboardInterrupt:
            print("\n🛑 Stopping servers...")
            django_process.terminate()
            django_process.wait()

def run_tests():
    """Run basic tests to verify setup"""
    print("\n🧪 Running Basic Tests")
    
    backend_dir = Path("backend")
    activate_cmd = "source venv/bin/activate" if os.name != 'nt' else "venv\\Scripts\\activate"
    
    # Test Django
    django_test = f"{activate_cmd} && python manage.py check"
    django_result = run_command(django_test, "Django system check", backend_dir, check=False)
    
    # Test frontend build
    frontend_result = run_command("npm run build", "Frontend build test", check=False)
    
    if django_result and frontend_result:
        print("✅ All basic tests passed!")
        return True
    else:
        print("⚠️  Some tests failed, but the setup might still work")
        return False

def print_final_instructions():
    """Print final setup instructions"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("\n1. 🚀 Start the development servers:")
    print("   Terminal 1 (Backend):")
    print("   cd backend")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    print("   python manage.py runserver")
    
    print("\n   Terminal 2 (Frontend):")
    print("   npm run dev")
    
    print("\n2. 🌐 Access the application:")
    print("   Frontend: http://localhost:5173")
    print("   Backend API: http://localhost:8000/api")
    print("   Django Admin: http://localhost:8000/admin")
    
    print("\n3. 🗺️ Map Features:")
    print("   ✅ Free OpenStreetMap integration")
    print("   ✅ Tunisia location database")
    print("   ✅ Route calculation")
    print("   ✅ Location autocomplete")
    
    print("\n4. 🔧 Optional Setup:")
    print("   - Install Redis for real-time messaging")
    print("   - Configure email settings in backend/.env")
    print("   - Set up PostgreSQL for production")
    
    print("\n5. 🧪 Run Tests:")
    print("   python run_tests.py")
    
    print("\n6. 📚 Documentation:")
    print("   - DJANGO_SETUP_GUIDE.md - Detailed setup guide")
    print("   - Backend API docs at /api/ endpoints")
    
    print("\n🎯 Features Included:")
    print("   ✅ User authentication (JWT)")
    print("   ✅ Ride creation and booking")
    print("   ✅ Real-time messaging")
    print("   ✅ Location search with maps")
    print("   ✅ Review system")
    print("   ✅ Notifications")
    print("   ✅ Mobile-responsive design")
    
    print("\n🔒 Security Features:")
    print("   ✅ JWT authentication")
    print("   ✅ CORS protection")
    print("   ✅ Input validation")
    print("   ✅ SQL injection protection")
    
    print("\n" + "="*60)
    print("Happy coding! 🚗💨")
    print("="*60)

def main():
    """Main setup function"""
    print("🚀 CoJourneyHub Complete Setup")
    print("Django Backend + React Frontend + Free Maps")
    print("="*60)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Setup backend
    if not setup_backend():
        print("❌ Backend setup failed")
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        print("❌ Frontend setup failed")
        sys.exit(1)
    
    # Run basic tests
    run_tests()
    
    # Print final instructions
    print_final_instructions()
    
    # Ask about starting servers
    start_servers = input("\nWould you like to start the development servers now? (y/N): ")
    if start_servers.lower() == 'y':
        start_development_servers()

if __name__ == "__main__":
    main()
