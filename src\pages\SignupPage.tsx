
import { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Navbar from "@/components/Navbar";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { useAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import Footer from "@/components/Footer";

const SignupPage = () => {
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { translations, language } = useLanguage();
  const { pendingRideOffer, pendingRideRequest } = useUser();
  const { register } = useAuth();
  const location = useLocation();
  const isRTL = language === "ar";

  // Get redirect path from query parameter
  const searchParams = new URLSearchParams(location.search);
  const redirectPath = searchParams.get("redirect");

  // Handle redirect after successful signup
  const handleRedirectAfterSignup = () => {
    if (redirectPath === "offer-ride" && pendingRideOffer) {
      navigate("/offer-ride");
    } else if (redirectPath === "search" && pendingRideRequest) {
      const { origin, destination, date, passengers } = pendingRideRequest;
      navigate(`/?origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&date=${encodeURIComponent(date)}&passengers=${passengers}`);
    } else {
      navigate("/");
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setErrors({});

    try {
      // Validate input
      const signupSchema = z.object({
        fullName: z.string().min(3, { message: translations.pleaseEnterStartingPoint }),
        email: z.string().email({ message: translations.pleaseEnterStartingPoint }),
        password: z.string().min(6, { message: translations.pleaseSelectTravelDate }),
        confirmPassword: z.string(),
      }).refine((data) => data.password === data.confirmPassword, {
        message: translations.pleaseSelectTravelDate,
        path: ["confirmPassword"],
      });

      signupSchema.parse({ fullName, email, password, confirmPassword });

      setIsLoading(true);

      // Use Django registration
      const success = await register({
        email,
        password,
        full_name: fullName
      });

      if (success) {
        toast({
          title: translations.accountCreated,
          description: translations.welcomeToCoSesameHub,
        });

        handleRedirectAfterSignup();
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path) {
            fieldErrors[err.path[0]] = err.message;
          }
        });
        setErrors(fieldErrors);
      } else {
        toast({
          variant: "destructive",
          title: translations.signupFailed,
          description: translations.errorCreatingAccount,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1 flex justify-center items-center">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className={`text-2xl font-bold text-center ${isRTL ? 'text-right' : ''}`}>
              {translations.createAccount}
            </CardTitle>
            <CardDescription className={`text-center ${isRTL ? 'text-right' : ''}`}>
              {translations.enterInformation}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="fullName" className={`text-sm font-medium ${isRTL ? 'block text-right' : ''}`}>
                  {translations.fullName}
                </label>
                <Input
                  id="fullName"
                  placeholder="John Doe"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className={`${errors.fullName ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
                  dir={isRTL ? "rtl" : "ltr"}
                />
                {errors.fullName && (
                  <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : ''}`}>{errors.fullName}</p>
                )}
              </div>
              <div className="space-y-2">
                <label htmlFor="email" className={`text-sm font-medium ${isRTL ? 'block text-right' : ''}`}>
                  {translations.email}
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`${errors.email ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
                  dir={isRTL ? "rtl" : "ltr"}
                />
                {errors.email && (
                  <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : ''}`}>{errors.email}</p>
                )}
              </div>
              <div className="space-y-2">
                <label htmlFor="password" className={`text-sm font-medium ${isRTL ? 'block text-right' : ''}`}>
                  {translations.password}
                </label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`${errors.password ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
                  dir={isRTL ? "rtl" : "ltr"}
                />
                {errors.password && (
                  <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : ''}`}>{errors.password}</p>
                )}
              </div>
              <div className="space-y-2">
                <label htmlFor="confirmPassword" className={`text-sm font-medium ${isRTL ? 'block text-right' : ''}`}>
                  {translations.confirmPassword}
                </label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={`${errors.confirmPassword ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
                  dir={isRTL ? "rtl" : "ltr"}
                />
                {errors.confirmPassword && (
                  <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : ''}`}>{errors.confirmPassword}</p>
                )}
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? translations.creatingAccount : translations.signup}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <p className={`text-sm text-muted-foreground ${isRTL ? 'text-right' : ''}`}>
              {translations.alreadyHaveAccount}{" "}
              <Link to={`/login${location.search}`} className="text-primary hover:underline">
                {translations.login}
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
      <Footer />
    </div>
  );
};

export default SignupPage;
