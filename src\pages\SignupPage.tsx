
import { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Navbar from "@/components/Navbar";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { toast } from "sonner";
import Footer from "@/components/Footer";
import RoleSelection from "@/components/auth/RoleSelection";
import DriverSignupForm, { DriverSignupData } from "@/components/auth/DriverSignupForm";
import PassengerSignupForm, { PassengerSignupData } from "@/components/auth/PassengerSignupForm";

const SignupPage = () => {
  const [selectedRole, setSelectedRole] = useState<'driver' | 'passenger' | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { translations, language } = useLanguage();
  const location = useLocation();
  const isRTL = language === "ar";

  // Handle redirect after successful signup
  const handleRedirectAfterSignup = () => {
    navigate("/");
  };

  // API call function
  const callAPI = async (url: string, data: any) => {
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(JSON.stringify(errorData));
    }

    return response.json();
  };

  const handleDriverSignup = async (data: DriverSignupData) => {
    setErrors({});
    setIsLoading(true);

    try {
      const result = await callAPI('/auth/register/driver/', data);

      toast({
        title: "Account Created Successfully!",
        description: "Welcome to CoJourneyHub! Your driver account has been created.",
      });

      // Store user data in localStorage for now
      localStorage.setItem('user', JSON.stringify(result.user));
      localStorage.setItem('isAuthenticated', 'true');

      handleRedirectAfterSignup();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      try {
        const errorData = JSON.parse(errorMessage);
        setErrors(errorData);
      } catch {
        toast({
          variant: "destructive",
          title: "Registration Failed",
          description: errorMessage,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePassengerSignup = async (data: PassengerSignupData) => {
    setErrors({});
    setIsLoading(true);

    try {
      const result = await callAPI('/auth/register/passenger/', data);

      toast({
        title: "Account Created Successfully!",
        description: "Welcome to CoJourneyHub! Your passenger account has been created.",
      });

      // Store user data in localStorage for now
      localStorage.setItem('user', JSON.stringify(result.user));
      localStorage.setItem('isAuthenticated', 'true');

      handleRedirectAfterSignup();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      try {
        const errorData = JSON.parse(errorMessage);
        setErrors(errorData);
      } catch {
        toast({
          variant: "destructive",
          title: "Registration Failed",
          description: errorMessage,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1">
        <div className="max-w-4xl mx-auto">
          {!selectedRole ? (
            <RoleSelection
              onRoleSelect={setSelectedRole}
              selectedRole={selectedRole}
            />
          ) : (
            <div className="space-y-6">
              <div className="text-center">
                <h1 className="text-3xl font-bold">
                  Create Your {selectedRole === 'driver' ? 'Driver' : 'Passenger'} Account
                </h1>
                <p className="text-muted-foreground mt-2">
                  Fill in the information below to get started
                </p>
              </div>

              {selectedRole === 'driver' ? (
                <DriverSignupForm
                  onSubmit={handleDriverSignup}
                  isLoading={isLoading}
                  errors={errors}
                />
              ) : (
                <PassengerSignupForm
                  onSubmit={handlePassengerSignup}
                  isLoading={isLoading}
                  errors={errors}
                />
              )}

              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  Already have an account?{" "}
                  <Link to={`/login${location.search}`} className="text-primary hover:underline">
                    Sign in
                  </Link>
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Want to change your role?{" "}
                  <button
                    onClick={() => setSelectedRole(null)}
                    className="text-primary hover:underline"
                  >
                    Go back
                  </button>
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default SignupPage;
