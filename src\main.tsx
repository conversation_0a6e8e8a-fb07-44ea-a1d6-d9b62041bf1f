
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Check if user has already set a theme preference
const userTheme = localStorage.getItem("theme") as "light" | "dark" | null;
const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;

// Apply dark mode if user has selected it or if system prefers dark and user hasn't set a preference
if (userTheme === "dark" || (!userTheme && systemPrefersDark)) {
  document.documentElement.classList.add("dark");
  localStorage.setItem("theme", "dark");
} else {
  document.documentElement.classList.remove("dark");
  localStorage.setItem("theme", "light");
}

// Check for Supabase environment variables and provide helpful warning if missing
if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
  console.warn(
    "Supabase environment variables are missing. Please ensure your project is connected to Supabase. " +
    "You can connect by clicking the green Supabase button in the top right corner."
  );
}

// Render the app
const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Root element not found");
}

createRoot(rootElement).render(<App />);
