from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Review, ReviewResponse, ReviewFlag
from accounts.serializers import UserSerializer

User = get_user_model()


class ReviewSerializer(serializers.ModelSerializer):
    """Serializer for reviews"""

    reviewer = UserSerializer(read_only=True)
    reviewed = UserSerializer(read_only=True)
    response = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = [
            'id', 'ride', 'reviewer', 'reviewed', 'rating', 'comment',
            'punctuality_rating', 'communication_rating', 'cleanliness_rating',
            'safety_rating', 'is_driver_review', 'is_public', 'response',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['reviewer', 'created_at', 'updated_at']

    def get_response(self, obj):
        if hasattr(obj, 'response'):
            return ReviewResponseSerializer(obj.response).data
        return None


class ReviewCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating reviews"""

    class Meta:
        model = Review
        fields = [
            'ride', 'reviewed', 'rating', 'comment',
            'punctuality_rating', 'communication_rating',
            'cleanliness_rating', 'safety_rating', 'is_driver_review'
        ]

    def validate(self, attrs):
        ride = attrs['ride']
        reviewed = attrs['reviewed']

        # Get reviewer (authenticated or default for testing)
        if self.context['request'].user.is_authenticated:
            reviewer = self.context['request'].user
        else:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            reviewer = User.objects.first()
            if not reviewer:
                raise serializers.ValidationError("No users available for review")

        # Check if reviewer participated in the ride
        if reviewer == ride.driver:
            # Driver can review passengers
            if not ride.bookings.filter(user=reviewed, status='completed').exists():
                raise serializers.ValidationError(
                    "You can only review passengers who completed your ride"
                )
        else:
            # Passenger can review driver
            if reviewed != ride.driver:
                raise serializers.ValidationError(
                    "Passengers can only review the driver"
                )

            # Check if reviewer booked this ride
            if not ride.bookings.filter(user=reviewer, status='completed').exists():
                raise serializers.ValidationError(
                    "You can only review rides you completed"
                )

        # Check if review already exists
        if Review.objects.filter(
            ride=ride,
            reviewer=reviewer,
            reviewed=reviewed
        ).exists():
            raise serializers.ValidationError(
                "You have already reviewed this user for this ride"
            )

        return attrs

    def create(self, validated_data):
        # Get reviewer (authenticated or default for testing)
        if self.context['request'].user.is_authenticated:
            reviewer = self.context['request'].user
        else:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            reviewer = User.objects.first()
            if not reviewer:
                # Create a default user for testing
                reviewer = User.objects.create_user(
                    username='default_reviewer',
                    email='<EMAIL>',
                    full_name='Default Reviewer',
                    password='testpass123'
                )

        validated_data['reviewer'] = reviewer
        return super().create(validated_data)


class ReviewResponseSerializer(serializers.ModelSerializer):
    """Serializer for review responses"""

    responder = UserSerializer(read_only=True)

    class Meta:
        model = ReviewResponse
        fields = ['id', 'content', 'responder', 'created_at', 'updated_at']
        read_only_fields = ['responder', 'created_at', 'updated_at']


class ReviewFlagSerializer(serializers.ModelSerializer):
    """Serializer for review flags"""

    flagger = UserSerializer(read_only=True)

    class Meta:
        model = ReviewFlag
        fields = ['id', 'reason', 'description', 'flagger', 'created_at']
        read_only_fields = ['flagger', 'created_at']


class UserReviewStatsSerializer(serializers.Serializer):
    """Serializer for user review statistics"""

    total_reviews = serializers.IntegerField()
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    rating_distribution = serializers.DictField()
    recent_reviews = ReviewSerializer(many=True)
