import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from datetime import datetime, timedelta
from rides.models import Ride, RideBooking, RideRequest

User = get_user_model()


class RideModelTest(TestCase):
    """Test Ride model"""
    
    def setUp(self):
        self.driver = User.objects.create_user(
            email='<EMAIL>',
            username='driver',
            password='pass123',
            full_name='Driver User'
        )
        self.passenger = User.objects.create_user(
            email='<EMAIL>',
            username='passenger',
            password='pass123',
            full_name='Passenger User'
        )
        
        self.ride = Ride.objects.create(
            driver=self.driver,
            origin='Tunis',
            destination='Sousse',
            departure_time=timezone.now() + timedelta(hours=2),
            arrival_time=timezone.now() + timedelta(hours=4),
            price=25.00,
            distance='140 km',
            seats_available=3,
            car_model='Peugeot 308',
            car_color='White'
        )
    
    def test_ride_creation(self):
        """Test ride creation"""
        self.assertEqual(self.ride.driver, self.driver)
        self.assertEqual(self.ride.origin, 'Tunis')
        self.assertEqual(self.ride.destination, 'Sousse')
        self.assertEqual(self.ride.price, 25.00)
        self.assertEqual(self.ride.seats_available, 3)
    
    def test_ride_str(self):
        """Test ride string representation"""
        expected = f"Tunis → Sousse on {self.ride.departure_time.strftime('%Y-%m-%d %H:%M')}"
        self.assertEqual(str(self.ride), expected)
    
    def test_seats_booked_property(self):
        """Test seats_booked property"""
        # Create a booking
        RideBooking.objects.create(
            ride=self.ride,
            user=self.passenger,
            seats_booked=2,
            status='confirmed'
        )
        
        self.assertEqual(self.ride.seats_booked, 2)
    
    def test_seats_remaining_property(self):
        """Test seats_remaining property"""
        # Create a booking
        RideBooking.objects.create(
            ride=self.ride,
            user=self.passenger,
            seats_booked=2,
            status='confirmed'
        )
        
        self.assertEqual(self.ride.seats_remaining, 1)  # 3 - 2 = 1
    
    def test_can_be_booked_by_passenger(self):
        """Test can_be_booked_by method for valid passenger"""
        can_book, message = self.ride.can_be_booked_by(self.passenger)
        self.assertTrue(can_book)
        self.assertEqual(message, "Can book")
    
    def test_cannot_book_own_ride(self):
        """Test driver cannot book their own ride"""
        can_book, message = self.ride.can_be_booked_by(self.driver)
        self.assertFalse(can_book)
        self.assertEqual(message, "Cannot book your own ride")
    
    def test_cannot_book_inactive_ride(self):
        """Test cannot book inactive ride"""
        self.ride.status = 'cancelled'
        self.ride.save()
        
        can_book, message = self.ride.can_be_booked_by(self.passenger)
        self.assertFalse(can_book)
        self.assertEqual(message, "Ride is not active")


class RideBookingModelTest(TestCase):
    """Test RideBooking model"""
    
    def setUp(self):
        self.driver = User.objects.create_user(
            email='<EMAIL>',
            username='driver',
            password='pass123'
        )
        self.passenger = User.objects.create_user(
            email='<EMAIL>',
            username='passenger',
            password='pass123'
        )
        
        self.ride = Ride.objects.create(
            driver=self.driver,
            origin='Tunis',
            destination='Sousse',
            departure_time=timezone.now() + timedelta(hours=2),
            arrival_time=timezone.now() + timedelta(hours=4),
            price=25.00,
            distance='140 km',
            seats_available=3,
            car_model='Peugeot 308',
            car_color='White'
        )
        
        self.booking = RideBooking.objects.create(
            ride=self.ride,
            user=self.passenger,
            seats_booked=2
        )
    
    def test_booking_creation(self):
        """Test booking creation"""
        self.assertEqual(self.booking.ride, self.ride)
        self.assertEqual(self.booking.user, self.passenger)
        self.assertEqual(self.booking.seats_booked, 2)
        self.assertEqual(self.booking.status, 'confirmed')
    
    def test_calculate_amount(self):
        """Test calculate_amount method"""
        expected_amount = self.ride.price * self.booking.seats_booked
        self.assertEqual(self.booking.calculate_amount(), expected_amount)


class RideAPITest(APITestCase):
    """Test Ride API"""
    
    def setUp(self):
        self.driver = User.objects.create_user(
            email='<EMAIL>',
            username='driver',
            password='pass123',
            full_name='Driver User'
        )
        self.passenger = User.objects.create_user(
            email='<EMAIL>',
            username='passenger',
            password='pass123',
            full_name='Passenger User'
        )
    
    def test_create_ride_authenticated(self):
        """Test creating a ride when authenticated"""
        self.client.force_authenticate(user=self.driver)
        
        url = reverse('ride-list-create')
        data = {
            'origin': 'Tunis',
            'destination': 'Sousse',
            'departure_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'arrival_time': (timezone.now() + timedelta(hours=4)).isoformat(),
            'price': 25.00,
            'distance': '140 km',
            'seats_available': 3,
            'car_model': 'Peugeot 308',
            'car_color': 'White',
            'features': ['AC', 'Music']
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        ride = Ride.objects.get(id=response.data['id'])
        self.assertEqual(ride.driver, self.driver)
        self.assertEqual(ride.origin, 'Tunis')
    
    def test_create_ride_unauthenticated(self):
        """Test creating a ride when not authenticated"""
        url = reverse('ride-list-create')
        data = {
            'origin': 'Tunis',
            'destination': 'Sousse',
            'departure_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'arrival_time': (timezone.now() + timedelta(hours=4)).isoformat(),
            'price': 25.00,
            'distance': '140 km',
            'seats_available': 3,
            'car_model': 'Peugeot 308',
            'car_color': 'White'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_rides(self):
        """Test listing rides"""
        self.client.force_authenticate(user=self.passenger)
        
        # Create a ride
        Ride.objects.create(
            driver=self.driver,
            origin='Tunis',
            destination='Sousse',
            departure_time=timezone.now() + timedelta(hours=2),
            arrival_time=timezone.now() + timedelta(hours=4),
            price=25.00,
            distance='140 km',
            seats_available=3,
            car_model='Peugeot 308',
            car_color='White'
        )
        
        url = reverse('ride-list-create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_book_ride(self):
        """Test booking a ride"""
        self.client.force_authenticate(user=self.passenger)
        
        ride = Ride.objects.create(
            driver=self.driver,
            origin='Tunis',
            destination='Sousse',
            departure_time=timezone.now() + timedelta(hours=2),
            arrival_time=timezone.now() + timedelta(hours=4),
            price=25.00,
            distance='140 km',
            seats_available=3,
            car_model='Peugeot 308',
            car_color='White'
        )
        
        url = reverse('book-ride', kwargs={'ride_id': ride.id})
        data = {
            'seats_booked': 2,
            'pickup_location': 'Tunis Centre',
            'dropoff_location': 'Sousse Centre'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        booking = RideBooking.objects.get(ride=ride, user=self.passenger)
        self.assertEqual(booking.seats_booked, 2)
    
    def test_search_rides(self):
        """Test searching rides"""
        self.client.force_authenticate(user=self.passenger)
        
        # Create rides
        Ride.objects.create(
            driver=self.driver,
            origin='Tunis',
            destination='Sousse',
            departure_time=timezone.now() + timedelta(hours=2),
            arrival_time=timezone.now() + timedelta(hours=4),
            price=25.00,
            distance='140 km',
            seats_available=3,
            car_model='Peugeot 308',
            car_color='White'
        )
        
        Ride.objects.create(
            driver=self.driver,
            origin='Sfax',
            destination='Tunis',
            departure_time=timezone.now() + timedelta(hours=3),
            arrival_time=timezone.now() + timedelta(hours=6),
            price=30.00,
            distance='270 km',
            seats_available=2,
            car_model='Renault Clio',
            car_color='Blue'
        )
        
        url = reverse('ride-search')
        data = {
            'origin': 'Tunis',
            'destination': 'Sousse',
            'max_price': 30.00
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)  # Only Tunis->Sousse ride should match
