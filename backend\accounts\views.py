from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import authenticate, login, logout
from django.shortcuts import get_object_or_404
from .models import User, UserProfile, Vehicle
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    UserUpdateSerializer, PasswordChangeSerializer, UserStatsSerializer,
    UserProfileSerializer, VehicleSerializer
)


class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint"""

    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Log the user in
        login(request, user)

        return Response({
            'user': UserSerializer(user).data,
            'message': 'User registered and logged in successfully'
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """User login endpoint"""

    serializer = UserLoginSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    user = serializer.validated_data['user']
    login(request, user)

    return Response({
        'user': UserSerializer(user).data,
        'message': 'Login successful'
    })


@api_view(['POST'])
def logout_view(request):
    """User logout endpoint"""

    logout(request)
    return Response({'message': 'Successfully logged out'})


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile view and update"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class UserUpdateView(generics.UpdateAPIView):
    """Update user information"""

    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


@api_view(['POST'])
def change_password_view(request):
    """Change user password"""

    serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
    serializer.is_valid(raise_exception=True)

    user = request.user
    user.set_password(serializer.validated_data['new_password'])
    user.save()

    return Response({'message': 'Password changed successfully'})


class UserStatsView(generics.RetrieveAPIView):
    """Get user statistics"""

    serializer_class = UserStatsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class UserProfileDetailView(generics.RetrieveUpdateAPIView):
    """User profile details view"""

    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile


class VehicleListCreateView(generics.ListCreateAPIView):
    """List and create user vehicles"""

    serializer_class = VehicleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Vehicle.objects.filter(owner=self.request.user)

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)


class VehicleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vehicle detail, update, and delete"""

    serializer_class = VehicleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Vehicle.objects.filter(owner=self.request.user)


@api_view(['GET'])
def user_detail_view(request, user_id):
    """Get public user information"""

    user = get_object_or_404(User, id=user_id)

    # Return limited public information
    data = {
        'id': user.id,
        'full_name': user.full_name,
        'avatar': user.avatar.url if user.avatar else None,
        'rating': user.rating,
        'total_ratings': user.total_ratings,
        'rides_offered': user.rides_offered,
        'rides_completed': user.rides_completed,
        'city': user.city,
        'country': user.country,
        'created_at': user.created_at,
    }

    return Response(data)


@api_view(['POST'])
def add_loyalty_points_view(request):
    """Add loyalty points to user account"""

    points = request.data.get('points', 0)
    if points > 0:
        request.user.add_loyalty_points(points)
        return Response({
            'message': f'{points} loyalty points added',
            'total_points': request.user.loyalty_points
        })

    return Response({'error': 'Invalid points value'}, status=status.HTTP_400_BAD_REQUEST)
