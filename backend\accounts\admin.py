from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserProfile, Vehicle


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for User model"""
    
    list_display = [
        'email', 'username', 'full_name', 'city', 'rating',
        'rides_offered', 'rides_completed', 'loyalty_points',
        'is_verified', 'is_active', 'created_at'
    ]
    list_filter = [
        'is_active', 'is_verified', 'city', 'country',
        'email_notifications', 'sms_notifications', 'created_at'
    ]
    search_fields = ['email', 'username', 'full_name', 'phone_number']
    ordering = ['-created_at']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Personal Information', {
            'fields': (
                'full_name', 'phone_number', 'date_of_birth',
                'bio', 'avatar', 'city', 'country'
            )
        }),
        ('Ride Statistics', {
            'fields': (
                'rides_offered', 'rides_completed', 'loyalty_points',
                'referrals', 'rating', 'total_ratings'
            )
        }),
        ('Account Settings', {
            'fields': (
                'is_verified', 'email_notifications', 'sms_notifications'
            )
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for UserProfile model"""
    
    list_display = [
        'user', 'preferred_language', 'preferred_currency',
        'is_driver', 'created_at'
    ]
    list_filter = ['preferred_language', 'preferred_currency', 'is_driver']
    search_fields = ['user__email', 'user__full_name']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Preferences', {
            'fields': ('preferred_language', 'preferred_currency')
        }),
        ('Driver Information', {
            'fields': (
                'is_driver', 'driver_license_number', 'driver_license_expiry'
            )
        }),
        ('Emergency Contact', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        ('Social Links', {
            'fields': ('facebook_profile', 'linkedin_profile')
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    """Admin configuration for Vehicle model"""
    
    list_display = [
        'license_plate', 'owner', 'make', 'model', 'year',
        'color', 'seats', 'is_active', 'created_at'
    ]
    list_filter = [
        'make', 'vehicle_type', 'year', 'has_ac',
        'has_music', 'is_smoking_allowed', 'is_active'
    ]
    search_fields = [
        'license_plate', 'owner__email', 'owner__full_name',
        'make', 'model'
    ]
    
    fieldsets = (
        ('Owner', {
            'fields': ('owner',)
        }),
        ('Vehicle Information', {
            'fields': (
                'make', 'model', 'year', 'color',
                'license_plate', 'vehicle_type', 'seats'
            )
        }),
        ('Features', {
            'fields': (
                'has_ac', 'has_music', 'is_smoking_allowed', 'has_wifi'
            )
        }),
        ('Legal Documents', {
            'fields': ('insurance_expiry', 'registration_expiry')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']
