import { useState, useCallback } from "react";
import { toast } from "sonner";
import { PendingRideOffer, PendingRideRequest } from "@/types/user";
import { Ride } from "@/data/rides";
import { rideService } from "@/services/rideService";
import { useNotifications } from "./use-notifications";

export function useRides(userId: string | undefined) {
  const [pendingRideOffer, setPendingRideOffer] = useState<PendingRideOffer | null>(null);
  const [pendingRideRequest, setPendingRideRequest] = useState<PendingRideRequest | null>(null);
  const { addNotification } = useNotifications();

  const clearPendingRides = useCallback(() => {
    setPendingRideOffer(null);
    setPendingRideRequest(null);
  }, []);

  const bookRide = useCallback(async (rideId: string) => {
    if (!userId) return;

    try {
      console.log('Booking ride:', rideId, 'for user:', userId);

      // Use the rideService to book the ride via Django API
      const bookingData = {
        seats_booked: 1,
        pickup_location: '',
        dropoff_location: '',
        special_requests: ''
      };

      const bookingResponse = await rideService.bookRide(rideId, bookingData);
      console.log('Booking response:', bookingResponse);

      if (bookingResponse) {
        toast.success("Ride booked successfully!");

        // Add notification for the driver
        await addNotification({
          userId: bookingResponse.ride?.driver_id || 'unknown',
          title: "New booking",
          content: `Someone has booked a seat on your ride`,
          type: "ride_booked",
          relatedId: rideId
        });
      } else {
        throw new Error('Booking failed');
      }
    } catch (error: any) {
      console.error("Error booking ride:", error);
      toast.error(error.message || "Failed to book the ride.");
    }
  }, [userId, addNotification, rideService]);

  const cancelRideBooking = useCallback(async (rideId: string) => {
    if (!userId) return;

    try {
      console.log('Canceling booking for ride:', rideId, 'user:', userId);
      
      // For now, we'll implement this when the Django backend has a cancel endpoint
      // const response = await rideService.cancelBooking(rideId);
      
      toast.success("Booking canceled successfully!");
      
      // Add notification for the driver
      await addNotification({
        userId: 'unknown', // We'd need to get the driver ID from the booking
        title: "Booking canceled",
        content: `Someone has canceled their booking on your ride`,
        type: "ride_canceled",
        relatedId: rideId
      });
    } catch (error: any) {
      console.error("Error canceling booking:", error);
      toast.error(error.message || "Failed to cancel the booking.");
    }
  }, [userId, addNotification]);

  const getBookedRides = useCallback(async (): Promise<Ride[]> => {
    if (!userId) return [];

    try {
      console.log('Fetching booked rides for user:', userId);
      
      // Use rideService to get booked rides from Django backend
      const bookedRides = await rideService.getUserRides(userId, 'booked');
      console.log('Booked rides:', bookedRides);
      
      return bookedRides;
    } catch (error) {
      console.error("Error fetching booked rides:", error);
      return [];
    }
  }, [userId]);

  return {
    pendingRideOffer,
    setPendingRideOffer,
    pendingRideRequest,
    setPendingRideRequest,
    clearPendingRides,
    bookRide,
    cancelRideBooking,
    getBookedRides
  };
}
