from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.db.models import Q
import requests
import math
from .models import TunisiaLocation, Route, LocationSearch
from .serializers import (
    TunisiaLocationSerializer, RouteSerializer, LocationSearchSerializer,
    LocationAutocompleteSerializer, RouteCalculationSerializer, RouteResponseSerializer
)


class LocationListView(generics.ListAPIView):
    """List all locations"""

    serializer_class = TunisiaLocationSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = TunisiaLocation.objects.all()

        # Filter by type
        location_type = self.request.query_params.get('type')
        if location_type:
            queryset = queryset.filter(location_type=location_type)

        # Filter by governorate
        governorate = self.request.query_params.get('governorate')
        if governorate:
            queryset = queryset.filter(governorate__icontains=governorate)

        # Show popular locations first
        popular_only = self.request.query_params.get('popular')
        if popular_only:
            queryset = queryset.filter(is_popular=True)

        return queryset.order_by('-is_popular', 'name')


@api_view(['GET'])
@permission_classes([AllowAny])
def location_autocomplete(request):
    """Autocomplete search for locations"""

    query = request.GET.get('q', '').strip()
    if len(query) < 2:
        return Response([])

    # Search in name fields
    locations = TunisiaLocation.objects.filter(
        Q(name__icontains=query) |
        Q(name_ar__icontains=query) |
        Q(name_fr__icontains=query)
    ).order_by('-is_popular', 'name')[:10]

    # Log the search
    LocationSearch.objects.create(
        query=query,
        result_count=locations.count(),
        user_ip=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', '')
    )

    # Format response
    results = []
    for location in locations:
        results.append({
            'id': location.id,
            'name': location.name,
            'type': location.get_location_type_display(),
            'governorate': location.governorate,
            'coordinates': location.coordinates
        })

    return Response(results)


@api_view(['GET'])
@permission_classes([AllowAny])
def popular_routes(request):
    """Get popular routes"""

    routes = Route.objects.filter(is_popular=True).select_related('origin', 'destination')[:20]
    serializer = RouteSerializer(routes, many=True)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([AllowAny])
def calculate_route(request):
    """Calculate route between two points"""

    serializer = RouteCalculationSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    data = serializer.validated_data
    origin = (float(data['origin_lat']), float(data['origin_lng']))
    destination = (float(data['destination_lat']), float(data['destination_lng']))

    # Calculate distance using haversine formula
    distance = calculate_distance(origin[0], origin[1], destination[0], destination[1])

    # Estimate duration (assuming average speed of 60 km/h)
    duration_minutes = int((distance / 60) * 60)

    # Try to get route from external service (OpenRouteService, OSRM, etc.)
    route_coordinates = get_route_coordinates(origin, destination)

    response_data = {
        'distance_km': round(distance, 2),
        'duration_minutes': duration_minutes,
    }

    if route_coordinates:
        response_data['route_coordinates'] = route_coordinates

    return Response(response_data)


@api_view(['GET'])
@permission_classes([AllowAny])
def nearby_locations(request):
    """Get locations near a point"""

    lat = request.GET.get('lat')
    lng = request.GET.get('lng')
    radius = float(request.GET.get('radius', 50))  # km

    if not lat or not lng:
        return Response({'error': 'Latitude and longitude required'}, status=status.HTTP_400_BAD_REQUEST)

    center = (float(lat), float(lng))
    nearby_locations = []

    # For now, get all locations and filter by distance
    for location in TunisiaLocation.objects.all():
        if location.coordinates:
            distance = calculate_distance(center[0], center[1], location.coordinates[0], location.coordinates[1])
            if distance <= radius:
                location_data = TunisiaLocationSerializer(location).data
                location_data['distance_km'] = round(distance, 2)
                nearby_locations.append(location_data)

    # Sort by distance
    nearby_locations.sort(key=lambda x: x['distance_km'])

    return Response(nearby_locations[:50])  # Limit to 50 results


@api_view(['GET'])
@permission_classes([AllowAny])
def governorates(request):
    """Get list of governorates"""

    governorates = TunisiaLocation.objects.values_list('governorate', flat=True).distinct()
    governorates = [g for g in governorates if g]  # Remove empty values
    return Response(sorted(governorates))


def calculate_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two points using haversine formula"""
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))

    # Radius of earth in kilometers
    r = 6371
    return c * r


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_route_coordinates(origin, destination):
    """Get route coordinates from external routing service"""

    # Example using OpenRouteService (free tier available)
    # You would need to sign up for an API key

    try:
        # This is a placeholder - you would implement actual routing service integration
        # For now, return a simple straight line
        return [
            [origin[1], origin[0]],  # [lng, lat] format for GeoJSON
            [destination[1], destination[0]]
        ]
    except Exception as e:
        print(f"Error getting route coordinates: {e}")
        return None


@api_view(['POST'])
@permission_classes([AllowAny])
def log_location_selection(request):
    """Log when a user selects a location from search results"""

    query = request.data.get('query')
    location_id = request.data.get('location_id')

    if query and location_id:
        try:
            location = TunisiaLocation.objects.get(id=location_id)
            LocationSearch.objects.create(
                query=query,
                result_count=1,
                selected_location=location,
                user_ip=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
        except TunisiaLocation.DoesNotExist:
            pass

    return Response({'status': 'logged'})
