
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star } from "lucide-react";
import { Driver } from "@/data/rides";
import { useLanguage } from "@/contexts/LanguageContext";

interface DriverProfileProps {
  driver: Driver;
  onViewProfile: () => void;
}

export const DriverProfile = ({ driver, onViewProfile }: DriverProfileProps) => {
  const { translations } = useLanguage();
  
  return (
    <button 
      onClick={onViewProfile}
      className="group flex flex-col items-center focus:outline-none"
      aria-label="View driver profile"
    >
      <Avatar className="h-16 w-16 mb-2 border-2 border-primary group-hover:ring-2 group-hover:ring-primary/50 transition-all">
        <AvatarImage src={driver.avatar} alt={driver.name} />
        <AvatarFallback>{driver.name.substring(0, 2).toUpperCase()}</AvatarFallback>
      </Avatar>
      <div className="text-sm font-medium dark:text-white group-hover:text-primary transition-colors">{driver.name}</div>
      <div className="flex items-center mt-1">
        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
        <span className="text-sm ml-1">{driver.rating}/5</span>
      </div>
      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
        {driver.rideCount} {translations.ridesCompleted}
      </div>
      <div className="mt-2 text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">
        {translations.viewProfile || "View Profile"}
      </div>
    </button>
  );
};
