import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Star, MessageSquare, ThumbsUp, Flag } from "lucide-react";
import { toast } from "sonner";

interface Review {
  id: string;
  reviewer: {
    id: string;
    full_name: string;
    email: string;
  };
  reviewed: {
    id: string;
    full_name: string;
    email: string;
  };
  rating: number;
  comment: string;
  punctuality_rating?: number;
  communication_rating?: number;
  cleanliness_rating?: number;
  safety_rating?: number;
  is_driver_review: boolean;
  created_at: string;
  response?: {
    content: string;
    responder: {
      full_name: string;
    };
    created_at: string;
  };
}

interface ReviewsSectionProps {
  userId?: string;
  rideId?: string;
  canLeaveReview?: boolean;
  reviewedUserId?: string;
  isDriverReview?: boolean;
}

const ReviewsSection = ({ 
  userId, 
  rideId, 
  canLeaveReview = false, 
  reviewedUserId,
  isDriverReview = false 
}: ReviewsSectionProps) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 5,
    comment: "",
    punctuality_rating: 5,
    communication_rating: 5,
    cleanliness_rating: 5,
    safety_rating: 5,
  });

  useEffect(() => {
    fetchReviews();
  }, [userId, rideId]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (userId) params.append('user_id', userId);
      if (rideId) params.append('ride_id', rideId);

      const response = await fetch(`/api/reviews/?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setReviews(data.results || data);
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    if (!reviewedUserId || !rideId) {
      toast.error("Missing required information for review");
      return;
    }

    try {
      setSubmitting(true);
      
      const reviewData = {
        ride: rideId,
        reviewed: reviewedUserId,
        is_driver_review: isDriverReview,
        ...newReview,
      };

      const response = await fetch('/api/reviews/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(reviewData),
      });

      if (response.ok) {
        toast.success("Review submitted successfully!");
        setShowReviewForm(false);
        setNewReview({
          rating: 5,
          comment: "",
          punctuality_rating: 5,
          communication_rating: 5,
          cleanliness_rating: 5,
          safety_rating: 5,
        });
        fetchReviews();
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to submit review");
      }
    } catch (error) {
      console.error("Error submitting review:", error);
      toast.error("Failed to submit review");
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number, size = "h-4 w-4") => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${size} ${
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    );
  };

  const renderInteractiveStars = (rating: number, onChange: (rating: number) => void) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-6 w-6 cursor-pointer transition-colors ${
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300 dark:text-gray-600 hover:text-yellow-300"
            }`}
            onClick={() => onChange(star)}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">
          Reviews {reviews.length > 0 && `(${reviews.length})`}
        </h3>
        {canLeaveReview && (
          <Button
            onClick={() => setShowReviewForm(true)}
            variant="outline"
            size="sm"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Leave Review
          </Button>
        )}
      </div>

      {/* Review Form */}
      {showReviewForm && (
        <Card>
          <CardHeader>
            <CardTitle>Leave a Review</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Overall Rating</label>
              {renderInteractiveStars(newReview.rating, (rating) =>
                setNewReview({ ...newReview, rating })
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Punctuality</label>
                {renderInteractiveStars(newReview.punctuality_rating, (rating) =>
                  setNewReview({ ...newReview, punctuality_rating: rating })
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Communication</label>
                {renderInteractiveStars(newReview.communication_rating, (rating) =>
                  setNewReview({ ...newReview, communication_rating: rating })
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Cleanliness</label>
                {renderInteractiveStars(newReview.cleanliness_rating, (rating) =>
                  setNewReview({ ...newReview, cleanliness_rating: rating })
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Safety</label>
                {renderInteractiveStars(newReview.safety_rating, (rating) =>
                  setNewReview({ ...newReview, safety_rating: rating })
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Comment</label>
              <Textarea
                placeholder="Share your experience..."
                value={newReview.comment}
                onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}
                rows={4}
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleSubmitReview}
                disabled={submitting}
              >
                {submitting ? "Submitting..." : "Submit Review"}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowReviewForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      {reviews.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No reviews yet</p>
        </div>
      ) : (
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-300 font-semibold text-sm">
                        {review.reviewer.full_name?.charAt(0) || review.reviewer.email.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="font-semibold">
                        {review.reviewer.full_name || review.reviewer.email}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatDate(review.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {renderStars(review.rating)}
                    <Badge variant={review.is_driver_review ? "default" : "secondary"}>
                      {review.is_driver_review ? "Driver Review" : "Passenger Review"}
                    </Badge>
                  </div>
                </div>

                {review.comment && (
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    {review.comment}
                  </p>
                )}

                {/* Detailed Ratings */}
                {(review.punctuality_rating || review.communication_rating || 
                  review.cleanliness_rating || review.safety_rating) && (
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    {review.punctuality_rating && (
                      <div className="flex items-center justify-between">
                        <span>Punctuality:</span>
                        {renderStars(review.punctuality_rating, "h-3 w-3")}
                      </div>
                    )}
                    {review.communication_rating && (
                      <div className="flex items-center justify-between">
                        <span>Communication:</span>
                        {renderStars(review.communication_rating, "h-3 w-3")}
                      </div>
                    )}
                    {review.cleanliness_rating && (
                      <div className="flex items-center justify-between">
                        <span>Cleanliness:</span>
                        {renderStars(review.cleanliness_rating, "h-3 w-3")}
                      </div>
                    )}
                    {review.safety_rating && (
                      <div className="flex items-center justify-between">
                        <span>Safety:</span>
                        {renderStars(review.safety_rating, "h-3 w-3")}
                      </div>
                    )}
                  </div>
                )}

                {/* Response */}
                {review.response && (
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-semibold">
                        Response from {review.response.responder.full_name}:
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatDate(review.response.created_at)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {review.response.content}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button variant="ghost" size="sm">
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    Helpful
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Flag className="h-4 w-4 mr-1" />
                    Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewsSection;
