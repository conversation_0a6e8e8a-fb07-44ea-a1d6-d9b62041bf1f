import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import SearchBar from '@/components/SearchBar';
import { UserProvider } from '@/contexts/UserContext';
import { LanguageProvider } from '@/contexts/LanguageContext';

// Mock the contexts and services
vi.mock('@/services/rideService', () => ({
  rideService: {
    searchRides: vi.fn().mockResolvedValue([])
  }
}));

const MockProviders = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <LanguageProvider>
      <UserProvider>
        {children}
      </UserProvider>
    </LanguageProvider>
  </BrowserRouter>
);

describe('SearchBar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all form fields', () => {
    render(
      <MockProviders>
        <SearchBar />
      </MockProviders>
    );

    expect(screen.getByPlaceholderText(/from/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/to/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue('')).toBeInTheDocument(); // date input
    expect(screen.getByRole('combobox')).toBeInTheDocument(); // passengers select
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(
      <MockProviders>
        <SearchBar />
      </MockProviders>
    );

    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);

    // Should show validation errors for empty fields
    await waitFor(() => {
      expect(screen.getByText(/please enter starting point/i)).toBeInTheDocument();
    });
  });

  it('calls onSearch with correct parameters', async () => {
    const mockOnSearch = vi.fn();
    render(
      <MockProviders>
        <SearchBar onSearch={mockOnSearch} />
      </MockProviders>
    );

    // Fill in the form
    const originInput = screen.getByPlaceholderText(/from/i);
    const destinationInput = screen.getByPlaceholderText(/to/i);
    const dateInput = screen.getByDisplayValue('');
    const passengersSelect = screen.getByRole('combobox');

    fireEvent.change(originInput, { target: { value: 'Tunis' } });
    fireEvent.change(destinationInput, { target: { value: 'Sousse' } });
    fireEvent.change(dateInput, { target: { value: '2024-12-25' } });
    fireEvent.change(passengersSelect, { target: { value: '2' } });

    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        origin: 'Tunis',
        destination: 'Sousse',
        date: '2024-12-25',
        passengers: 2,
        originCoords: undefined,
        destinationCoords: undefined
      });
    });
  });

  it('shows loading state during search', async () => {
    const mockOnSearch = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <MockProviders>
        <SearchBar onSearch={mockOnSearch} />
      </MockProviders>
    );

    // Fill required fields
    fireEvent.change(screen.getByPlaceholderText(/from/i), { target: { value: 'Tunis' } });
    fireEvent.change(screen.getByPlaceholderText(/to/i), { target: { value: 'Sousse' } });
    fireEvent.change(screen.getByDisplayValue(''), { target: { value: '2024-12-25' } });

    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);

    expect(screen.getByText(/searching/i)).toBeInTheDocument();
    expect(searchButton).toBeDisabled();
  });

  it('handles passenger count selection', () => {
    render(
      <MockProviders>
        <SearchBar />
      </MockProviders>
    );

    const passengersSelect = screen.getByRole('combobox');
    fireEvent.change(passengersSelect, { target: { value: '4' } });

    expect(passengersSelect).toHaveValue('4');
  });
});
