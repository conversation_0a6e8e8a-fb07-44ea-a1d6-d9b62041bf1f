
import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useUser } from "@/contexts/UserContext";
import LoginForm from "@/components/auth/LoginForm";

const LoginPage = () => {
  const { user } = useUser();
  const location = useLocation();
  const navigate = useNavigate();

  // Get redirect path from query parameter
  const searchParams = new URLSearchParams(location.search);
  const redirectPath = searchParams.get("redirect");

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      if (redirectPath === "offer-ride") {
        navigate("/offer-ride");
      } else if (redirectPath === "search") {
        navigate("/");
      } else {
        navigate("/");
      }
    }
  }, [user, redirectPath, navigate]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-1 flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
        <LoginForm redirectPath={redirectPath} />
      </main>
      <Footer />
    </div>
  );
};

export default LoginPage;
