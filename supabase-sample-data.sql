-- Sample data for CoJourneyHub (for testing purposes)
-- Run this after setting up the schema and RLS policies

-- Note: In a real application, users would be created through Supabase Auth
-- This is just for testing the database structure

-- Sample rides data (these will be inserted by authenticated users in the real app)
INSERT INTO public.rides (
    id,
    origin,
    destination,
    departure_time,
    arrival_time,
    price,
    distance,
    seats_available,
    total_seats,
    car_model,
    car_color,
    driver_id,
    features,
    origin_lat,
    origin_lng,
    destination_lat,
    destination_lng,
    status
) VALUES 
(
    uuid_generate_v4(),
    'Tunis',
    'Sousse',
    NOW() + INTERVAL '2 hours',
    NOW() + INTERVAL '4 hours',
    25.00,
    '140 km',
    3,
    4,
    'Peugeot 308',
    'White',
    (SELECT id FROM auth.users LIMIT 1), -- This will need to be a real user ID
    ARRAY['Air Conditioning', 'Music', 'Non-Smoking'],
    36.8065,
    10.1815,
    35.8256,
    10.6369,
    'active'
),
(
    uuid_generate_v4(),
    'Sfax',
    'Tunis',
    NOW() + INTERVAL '3 hours',
    NOW() + INTERVAL '6 hours',
    30.00,
    '270 km',
    2,
    4,
    'Renault Clio',
    'Blue',
    (SELECT id FROM auth.users LIMIT 1), -- This will need to be a real user ID
    ARRAY['Air Conditioning', 'Quiet Ride'],
    34.7406,
    10.7603,
    36.8065,
    10.1815,
    'active'
),
(
    uuid_generate_v4(),
    'Monastir',
    'Kairouan',
    NOW() + INTERVAL '1 day',
    NOW() + INTERVAL '1 day 2 hours',
    20.00,
    '85 km',
    4,
    4,
    'Volkswagen Golf',
    'Red',
    (SELECT id FROM auth.users LIMIT 1), -- This will need to be a real user ID
    ARRAY['Music', 'Pet Friendly'],
    35.7643,
    10.8113,
    35.6781,
    10.0963,
    'active'
);

-- Sample locations with coordinates for Tunisia
CREATE TABLE IF NOT EXISTS public.tunisia_locations (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    type TEXT DEFAULT 'city' CHECK (type IN ('city', 'university', 'hospital', 'airport', 'landmark'))
);

INSERT INTO public.tunisia_locations (name, latitude, longitude, type) VALUES
-- Major cities
('Tunis', 36.8065, 10.1815, 'city'),
('Sfax', 34.7406, 10.7603, 'city'),
('Sousse', 35.8256, 10.6369, 'city'),
('Kairouan', 35.6781, 10.0963, 'city'),
('Bizerte', 37.2744, 9.8739, 'city'),
('Gabès', 33.8815, 10.0982, 'city'),
('Ariana', 36.8625, 10.1956, 'city'),
('Gafsa', 34.4250, 8.7842, 'city'),
('Monastir', 35.7643, 10.8113, 'city'),
('Ben Arous', 36.7544, 10.2181, 'city'),
('Kasserine', 35.1674, 8.8363, 'city'),
('Médenine', 33.3549, 10.5055, 'city'),
('Nabeul', 36.4561, 10.7376, 'city'),
('Tataouine', 32.9297, 10.4517, 'city'),
('Béja', 36.7256, 9.1816, 'city'),
('Jendouba', 36.5014, 8.7800, 'city'),
('El Kef', 36.1699, 8.7049, 'city'),
('Mahdia', 35.5047, 11.0622, 'city'),
('Sidi Bouzid', 35.0381, 9.4858, 'city'),
('Tozeur', 33.9197, 8.1335, 'city'),

-- Universities
('Université de Tunis', 36.8485, 10.1856, 'university'),
('Université de Carthage', 36.8531, 10.3208, 'university'),
('Université de Sousse', 35.8467, 10.6411, 'university'),
('Université de Sfax', 34.7289, 10.7581, 'university'),
('INSAT', 36.8442, 10.1929, 'university'),
('ESPRIT', 36.8989, 10.1897, 'university'),

-- Airports
('Aéroport Tunis-Carthage', 36.8510, 10.2272, 'airport'),
('Aéroport Enfidha-Hammamet', 36.0758, 10.4386, 'airport'),
('Aéroport Monastir Habib Bourguiba', 35.7581, 10.7547, 'airport'),
('Aéroport international de Sfax-Thyna', 34.7178, 10.6911, 'airport'),

-- Hospitals
('Hôpital Charles Nicolle', 36.8089, 10.1658, 'hospital'),
('Hôpital La Rabta', 36.8156, 10.1742, 'hospital'),
('Hôpital Sahloul', 35.8367, 10.5944, 'hospital'),
('Hôpital Farhat Hached', 35.8289, 10.6378, 'hospital'),

-- Landmarks
('Médina de Tunis', 36.7981, 10.1712, 'landmark'),
('Sidi Bou Said', 36.8708, 10.3469, 'landmark'),
('Carthage', 36.8531, 10.3208, 'landmark'),
('Amphithéâtre d\'El Jem', 35.2969, 10.7061, 'landmark');

-- Create a function to search locations
CREATE OR REPLACE FUNCTION search_locations(search_term TEXT)
RETURNS TABLE(
    name TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    type TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.name,
        l.latitude,
        l.longitude,
        l.type
    FROM public.tunisia_locations l
    WHERE l.name ILIKE '%' || search_term || '%'
    ORDER BY 
        CASE 
            WHEN l.name ILIKE search_term || '%' THEN 1
            WHEN l.name ILIKE '%' || search_term || '%' THEN 2
            ELSE 3
        END,
        l.name;
END;
$$ LANGUAGE plpgsql;
