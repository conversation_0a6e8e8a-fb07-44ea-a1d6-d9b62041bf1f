from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

User = get_user_model()


class Ride(models.Model):
    """Model for ride offers"""

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic ride information
    driver = models.ForeignKey(User, on_delete=models.CASCADE, related_name='offered_rides')
    origin = models.CharField(max_length=255)
    destination = models.CharField(max_length=255)

    # Location coordinates
    origin_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    origin_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    destination_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    destination_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    # Time information
    departure_time = models.DateTimeField()
    arrival_time = models.DateTimeField()

    # Ride details
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    distance = models.CharField(max_length=50)  # e.g., "140 km"
    seats_available = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(8)]
    )
    total_seats = models.PositiveIntegerField(default=4)

    # Vehicle information
    car_model = models.CharField(max_length=100)
    car_color = models.CharField(max_length=50)

    # Additional features
    features = models.JSONField(default=list, blank=True)  # e.g., ["AC", "Music", "Non-smoking"]

    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    notes = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'rides'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['origin']),
            models.Index(fields=['destination']),
            models.Index(fields=['departure_time']),
            models.Index(fields=['status']),
            models.Index(fields=['driver']),
        ]

    def __str__(self):
        return f"{self.origin} → {self.destination} on {self.departure_time.strftime('%Y-%m-%d %H:%M')}"

    @property
    def seats_booked(self):
        """Number of seats currently booked"""
        return self.bookings.filter(status='confirmed').count()

    @property
    def seats_remaining(self):
        """Number of seats still available"""
        return self.seats_available - self.seats_booked

    def can_be_booked_by(self, user):
        """Check if a user can book this ride"""
        if user == self.driver:
            return False, "Cannot book your own ride"

        if self.status != 'active':
            return False, "Ride is not active"

        if self.seats_remaining <= 0:
            return False, "No seats available"

        # Check if user already booked this ride
        if self.bookings.filter(user=user, status='confirmed').exists():
            return False, "Already booked this ride"

        return True, "Can book"


class RideBooking(models.Model):
    """Model for ride bookings"""

    STATUS_CHOICES = [
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
    ]

    ride = models.ForeignKey(Ride, on_delete=models.CASCADE, related_name='bookings')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ride_bookings')
    seats_booked = models.PositiveIntegerField(default=1)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='confirmed')

    # Booking details
    pickup_location = models.CharField(max_length=255, blank=True)
    dropoff_location = models.CharField(max_length=255, blank=True)
    special_requests = models.TextField(blank=True)

    # Payment information
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    payment_method = models.CharField(max_length=50, default='cash')
    payment_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('paid', 'Paid'),
            ('refunded', 'Refunded'),
        ],
        default='pending'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ride_bookings'
        unique_together = ['ride', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.full_name} → {self.ride}"

    def calculate_amount(self):
        """Calculate the amount to be paid for this booking"""
        return self.ride.price * self.seats_booked

    def save(self, *args, **kwargs):
        """Override save to handle seat management"""
        is_new = self.pk is None
        old_status = None
        old_seats = 0

        if not is_new:
            # Get old values for comparison
            old_booking = RideBooking.objects.get(pk=self.pk)
            old_status = old_booking.status
            old_seats = old_booking.seats_booked

        super().save(*args, **kwargs)

        # Handle seat decrements/increments based on status changes
        if is_new and self.status == 'confirmed':
            # New confirmed booking - decrement seats
            self.ride.seats_available -= self.seats_booked
            self.ride.save()
        elif not is_new and old_status != self.status:
            if old_status == 'confirmed' and self.status == 'cancelled':
                # Booking cancelled - increment seats back
                self.ride.seats_available += old_seats
                self.ride.save()
            elif old_status != 'confirmed' and self.status == 'confirmed':
                # Booking confirmed - decrement seats
                self.ride.seats_available -= self.seats_booked
                self.ride.save()
        elif not is_new and self.status == 'confirmed' and old_seats != self.seats_booked:
            # Seats changed for confirmed booking
            seat_difference = self.seats_booked - old_seats
            self.ride.seats_available -= seat_difference
            self.ride.save()


class RideRequest(models.Model):
    """Model for ride requests (passengers looking for rides)"""

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('matched', 'Matched'),
        ('cancelled', 'Cancelled'),
    ]

    passenger = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ride_requests')
    origin = models.CharField(max_length=255)
    destination = models.CharField(max_length=255)

    # Location coordinates
    origin_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    origin_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    destination_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    destination_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    # Time preferences
    preferred_departure_time = models.DateTimeField()
    flexible_time = models.BooleanField(default=True)  # Can accept rides within ±2 hours

    # Passenger details
    passengers_count = models.PositiveIntegerField(default=1)
    max_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Preferences
    preferred_features = models.JSONField(default=list, blank=True)
    notes = models.TextField(blank=True)

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    matched_ride = models.ForeignKey(Ride, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ride_requests'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.passenger.full_name}: {self.origin} → {self.destination}"


class RidePending(models.Model):
    """Model for pending ride booking requests"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
    ]

    ride = models.ForeignKey(Ride, on_delete=models.CASCADE, related_name='pending_bookings')
    passenger = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pending_bookings')

    # Booking details
    seats_requested = models.PositiveIntegerField(default=1)
    pickup_location = models.CharField(max_length=255, blank=True)
    dropoff_location = models.CharField(max_length=255, blank=True)

    # Passenger information
    passenger_name = models.CharField(max_length=255)
    passenger_phone = models.CharField(max_length=20)
    special_requests = models.TextField(blank=True)

    # Status and response
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    driver_response = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    responded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'ride_pending'
        unique_together = ['ride', 'passenger']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['ride', 'status']),
            models.Index(fields=['passenger', 'status']),
            models.Index(fields=['status', '-created_at']),
        ]

    def __str__(self):
        return f"Pending: {self.passenger.full_name} → {self.ride}"

    def confirm_booking(self, driver_response=""):
        """Confirm the pending booking and create actual booking"""
        from django.utils import timezone

        # Check if ride still has available seats
        if self.ride.seats_remaining >= self.seats_requested:
            # Create actual booking
            booking = RideBooking.objects.create(
                ride=self.ride,
                user=self.passenger,
                seats_booked=self.seats_requested,
                pickup_location=self.pickup_location,
                dropoff_location=self.dropoff_location,
                status='confirmed'
            )

            # Update pending status
            self.status = 'confirmed'
            self.driver_response = driver_response
            self.responded_at = timezone.now()
            self.save()

            # Update ride seats (will be handled by booking save method)

            return booking
        else:
            # Reject if no seats available
            self.status = 'rejected'
            self.driver_response = "No seats available"
            self.responded_at = timezone.now()
            self.save()
            return None

    def reject_booking(self, driver_response=""):
        """Reject the pending booking"""
        from django.utils import timezone

        self.status = 'rejected'
        self.driver_response = driver_response
        self.responded_at = timezone.now()
        self.save()


class RideWaypoint(models.Model):
    """Model for intermediate stops in a ride"""

    ride = models.ForeignKey(Ride, on_delete=models.CASCADE, related_name='waypoints')
    location = models.CharField(max_length=255)

    latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    order = models.PositiveIntegerField()  # Order of the waypoint in the route
    estimated_arrival_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'ride_waypoints'
        ordering = ['order']
        unique_together = ['ride', 'order']

    def __str__(self):
        return f"{self.ride} - Stop {self.order}: {self.location}"
