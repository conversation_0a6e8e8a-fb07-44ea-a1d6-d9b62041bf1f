
import { useCallback } from "react";
import { supabase } from "@/lib/supabase";
import { Notification } from "@/data/rides";

export function useNotifications() {
  const addNotification = useCallback(async (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>): Promise<void> => {
    const notifData = {
      user_id: notification.userId,
      title: notification.title,
      content: notification.content,
      type: notification.type,
      related_id: notification.relatedId,
      timestamp: new Date().toISOString(),
      read: false
    };
    
    const { error } = await supabase
      .from("notifications")
      .insert([notifData]);
    
    if (error) {
      console.error("Error adding notification:", error);
    }
  }, []);
  
  const getNotifications = useCallback(async (userId?: string): Promise<Notification[]> => {
    if (!userId) return [];
    
    const { data, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .order("timestamp", { ascending: false });
    
    if (error) {
      console.error("Error fetching notifications:", error);
      return [];
    }
    
    return data.map(notif => ({
      id: notif.id,
      userId: notif.user_id,
      title: notif.title,
      content: notif.content,
      timestamp: notif.timestamp,
      read: notif.read,
      type: notif.type as 'ride_booked' | 'ride_canceled' | 'new_message' | 'system',
      relatedId: notif.related_id || undefined
    }));
  }, []);
  
  const markNotificationAsRead = useCallback(async (notificationId: string): Promise<void> => {
    const { error } = await supabase
      .from("notifications")
      .update({ read: true })
      .eq("id", notificationId);
    
    if (error) {
      console.error("Error marking notification as read:", error);
    }
  }, []);
  
  const hasUnreadNotifications = useCallback(async (userId?: string): Promise<boolean> => {
    if (!userId) return false;
    
    const { data, error } = await supabase
      .from("notifications")
      .select("count", { count: 'exact', head: true })
      .eq("user_id", userId)
      .eq("read", false);
    
    if (error) {
      console.error("Error checking unread notifications:", error);
      return false;
    }
    
    return (data?.count ?? 0) > 0;
  }, []);

  return {
    addNotification,
    getNotifications,
    markNotificationAsRead,
    hasUnreadNotifications
  };
}
