import { apiClient } from '@/lib/supabase';

export interface Conversation {
  id: string;
  participants: User[];
  last_message?: Message;
  unread_count: number;
  other_participant?: User;
  related_ride?: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  conversation: string;
  sender: User;
  content: string;
  is_read: boolean;
  created_at: string;
}

export interface User {
  id: string;
  username: string;
  full_name: string;
  email: string;
  avatar?: string;
}

class MessagingService {
  async getConversations(): Promise<Conversation[]> {
    try {
      const response = await apiClient.get('/messaging/conversations/');
      return response.results || response;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      return [];
    }
  }

  async createConversation(participantId: string): Promise<Conversation | null> {
    try {
      const response = await apiClient.post('/messaging/conversations/', {
        participant_id: participantId
      });
      return response;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  async getMessages(conversationId: string): Promise<Message[]> {
    try {
      const response = await apiClient.get(`/messaging/conversations/${conversationId}/messages/`);
      return response.results || response;
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  }

  async sendMessage(conversationId: string, content: string): Promise<Message | null> {
    try {
      const response = await apiClient.post(`/messaging/conversations/${conversationId}/messages/`, {
        content
      });
      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async markMessageAsRead(messageId: string): Promise<boolean> {
    try {
      await apiClient.post(`/messaging/messages/${messageId}/read/`, {});
      return true;
    } catch (error) {
      console.error('Error marking message as read:', error);
      return false;
    }
  }

  // Helper method to start a conversation with a driver
  async contactDriver(driverId: string, initialMessage: string): Promise<{ conversation: Conversation; message: Message } | null> {
    try {
      // First, create or get conversation
      const conversation = await this.createConversation(driverId);
      if (!conversation) {
        throw new Error('Failed to create conversation');
      }

      // Then send the initial message
      const message = await this.sendMessage(conversation.id, initialMessage);
      if (!message) {
        throw new Error('Failed to send message');
      }

      return { conversation, message };
    } catch (error) {
      console.error('Error contacting driver:', error);
      throw error;
    }
  }
}

export const messagingService = new MessagingService();
