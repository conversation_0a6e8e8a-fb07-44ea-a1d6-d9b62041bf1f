
import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Mail, ArrowLeft, Check } from "lucide-react";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetSent, setResetSent] = useState(false);
  const [verificationStep, setVerificationStep] = useState<'initial' | 'sent-code' | 'reset-password'>('initial');
  const [verificationCode, setVerificationCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const { addNotification } = useUser();
  const isRTL = language === "ar";

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error(translations.pleaseEnterEmail || "Please enter your email");
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setResetSent(true);
      setVerificationStep('sent-code');
      
      toast.success(translations.resetLinkSent, {
        description: translations.checkEmail,
      });
      
      // Add a notification for the user to check their email
      addNotification({
        userId: "user-123", // In a real app, this would be dynamic
        title: translations.resetLinkSent || "Password Reset Link Sent",
        content: translations.checkEmail || "Please check your email for a password reset link",
        type: "system",
        relatedId: email
      });
    }, 1500);
  };
  
  const handleVerifyCode = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (verificationCode.length !== 6) {
      toast.error(translations.invalidVerificationCode || "Please enter a valid verification code");
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call to verify code
    setTimeout(() => {
      setIsSubmitting(false);
      
      // In a real app, this would check the code against the one sent to the email
      if (verificationCode === "123456") {
        setVerificationStep('reset-password');
      } else {
        toast.error(translations.invalidVerificationCode || "Invalid verification code");
      }
    }, 1500);
  };
  
  const handleResetPassword = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPassword) {
      toast.error(translations.pleaseEnterPassword || "Please enter a new password");
      return;
    }
    
    if (newPassword !== confirmPassword) {
      toast.error(translations.passwordsDontMatch || "Passwords don't match");
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call to reset password
    setTimeout(() => {
      setIsSubmitting(false);
      
      toast.success(translations.passwordResetSuccess || "Password reset successful", {
        description: translations.loginWithNewPassword || "You can now login with your new password"
      });
      
      // Navigate to login page
      navigate("/login");
    }, 1500);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-1 flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
        <Card className="w-full max-w-md shadow-lg animate-fade-in">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">{translations.forgotPasswordTitle}</CardTitle>
            <CardDescription>{translations.forgotPasswordDescription}</CardDescription>
          </CardHeader>
          
          {verificationStep === 'initial' && (
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">{translations.email}</Label>
                  <div className="relative">
                    <Mail className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4`} />
                    <Input
                      id="email"
                      type="email"
                      placeholder={translations.emailPlaceholder}
                      className={`${isRTL ? 'pr-10' : 'pl-10'}`}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-3">
                <Button 
                  type="submit" 
                  className="w-full transition-all hover:scale-[1.01]" 
                  disabled={isSubmitting}
                >
                  {isSubmitting ? `${translations.resetPasswordButton}...` : translations.resetPasswordButton}
                </Button>
                <Link 
                  to="/login" 
                  className={`flex items-center gap-1 text-sm text-gray-500 hover:text-primary ${isRTL ? 'self-end' : 'self-start'}`}
                >
                  <ArrowLeft className="h-4 w-4" />
                  {translations.backToLogin}
                </Link>
              </CardFooter>
            </form>
          )}
          
          {verificationStep === 'sent-code' && (
            <form onSubmit={handleVerifyCode}>
              <CardContent className="space-y-4 text-center">
                <div className="mx-auto bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                  <Mail className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">{translations.resetLinkSent || "Verification Code Sent"}</h3>
                <p className="text-muted-foreground">
                  {translations.checkEmail || "Please check your email for the verification code"}
                </p>
                
                <div className="space-y-2 py-4">
                  <Label htmlFor="verificationCode" className="text-center block">
                    {translations.enterVerificationCode || "Enter verification code"}
                  </Label>
                  <div className="flex justify-center">
                    <InputOTP
                      maxLength={6}
                      value={verificationCode}
                      onChange={(value) => setVerificationCode(value)}
                      pattern="^[0-9]+$"
                      render={({ slots }) => (
                        <InputOTPGroup>
                          {slots.map((slot, index) => (
                            <InputOTPSlot key={index} index={index} />
                          ))}
                        </InputOTPGroup>
                      )}
                    />
                  </div>
                  <p className="text-center text-sm text-gray-500 mt-2">
                    {translations.didntReceiveCode || "Didn't receive a code?"}{" "}
                    <button 
                      type="button" 
                      className="text-primary hover:underline" 
                      onClick={handleSubmit}
                    >
                      {translations.resend || "Resend"}
                    </button>
                  </p>
                </div>
                
                <div className="flex space-x-3">
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => setVerificationStep('initial')}
                  >
                    {translations.back || "Back"}
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1" 
                    disabled={isSubmitting || verificationCode.length !== 6}
                  >
                    {isSubmitting ? `${translations.verifying || "Verifying"}...` : translations.verify || "Verify"}
                  </Button>
                </div>
              </CardContent>
            </form>
          )}
          
          {verificationStep === 'reset-password' && (
            <form onSubmit={handleResetPassword}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">{translations.newPassword || "New Password"}</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    placeholder="••••••••"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">{translations.confirmPassword || "Confirm Password"}</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                  />
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-3">
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isSubmitting}
                >
                  {isSubmitting ? `${translations.resettingPassword || "Resetting password"}...` : translations.resetPassword || "Reset Password"}
                </Button>
              </CardFooter>
            </form>
          )}
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default ForgotPasswordPage;
