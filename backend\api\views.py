from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response


@api_view(['GET'])
@permission_classes([AllowAny])
def api_info(request):
    """API information endpoint"""
    
    return Response({
        'message': 'CoJourneyHub API',
        'version': '1.0.0',
        'status': 'active',
        'endpoints': {
            'auth': '/api/auth/',
            'rides': '/api/rides/',
            'locations': '/api/locations/',
            'messaging': '/api/messaging/',
            'notifications': '/api/notifications/',
            'reviews': '/api/reviews/',
            'admin': '/admin/',
        },
        'documentation': 'Visit individual endpoints for more details'
    })
