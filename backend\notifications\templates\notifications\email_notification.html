<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - CoJourneyHub</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .notification-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 20px;
        }
        .ride-booked { background-color: #dcfce7; color: #166534; }
        .booking-cancelled { background-color: #fef2f2; color: #dc2626; }
        .ride-reminder { background-color: #fef3c7; color: #d97706; }
        .system { background-color: #e0e7ff; color: #3730a3; }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>CoJourneyHub</h1>
        <p>Your ride-sharing companion</p>
    </div>
    
    <div class="content">
        <span class="notification-type {{ notification_type }}">{{ notification_type|title }}</span>
        
        <h2>{{ title }}</h2>
        
        <p>Hello {{ user.full_name }},</p>
        
        <p>{{ content }}</p>
        
        <a href="http://localhost:3000/notifications" class="button">View in App</a>
    </div>
    
    <div class="footer">
        <p>This email was sent to {{ user.email }} because you have notifications enabled.</p>
        <p>You can manage your notification preferences in your account settings.</p>
        <p>&copy; 2024 CoJourneyHub. All rights reserved.</p>
    </div>
</body>
</html>
