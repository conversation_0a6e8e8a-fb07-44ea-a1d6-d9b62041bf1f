@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.5% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors;
  }
  
  /* RTL Support */
  html[dir="rtl"] input[type="date"]::-webkit-calendar-picker-indicator,
  html[dir="rtl"] input[type="time"]::-webkit-calendar-picker-indicator {
    right: auto;
    left: 8px;
  }
  
  html[dir="rtl"] .rtl-flip {
    transform: scaleX(-1);
  }
  
  html[dir="rtl"] .form-message {
    text-align: right;
  }
  
  /* Improved text visibility in dark mode */
  .dark .text-gray-700 {
    @apply text-gray-200;
  }
  
  .dark .text-gray-500 {
    @apply text-gray-300;
  }
  
  .dark .text-gray-600 {
    @apply text-gray-300;
  }
  
  .dark .text-gray-400 {
    @apply text-gray-300;
  }
  
  .dark .text-muted-foreground {
    @apply text-opacity-85;
  }
  
  /* Ensure good contrast for navigation links */
  .dark a:not(.text-primary) {
    @apply text-gray-300 hover:text-white;
  }
  
  /* Make sure buttons have visible text */
  button, a {
    @apply text-current;
  }
  
  /* Fix for reward cards in dark mode */
  .dark .card-description {
    @apply text-gray-300;
  }
  
  /* Fix for loyalty points text */
  .truncate-text {
    @apply truncate max-w-full;
  }
  
  .points-needed {
    @apply truncate max-w-[150px];
  }
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Smooth transitions for theme changes */
html {
  transition: background-color 0.2s ease;
}

/* Nicer forms for RTL languages */
.rtl input, .rtl textarea, .rtl select {
  text-align: right;
}

/* Fix for navigation links to ensure visibility */
.nav-link {
  @apply text-gray-700 dark:text-gray-300;
}

/* Fix for offer ride button */
a[href="/offer-ride"], a[href="/offer-ride"] span {
  @apply text-visible dark:text-visible-dark;
}

/* Enhanced animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fade-in 0.5s ease forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease forwards;
}

/* Hover effects */
.hover-scale {
  @apply transition-transform duration-200;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-lift {
  @apply transition-all duration-200;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Card and button enhancements */
.card-gradient {
  background: linear-gradient(135deg, hsl(var(--card) / 0.9), hsl(var(--card)));
  backdrop-filter: blur(10px);
}

.btn-gradient {
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(90deg, hsl(var(--primary) / 0.9), hsl(var(--primary)));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
