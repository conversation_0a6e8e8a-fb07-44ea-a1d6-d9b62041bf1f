
import { useState, useEffect } from "react";
import { useUser } from "@/contexts/UserContext";
import { Bell, X } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Notification } from "@/data/rides";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON>etT<PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";

const NotificationCenter = () => {
  const { getNotifications, markNotificationAsRead, hasUnreadNotifications } = useUser();
  const { translations, language } = useLanguage();
  const [open, setOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [hasUnread, setHasUnread] = useState(false);
  const isRTL = language === "ar";
  
  // Fetch notifications and unread status when component mounts or when the sheet opens
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const notifs = await getNotifications();
        setNotifications(notifs);
        
        const unreadStatus = await hasUnreadNotifications();
        setHasUnread(unreadStatus);
      } catch (error) {
        console.error("Error fetching notifications:", error);
        setNotifications([]);
      }
    };
    
    fetchNotifications();
    // Refresh when the sheet opens
    if (open) {
      fetchNotifications();
    }
  }, [getNotifications, hasUnreadNotifications, open]);
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return translations.justNow || "Just now";
    if (diffMins < 60) return `${diffMins} ${translations.minutesAgo || "minutes ago"}`;
    if (diffHours < 24) return `${diffHours} ${translations.hoursAgo || "hours ago"}`;
    if (diffDays === 1) return translations.yesterday || "Yesterday";
    return date.toLocaleDateString();
  };
  
  const handleMarkAsRead = async (id: string) => {
    await markNotificationAsRead(id);
    // Refresh notifications list
    const updatedNotifications = await getNotifications();
    setNotifications(updatedNotifications);
    
    // Check if there are still unread notifications
    const unreadStatus = await hasUnreadNotifications();
    setHasUnread(unreadStatus);
  };
  
  const handleMarkAllAsRead = async () => {
    const markPromises = notifications
      .filter(notification => !notification.read)
      .map(notification => markNotificationAsRead(notification.id));
      
    await Promise.all(markPromises);
    
    // Refresh notifications
    const updatedNotifications = await getNotifications();
    setNotifications(updatedNotifications);
    setHasUnread(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {hasUnread && (
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent side={isRTL ? "left" : "right"} className={isRTL ? "text-right" : ""}>
        <SheetHeader className="flex flex-row items-center justify-between">
          <SheetTitle>{translations.notifications || "Notifications"}</SheetTitle>
          {notifications.some(n => !n.read) && (
            <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead}>
              {translations.markAllAsRead || "Mark all as read"}
            </Button>
          )}
        </SheetHeader>
        
        <ScrollArea className="h-[calc(100vh-5rem)] mt-6 pr-4">
          {notifications.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              {translations.noNotifications || "No notifications yet"}
            </div>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <Card 
                  key={notification.id} 
                  className={`p-4 ${!notification.read ? "border-l-4 border-l-primary" : ""}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{notification.title}</h4>
                      <p className="text-sm text-gray-500 mt-1">{notification.content}</p>
                      <p className="text-xs text-gray-400 mt-2">{formatTime(notification.timestamp)}</p>
                    </div>
                    {!notification.read && (
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6" 
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
};

export default NotificationCenter;
