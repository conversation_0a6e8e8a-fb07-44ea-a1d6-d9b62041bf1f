import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/use-auth';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';
import { User, Vehicle } from '@/types/user';
import { apiClient } from '@/lib/supabase';
import { 
  User as UserIcon, 
  Car, 
  Shield, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  Edit,
  Save,
  Plus,
  Trash2
} from 'lucide-react';

const ProfileManagement = () => {
  const { user, setUser } = useUser();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [editedUser, setEditedUser] = useState<Partial<User>>(user || {});
  const [newVehicle, setNewVehicle] = useState<Partial<Vehicle>>({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    color: '',
    licensePlate: '',
    vehicleType: 'sedan',
    seats: 4,
    hasAC: true,
    hasMusic: true,
    isSmokingAllowed: false,
    hasWifi: false,
    isActive: true
  });

  const handleSaveProfile = async () => {
    if (!user) return;

    setSaving(true);
    try {
      // Update profile via Django API
      const response = await apiClient.patch('/auth/profile/', {
        full_name: editedUser.fullName,
        phone_number: editedUser.phoneNumber,
        city: editedUser.city,
        country: editedUser.country,
        bio: editedUser.bio,
        date_of_birth: editedUser.dateOfBirth,
        role: editedUser.role
      });

      // Update local user state
      const updatedUser = { ...user, ...editedUser };
      setUser(updatedUser);
      
      toast.success('Profile updated successfully!');
      setIsEditing(false);
    } catch (error: any) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleAddVehicle = async () => {
    if (!user || !newVehicle.make || !newVehicle.model || !newVehicle.licensePlate) {
      toast.error('Please fill in all required vehicle fields');
      return;
    }

    try {
      const response = await apiClient.post('/vehicles/', {
        make: newVehicle.make,
        model: newVehicle.model,
        year: newVehicle.year,
        color: newVehicle.color,
        license_plate: newVehicle.licensePlate,
        vehicle_type: newVehicle.vehicleType,
        seats: newVehicle.seats,
        has_ac: newVehicle.hasAC,
        has_music: newVehicle.hasMusic,
        is_smoking_allowed: newVehicle.isSmokingAllowed,
        has_wifi: newVehicle.hasWifi,
        is_active: newVehicle.isActive
      });

      // Update user vehicles
      const updatedUser = {
        ...user,
        vehicles: [...(user.vehicles || []), response]
      };
      setUser(updatedUser);

      // Reset form
      setNewVehicle({
        make: '',
        model: '',
        year: new Date().getFullYear(),
        color: '',
        licensePlate: '',
        vehicleType: 'sedan',
        seats: 4,
        hasAC: true,
        hasMusic: true,
        isSmokingAllowed: false,
        hasWifi: false,
        isActive: true
      });

      toast.success('Vehicle added successfully!');
    } catch (error: any) {
      console.error('Error adding vehicle:', error);
      toast.error('Failed to add vehicle');
    }
  };

  const handleDeleteVehicle = async (vehicleId: string) => {
    if (!user) return;

    try {
      await apiClient.delete(`/vehicles/${vehicleId}/`);
      
      // Update user vehicles
      const updatedUser = {
        ...user,
        vehicles: user.vehicles?.filter(v => v.id !== vehicleId) || []
      };
      setUser(updatedUser);

      toast.success('Vehicle deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting vehicle:', error);
      toast.error('Failed to delete vehicle');
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6">
          <p>Please log in to view your profile.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            Profile Management
          </CardTitle>
          <Button
            variant={isEditing ? "default" : "outline"}
            onClick={() => {
              if (isEditing) {
                handleSaveProfile();
              } else {
                setIsEditing(true);
                setEditedUser(user);
              }
            }}
            disabled={isSaving}
          >
            {isEditing ? (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="personal" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="personal">Personal Info</TabsTrigger>
              <TabsTrigger value="vehicles">Vehicles</TabsTrigger>
              <TabsTrigger value="preferences">Preferences</TabsTrigger>
            </TabsList>

            <TabsContent value="personal" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    value={isEditing ? editedUser.fullName || '' : user.fullName || ''}
                    onChange={(e) => setEditedUser({ ...editedUser, fullName: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    value={user.email}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    value={isEditing ? editedUser.phoneNumber || '' : user.phoneNumber || ''}
                    onChange={(e) => setEditedUser({ ...editedUser, phoneNumber: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select
                    value={isEditing ? editedUser.role || 'passenger' : user.role || 'passenger'}
                    onValueChange={(value) => setEditedUser({ ...editedUser, role: value as any })}
                    disabled={!isEditing}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="passenger">Passenger</SelectItem>
                      <SelectItem value="driver">Driver</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={isEditing ? editedUser.city || '' : user.city || ''}
                    onChange={(e) => setEditedUser({ ...editedUser, city: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={isEditing ? editedUser.country || '' : user.country || ''}
                    onChange={(e) => setEditedUser({ ...editedUser, country: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={isEditing ? editedUser.bio || '' : user.bio || ''}
                  onChange={(e) => setEditedUser({ ...editedUser, bio: e.target.value })}
                  disabled={!isEditing}
                  rows={3}
                />
              </div>
              
              <div className="flex items-center gap-4 pt-4">
                <Badge variant={user.isVerified ? "default" : "secondary"}>
                  <Shield className="h-3 w-3 mr-1" />
                  {user.isVerified ? 'Verified' : 'Not Verified'}
                </Badge>
                <Badge variant="outline">
                  <UserIcon className="h-3 w-3 mr-1" />
                  {user.role || 'passenger'}
                </Badge>
              </div>
            </TabsContent>

            <TabsContent value="vehicles" className="space-y-4">
              {/* Existing Vehicles */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Your Vehicles</h3>
                {user.vehicles && user.vehicles.length > 0 ? (
                  user.vehicles.map((vehicle) => (
                    <Card key={vehicle.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold">
                              {vehicle.year} {vehicle.make} {vehicle.model}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {vehicle.color} • {vehicle.licensePlate} • {vehicle.seats} seats
                            </p>
                            <div className="flex gap-2 mt-2">
                              {vehicle.hasAC && <Badge variant="secondary">AC</Badge>}
                              {vehicle.hasMusic && <Badge variant="secondary">Music</Badge>}
                              {vehicle.hasWifi && <Badge variant="secondary">WiFi</Badge>}
                              {!vehicle.isSmokingAllowed && <Badge variant="secondary">Non-Smoking</Badge>}
                            </div>
                          </div>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteVehicle(vehicle.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <p className="text-gray-500">No vehicles added yet.</p>
                )}
              </div>

              {/* Add New Vehicle */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plus className="h-5 w-5" />
                    Add New Vehicle
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="make">Make</Label>
                      <Input
                        id="make"
                        value={newVehicle.make}
                        onChange={(e) => setNewVehicle({ ...newVehicle, make: e.target.value })}
                        placeholder="e.g., Toyota"
                      />
                    </div>
                    <div>
                      <Label htmlFor="model">Model</Label>
                      <Input
                        id="model"
                        value={newVehicle.model}
                        onChange={(e) => setNewVehicle({ ...newVehicle, model: e.target.value })}
                        placeholder="e.g., Corolla"
                      />
                    </div>
                    <div>
                      <Label htmlFor="year">Year</Label>
                      <Input
                        id="year"
                        type="number"
                        value={newVehicle.year}
                        onChange={(e) => setNewVehicle({ ...newVehicle, year: parseInt(e.target.value) })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="color">Color</Label>
                      <Input
                        id="color"
                        value={newVehicle.color}
                        onChange={(e) => setNewVehicle({ ...newVehicle, color: e.target.value })}
                        placeholder="e.g., White"
                      />
                    </div>
                    <div>
                      <Label htmlFor="licensePlate">License Plate</Label>
                      <Input
                        id="licensePlate"
                        value={newVehicle.licensePlate}
                        onChange={(e) => setNewVehicle({ ...newVehicle, licensePlate: e.target.value })}
                        placeholder="e.g., 123 TUN 456"
                      />
                    </div>
                    <div>
                      <Label htmlFor="seats">Seats</Label>
                      <Input
                        id="seats"
                        type="number"
                        min="2"
                        max="8"
                        value={newVehicle.seats}
                        onChange={(e) => setNewVehicle({ ...newVehicle, seats: parseInt(e.target.value) })}
                      />
                    </div>
                  </div>
                  <Button onClick={handleAddVehicle} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Vehicle
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preferences" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Account Statistics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span>Rides Offered:</span>
                      <span className="font-semibold">{user.ridesOffered}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Rides Completed:</span>
                      <span className="font-semibold">{user.ridesCompleted}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Loyalty Points:</span>
                      <span className="font-semibold">{user.loyaltyPoints}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Rating:</span>
                      <span className="font-semibold">{user.rating?.toFixed(1) || 'N/A'}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Account Info</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span>{user.email}</span>
                    </div>
                    {user.phoneNumber && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        <span>{user.phoneNumber}</span>
                      </div>
                    )}
                    {user.city && (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>{user.city}, {user.country}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>Joined {new Date(user.joinDate).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileManagement;
