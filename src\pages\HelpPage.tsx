
import PageContainer from "@/components/PageContainer";
import { useLanguage } from "@/contexts/LanguageContext";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

const HelpPage = () => {
  const { translations } = useLanguage();
  
  const faqs = [
    {
      question: "How do I offer a ride?",
      answer: "To offer a ride, log into your account, click on 'Offer a Ride' in the navigation menu, and fill out the form with your journey details including origin, destination, date, time, available seats, and price per seat."
    },
    {
      question: "How do I book a ride?",
      answer: "To book a ride, search for available rides by entering your origin, destination, and date. Browse the results, select a ride that matches your needs, and click 'Book Now'. You'll be prompted to confirm the booking details before finalizing."
    },
    {
      question: "What happens if my ride is canceled?",
      answer: "If a driver cancels a ride, you'll receive an immediate notification and any payment made will be refunded. If you need to cancel as a passenger, please do so at least 12 hours in advance to avoid any penalties."
    },
    {
      question: "How does the loyalty program work?",
      answer: "You earn loyalty points for various actions: offering rides (50 points), completing rides as a passenger (20 points), and referring friends (100 points per friend who joins). These points can be redeemed for rewards like ride discounts and university merchandise."
    },
    {
      question: "Is my personal information secure?",
      answer: "Yes, we prioritize the security of your data. We only share necessary information between riders and drivers to facilitate rides. Your payment information, full contact details, and personal data are protected in accordance with our Privacy Policy."
    },
    {
      question: "Can I use CoSesameHub if I'm not a Sesame University student?",
      answer: "Currently, CoSesameHub is exclusively for Sesame University students. You need a valid university email address to register and use our services."
    },
    {
      question: "How do payments work?",
      answer: "Payments are arranged directly between drivers and passengers. We recommend agreeing on payment terms before the ride begins. In the future, we plan to implement an in-app payment system for greater convenience and security."
    },
    {
      question: "What if I have an issue with another user?",
      answer: "If you encounter any problems with another user, please use the 'Report Issue' button on their profile or on your ride details page. Our team will investigate the matter promptly and take appropriate action according to our community guidelines."
    }
  ];

  return (
    <PageContainer title={translations.helpCenterTitle || "Help Center"} subtitle="Find answers to your questions">
      <div className="space-y-8">
        <div className="relative">
          <Input 
            placeholder="Search for answers..." 
            className="pl-10 py-6 text-lg"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-5 w-5" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button variant="outline" className="py-8 hover-lift flex flex-col h-auto space-y-2 animate-fade-in">
            <span className="text-lg font-semibold">Getting Started</span>
            <span className="text-sm text-gray-500">Account setup, basics</span>
          </Button>
          <Button variant="outline" className="py-8 hover-lift flex flex-col h-auto space-y-2 animate-fade-in" style={{animationDelay: "0.1s"}}>
            <span className="text-lg font-semibold">Booking Rides</span>
            <span className="text-sm text-gray-500">Search, book, cancel</span>
          </Button>
          <Button variant="outline" className="py-8 hover-lift flex flex-col h-auto space-y-2 animate-fade-in" style={{animationDelay: "0.2s"}}>
            <span className="text-lg font-semibold">Offering Rides</span>
            <span className="text-sm text-gray-500">Create, manage rides</span>
          </Button>
        </div>
        
        <div className="mt-10">
          <h2 className="text-2xl font-bold mb-6 dark:text-white">Frequently Asked Questions</h2>
          <Accordion type="single" collapsible className="border rounded-lg">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="px-4 hover:no-underline hover:bg-gray-50 dark:hover:bg-gray-800 text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1 text-gray-700 dark:text-gray-300">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
        
        <div className="mt-10 bg-primary/10 p-6 rounded-lg flex flex-col md:flex-row items-center justify-between gap-4">
          <div>
            <h3 className="text-xl font-bold mb-2 dark:text-white">Still need help?</h3>
            <p className="text-gray-700 dark:text-gray-300">Contact our support team and we'll get back to you within 24 hours.</p>
          </div>
          <Button>Contact Support</Button>
        </div>
      </div>
    </PageContainer>
  );
};

export default HelpPage;
