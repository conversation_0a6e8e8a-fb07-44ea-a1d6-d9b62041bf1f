import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from accounts.models import UserProfile, Vehicle

User = get_user_model()


class UserModelTest(TestCase):
    """Test User model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            full_name='Test User'
        )
    
    def test_user_creation(self):
        """Test user creation"""
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.full_name, 'Test User')
        self.assertEqual(self.user.loyalty_points, 100)
        self.assertEqual(self.user.rating, 5.0)
    
    def test_user_str(self):
        """Test user string representation"""
        self.assertEqual(str(self.user), '<EMAIL>')
    
    def test_ride_count_property(self):
        """Test ride_count property"""
        self.user.rides_offered = 5
        self.user.rides_completed = 3
        self.user.save()
        self.assertEqual(self.user.ride_count, 8)
    
    def test_add_loyalty_points(self):
        """Test adding loyalty points"""
        initial_points = self.user.loyalty_points
        self.user.add_loyalty_points(50)
        self.assertEqual(self.user.loyalty_points, initial_points + 50)


class UserRegistrationAPITest(APITestCase):
    """Test user registration API"""
    
    def test_user_registration_success(self):
        """Test successful user registration"""
        url = reverse('user-register')
        data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'full_name': 'New User',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
            'city': 'Tunis'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('user', response.data)
        self.assertIn('tokens', response.data)
        
        # Check user was created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.full_name, 'New User')
        self.assertEqual(user.city, 'Tunis')
    
    def test_user_registration_password_mismatch(self):
        """Test registration with password mismatch"""
        url = reverse('user-register')
        data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'password': 'newpass123',
            'password_confirm': 'differentpass',
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email"""
        User.objects.create_user(
            email='<EMAIL>',
            username='existing',
            password='pass123'
        )
        
        url = reverse('user-register')
        data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class UserLoginAPITest(APITestCase):
    """Test user login API"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
    
    def test_user_login_success(self):
        """Test successful user login"""
        url = reverse('user-login')
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
        self.assertIn('tokens', response.data)
    
    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        url = reverse('user-login')
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class VehicleModelTest(TestCase):
    """Test Vehicle model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='driver',
            password='pass123'
        )
        self.vehicle = Vehicle.objects.create(
            owner=self.user,
            make='Toyota',
            model='Corolla',
            year=2020,
            color='White',
            license_plate='123TUN456',
            seats=4
        )
    
    def test_vehicle_creation(self):
        """Test vehicle creation"""
        self.assertEqual(self.vehicle.owner, self.user)
        self.assertEqual(self.vehicle.make, 'Toyota')
        self.assertEqual(self.vehicle.model, 'Corolla')
        self.assertEqual(self.vehicle.year, 2020)
    
    def test_vehicle_str(self):
        """Test vehicle string representation"""
        expected = "2020 Toyota Corolla (123TUN456)"
        self.assertEqual(str(self.vehicle), expected)
    
    def test_vehicle_full_name_property(self):
        """Test vehicle full_name property"""
        expected = "2020 Toyota Corolla"
        self.assertEqual(self.vehicle.full_name, expected)


class VehicleAPITest(APITestCase):
    """Test Vehicle API"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='driver',
            password='pass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_create_vehicle(self):
        """Test creating a vehicle"""
        url = reverse('vehicle-list-create')
        data = {
            'make': 'Peugeot',
            'model': '308',
            'year': 2019,
            'color': 'Blue',
            'license_plate': '789TUN012',
            'seats': 5,
            'has_ac': True,
            'has_music': True
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        vehicle = Vehicle.objects.get(license_plate='789TUN012')
        self.assertEqual(vehicle.owner, self.user)
        self.assertEqual(vehicle.make, 'Peugeot')
    
    def test_list_user_vehicles(self):
        """Test listing user's vehicles"""
        Vehicle.objects.create(
            owner=self.user,
            make='Toyota',
            model='Corolla',
            year=2020,
            color='White',
            license_plate='123TUN456'
        )
        
        url = reverse('vehicle-list-create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
