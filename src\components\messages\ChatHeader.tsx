
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Contact } from "@/hooks/use-message-center";

interface ChatHeaderProps {
  contact: Contact | undefined;
}

const ChatHeader = ({ contact }: ChatHeaderProps) => {
  if (!contact) return null;
  
  return (
    <div className="border-b pb-4 mb-4">
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={contact.avatar} />
          <AvatarFallback>
            {contact.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className="font-medium">{contact.name}</h3>
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
