
import PageContainer from "@/components/PageContainer";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Car, DollarSign, Leaf, Globe, Clock, Users } from "lucide-react";

const AboutPage = () => {
  const { translations } = useLanguage();
  
  return (
    <PageContainer title={translations.aboutUsTitle || "About Us"} subtitle={translations.aboutUsIntro || "Welcome to CoSesameHub"}>
      <div className="prose prose-lg dark:prose-invert max-w-none">
        <p className="lead">CoSesameHub is a ridesharing platform specifically designed for Sesame University students. Our mission is to make transportation between campus and cities across Tunisia more accessible, affordable, and environmentally friendly.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 my-8 not-prose">
          <Card className="bg-gradient-to-br from-primary/10 to-primary/5 hover:shadow-md transition-all hover:-translate-y-1">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <Car className="h-10 w-10 text-primary mb-4" />
              <h3 className="text-xl font-semibold mb-2">Convenient Travel</h3>
              <p>Easy access to rides between campus and cities across Tunisia</p>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-primary/10 to-primary/5 hover:shadow-md transition-all hover:-translate-y-1 animate-fade-in" style={{animationDelay: "0.1s"}}>
            <CardContent className="p-6 flex flex-col items-center text-center">
              <DollarSign className="h-10 w-10 text-primary mb-4" />
              <h3 className="text-xl font-semibold mb-2">Cost-Effective</h3>
              <p>Share travel expenses and save money on every journey</p>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-primary/10 to-primary/5 hover:shadow-md transition-all hover:-translate-y-1 animate-fade-in" style={{animationDelay: "0.2s"}}>
            <CardContent className="p-6 flex flex-col items-center text-center">
              <Leaf className="h-10 w-10 text-primary mb-4" />
              <h3 className="text-xl font-semibold mb-2">Eco-Friendly</h3>
              <p>Reduce carbon emissions by sharing rides instead of driving solo</p>
            </CardContent>
          </Card>
        </div>
        
        <h2>Our Story</h2>
        <p>Founded in 2023, CoSesameHub was born from a simple observation: many Sesame students were traveling the same routes but in separate vehicles. We saw an opportunity to create a community-based solution that would help students save money, reduce their carbon footprint, and connect with their peers.</p>
        
        <h2>Our Mission</h2>
        <p>We are committed to:</p>
        <ul>
          <li>Connecting Sesame University students for safe and affordable travel</li>
          <li>Building a trusted community of drivers and passengers</li>
          <li>Reducing the number of vehicles on the road to decrease traffic and pollution</li>
          <li>Creating opportunities for students to make new connections</li>
        </ul>
        
        <div className="my-12 py-10 border-y border-gray-200 dark:border-gray-700 grid grid-cols-1 md:grid-cols-4 gap-6 not-prose text-center">
          <div className="flex flex-col items-center">
            <span className="text-4xl font-bold text-primary">2000+</span>
            <span className="text-gray-600 dark:text-gray-400">Active Users</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-4xl font-bold text-primary">15000+</span>
            <span className="text-gray-600 dark:text-gray-400">Rides Completed</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-4xl font-bold text-primary">8500+</span>
            <span className="text-gray-600 dark:text-gray-400">Hours Saved</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-4xl font-bold text-primary">20+</span>
            <span className="text-gray-600 dark:text-gray-400">Cities Connected</span>
          </div>
        </div>
        
        <h2>Why Choose CoSesameHub?</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4 not-prose">
          <div className="flex items-start gap-3">
            <Users className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="text-lg font-semibold mb-1">Exclusive Community</h3>
              <p className="text-gray-700 dark:text-gray-300">Unlike general ridesharing services, CoSesameHub is exclusively for Sesame University students, creating a trusted network.</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <Clock className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="text-lg font-semibold mb-1">Flexibility</h3>
              <p className="text-gray-700 dark:text-gray-300">Find rides that match your schedule, whether you're commuting to campus daily or traveling home for breaks.</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <Globe className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="text-lg font-semibold mb-1">Connect & Network</h3>
              <p className="text-gray-700 dark:text-gray-300">Meet fellow students from different departments and build your network while traveling.</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <DollarSign className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="text-lg font-semibold mb-1">Rewards Program</h3>
              <p className="text-gray-700 dark:text-gray-300">Earn loyalty points for offering and taking rides, redeemable for valuable rewards.</p>
            </div>
          </div>
        </div>
        
        <h2 className="mt-8">Join the Movement</h2>
        <p>Whether you're offering rides or looking for a lift, CoSesameHub makes it easy to get where you need to go while building community connections. Join us in creating a more connected, sustainable campus transportation network.</p>
        
        <div className="bg-primary/10 p-6 rounded-lg mt-8 not-prose">
          <h3 className="text-xl font-semibold mb-2">Ready to get started?</h3>
          <p className="mb-4">Sign up today and join thousands of Sesame students already using CoSesameHub.</p>
          <div className="flex gap-4 flex-wrap">
            <a href="/signup" className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">Create an account</a>
            <a href="/how-it-works" className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">Learn more</a>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default AboutPage;
