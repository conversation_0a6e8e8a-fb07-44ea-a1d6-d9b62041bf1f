
import { useState, useEffect } from "react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ride } from "@/data/rides";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, MapPin, Clock, Users, Car, Phone, MessageCircle, ChevronDown, ChevronUp } from "lucide-react";
import ChatInterface from "./ChatInterface";
import UserProfileDialog from "./UserProfileDialog";
import { apiService } from "@/services/apiService";

interface RideCardProps {
  ride: Ride;
}

const RideCard = ({ ride }: RideCardProps) => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useUser();
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  const [chatOpen, setChatOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isBooked, setIsBooked] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [rideDetails, setRideDetails] = useState(null);

  // Fetch detailed ride information when component mounts
  useEffect(() => {
    const fetchRideDetails = async () => {
      try {
        const details = await apiService.getRide(ride.id);
        setRideDetails(details);
      } catch (error) {
        console.error("Error fetching ride details:", error);
      }
    };

    fetchRideDetails();
  }, [ride.id]);

  const handleBookRide = async () => {
    if (!isAuthenticated) {
      toast.info(translations.loginRequiredForBooking || "Please login to book this ride");
      navigate("/login?redirect=search");
      return;
    }

    // Check user role - only passengers can book rides
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    if (userData.role !== 'passenger') {
      toast.error("Only passengers can book rides");
      return;
    }

    // Navigate to booking page with pending booking flow
    navigate(`/rides/${ride.id}/book`);
  };

  const handleContactDriver = () => {
    if (!isAuthenticated) {
      toast.info(translations.loginRequiredForContact || "Please login to contact the driver");
      navigate("/login?redirect=search");
      return;
    }

    setChatOpen(true);
  };

  const handleViewProfile = () => {
    setShowUserProfile(true);

    // Create a user profile with only the properties that exist
    const userProfile = {
      id: ride.driverId || `driver-${Date.now()}`, // Fallback to a generated ID if not available
      name: ride.driver.name,
      avatar: ride.driver.avatar,
      rating: ride.driver.rating || 4.5,
      joinDate: ride.driver.joinDate || new Date().toISOString(),
      ridesOffered: ride.driver.ridesOffered || ride.driver.rideCount || 0,
      ridesCompleted: ride.driver.ridesCompleted || ride.driver.rideCount || 0
    };

    setSelectedUser(userProfile);
    setProfileOpen(true);
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card className="overflow-hidden transition-all hover:shadow-lg cursor-pointer">
      <CardContent className="p-0">
        {/* Main Card Content */}
        <div className="p-4" onClick={() => setExpanded(!expanded)}>
          <div className="flex items-start gap-4">
            {/* Driver Avatar and Info */}
            <div className="flex-shrink-0">
              <Avatar className="w-12 h-12">
                <AvatarImage src={rideDetails?.driver?.user?.avatar || ride.driver?.avatar} />
                <AvatarFallback>
                  {(rideDetails?.driver?.user?.full_name || ride.driver?.name || 'D').charAt(0)}
                </AvatarFallback>
              </Avatar>
            </div>

            {/* Ride Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <h3 className="font-semibold text-lg">
                    {rideDetails?.driver?.user?.full_name || ride.driver?.name || 'Driver'}
                  </h3>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span>{rideDetails?.driver?.user?.rating || ride.driver?.rating || 4.5}</span>
                    <span>•</span>
                    <Car className="w-4 h-4" />
                    <span>
                      {rideDetails?.vehicle?.make} {rideDetails?.vehicle?.model}
                      {rideDetails?.vehicle?.color && ` (${rideDetails.vehicle.color})`}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">
                    {ride.price} TND
                  </div>
                  <div className="text-sm text-gray-600">
                    <Users className="w-4 h-4 inline mr-1" />
                    {rideDetails?.seats_available || ride.seatsAvailable} seats
                  </div>
                </div>
              </div>

              {/* Route */}
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center gap-2 flex-1">
                  <MapPin className="w-4 h-4 text-green-600" />
                  <span className="font-medium">{ride.origin}</span>
                </div>
                <div className="text-gray-400">→</div>
                <div className="flex items-center gap-2 flex-1">
                  <MapPin className="w-4 h-4 text-red-600" />
                  <span className="font-medium">{ride.destination}</span>
                </div>
              </div>

              {/* Time and Date */}
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{formatTime(ride.departureTime)}</span>
                </div>
                <div>{formatDate(ride.departureTime)}</div>
              </div>

              {/* Expand/Collapse Button */}
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  {user?.role === 'passenger' && (
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleBookRide();
                      }}
                      className="px-4 py-2"
                    >
                      Request Booking
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleContactDriver();
                    }}
                  >
                    <MessageCircle className="w-4 h-4 mr-1" />
                    Message
                  </Button>
                </div>
                <Button variant="ghost" size="sm">
                  {expanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  <span className="ml-1">{expanded ? 'Less' : 'More'}</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Expanded Content */}
          {expanded && (
            <div className="border-t bg-gray-50 p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Driver Details */}
                <div>
                  <h4 className="font-semibold mb-2">Driver Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      <span>{rideDetails?.driver?.user?.phone_number || 'Not provided'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Car className="w-4 h-4" />
                      <span>
                        {rideDetails?.vehicle?.year} {rideDetails?.vehicle?.make} {rideDetails?.vehicle?.model}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        License: {rideDetails?.vehicle?.license_plate || 'N/A'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">
                        {rideDetails?.driver?.total_rides_offered || 0} rides offered
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Ride Details */}
                <div>
                  <h4 className="font-semibold mb-2">Ride Details</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>Departure:</strong> {formatDate(ride.departureTime)} at {formatTime(ride.departureTime)}
                    </div>
                    <div>
                      <strong>Distance:</strong> {ride.distance || 'Calculating...'}
                    </div>
                    <div>
                      <strong>Duration:</strong> {ride.duration || 'Calculating...'}
                    </div>
                    <div>
                      <strong>Price per seat:</strong> {ride.price} TND
                    </div>
                    {rideDetails?.notes && (
                      <div>
                        <strong>Notes:</strong> {rideDetails.notes}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Map placeholder */}
              <div className="mt-4">
                <div className="bg-gray-200 h-32 rounded-lg flex items-center justify-center text-gray-500">
                  <MapPin className="w-6 h-6 mr-2" />
                  Route Map (Coming Soon)
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>

      {/* Chat dialog content */}
      <Dialog open={chatOpen} onOpenChange={setChatOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{translations.chatWithDriver || "Chat with Driver"}</DialogTitle>
            <DialogDescription>
              {translations.chatWithDriverDesc || "Send a message to the driver about this ride"}
            </DialogDescription>
          </DialogHeader>
          {ride.driverId && (
            <ChatInterface recipientId={ride.driverId} recipientName={ride.driver.name} />
          )}
        </DialogContent>
      </Dialog>

      {/* User profile dialog */}
      {showUserProfile && (
        <UserProfileDialog
          open={profileOpen}
          onOpenChange={setProfileOpen}
          user={selectedUser}
        />
      )}
    </Card>
  );
};

export default RideCard;
