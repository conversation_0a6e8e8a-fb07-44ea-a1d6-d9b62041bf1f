
import { useState, useEffect } from "react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Ride } from "@/data/rides";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import ChatInterface from "./ChatInterface";
import UserProfileDialog from "./UserProfileDialog";
import { DriverProfile } from "./ride/DriverProfile";
import { RideDetails } from "./ride/RideDetails";
import { RidePricing } from "./ride/RidePricing";
import { RideActions } from "./ride/RideActions";

interface RideCardProps {
  ride: Ride;
}

const RideCard = ({ ride }: RideCardProps) => {
  const navigate = useNavigate();
  const { user, isAuthenticated, addLoyaltyPoints, bookRide, getBookedRides } = useUser();
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  const [chatOpen, setChatOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isBooked, setIsBooked] = useState(false);

  // Check if ride is booked when component mounts
  useEffect(() => {
    const checkIfBooked = async () => {
      if (!isAuthenticated || !user) {
        setIsBooked(false);
        return;
      }

      try {
        const bookedRides = await getBookedRides();
        setIsBooked(bookedRides.some(bookedRide => bookedRide.id === ride.id));
      } catch (error) {
        console.error("Error checking if ride is booked:", error);
        setIsBooked(false);
      }
    };

    checkIfBooked();
  }, [isAuthenticated, user, ride.id, getBookedRides]);

  const handleBookRide = () => {
    if (!isAuthenticated) {
      toast.info(translations.loginRequiredForBooking || "Please login to book this ride");
      navigate("/login?redirect=search");
      return;
    }

    // Check user role - only passengers can book rides
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    if (userData.role !== 'passenger') {
      toast.error("Only passengers can book rides");
      return;
    }

    // Already booked?
    if (isBooked) {
      toast.info(translations.alreadyBooked || "You have already booked this ride");
      return;
    }

    // Navigate to booking page with pending booking flow
    navigate(`/rides/${ride.id}/book`);
  };

  const handleContactDriver = () => {
    if (!isAuthenticated) {
      toast.info(translations.loginRequiredForContact || "Please login to contact the driver");
      navigate("/login?redirect=search");
      return;
    }

    setChatOpen(true);
  };

  const handleViewProfile = () => {
    setShowUserProfile(true);

    // Create a user profile with only the properties that exist
    const userProfile = {
      id: ride.driverId || `driver-${Date.now()}`, // Fallback to a generated ID if not available
      name: ride.driver.name,
      avatar: ride.driver.avatar,
      rating: ride.driver.rating || 4.5,
      joinDate: ride.driver.joinDate || new Date().toISOString(),
      ridesOffered: ride.driver.ridesOffered || ride.driver.rideCount || 0,
      ridesCompleted: ride.driver.ridesCompleted || ride.driver.rideCount || 0
    };

    setSelectedUser(userProfile);
    setProfileOpen(true);
  };

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      <CardContent className="p-0">
        <div className={`flex flex-col sm:flex-row ${isRTL ? 'sm:flex-row-reverse' : ''} h-full`}>
          <div className="sm:w-1/4 bg-gray-100 dark:bg-gray-700 px-4 py-5 flex flex-col items-center justify-center text-center">
            <DriverProfile driver={ride.driver} onViewProfile={handleViewProfile} />
          </div>

          <div className={`sm:w-3/4 p-5 ${isRTL ? 'text-right' : ''}`}>
            <div className="flex flex-wrap justify-between mb-4">
              <RideDetails ride={ride} />
              <RidePricing price={ride.price} seatsAvailable={ride.seatsAvailable} />
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-0">
        <RideActions
          isRideBooked={isBooked}
          seatsAvailable={ride.seatsAvailable}
          onBookRide={handleBookRide}
          onViewProfile={handleViewProfile}
          onContactDriver={handleContactDriver}
          profileOpen={profileOpen}
          chatOpen={chatOpen}
        />
      </CardFooter>

      {/* Chat dialog content */}
      <Dialog open={chatOpen} onOpenChange={setChatOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{translations.chatWithDriver || "Chat with Driver"}</DialogTitle>
            <DialogDescription>
              {translations.chatWithDriverDesc || "Send a message to the driver about this ride"}
            </DialogDescription>
          </DialogHeader>
          {ride.driverId && (
            <ChatInterface recipientId={ride.driverId} recipientName={ride.driver.name} />
          )}
        </DialogContent>
      </Dialog>

      {/* User profile dialog */}
      {showUserProfile && (
        <UserProfileDialog
          open={profileOpen}
          onOpenChange={setProfileOpen}
          user={selectedUser}
        />
      )}
    </Card>
  );
};

export default RideCard;
