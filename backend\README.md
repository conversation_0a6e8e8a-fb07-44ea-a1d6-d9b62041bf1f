# CoJourneyHub Django Backend

This is the Django REST API backend for the CoJourneyHub ride-sharing platform.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
# Run the automated startup script
python start_server.py
```

### Option 2: Manual Setup
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Set up database
python manage.py migrate

# 3. Create superuser (optional)
python manage.py createsuperuser

# 4. Start server
python manage.py runserver
```

### Option 3: Test Setup First
```bash
# Test if Django is configured correctly
python test_django.py

# If tests pass, start the server
python manage.py runserver
```

## 📋 Prerequisites

- Python 3.8+
- pip (Python package manager)
- Virtual environment (recommended)

## 🔧 Environment Setup

### 1. Create Virtual Environment
```bash
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Variables
Create a `.env` file in the backend directory:
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
DATABASE_URL=sqlite:///db.sqlite3
```

## 🗄️ Database Setup

### SQLite (Default - Development)
```bash
python manage.py migrate
```

### PostgreSQL (Production)
1. Install PostgreSQL
2. Create database: `createdb cojourneyhub`
3. Update `.env`:
   ```env
   DATABASE_URL=postgresql://username:password@localhost:5432/cojourneyhub
   ```
4. Install psycopg2: `pip install psycopg2-binary`
5. Run migrations: `python manage.py migrate`

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `GET /api/auth/profile/` - Get user profile

### Rides
- `GET /api/rides/` - List rides
- `POST /api/rides/` - Create ride
- `GET /api/rides/{id}/` - Get ride details
- `POST /api/rides/search/` - Search rides

### Locations
- `GET /api/locations/` - List locations
- `GET /api/locations/autocomplete/` - Location search

### Admin Panel
- Access: `http://localhost:8000/admin/`
- Create superuser: `python manage.py createsuperuser`

## 🧪 Testing

### Run Tests
```bash
python manage.py test
```

### Test Django Setup
```bash
python test_django.py
```

## 📁 Project Structure

```
backend/
├── cojourneyhub/          # Main Django project
│   ├── settings.py        # Django settings
│   ├── urls.py           # Main URL configuration
│   └── wsgi.py           # WSGI configuration
├── accounts/             # User management
├── rides/                # Ride management
├── locations/            # Location services
├── messaging/            # Real-time messaging
├── notifications/        # Notification system
├── reviews/              # Review system
├── manage.py             # Django management script
├── requirements.txt      # Python dependencies
└── .env                  # Environment variables
```

## 🔒 Security Features

- JWT Authentication (optional)
- CORS protection
- Input validation
- SQL injection protection
- XSS protection

## 🚀 Deployment

### Development
```bash
python manage.py runserver
```

### Production
```bash
# Install production dependencies
pip install gunicorn

# Collect static files
python manage.py collectstatic

# Run with Gunicorn
gunicorn cojourneyhub.wsgi:application
```

### Docker
```bash
docker-compose up --build
```

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Make sure you're in the backend directory
   cd backend
   
   # Check if Django is installed
   python -c "import django; print(django.get_version())"
   ```

2. **Database Errors**
   ```bash
   # Reset database
   rm db.sqlite3
   python manage.py migrate
   ```

3. **Port Already in Use**
   ```bash
   # Kill process on port 8000
   lsof -ti:8000 | xargs kill -9
   
   # Or use different port
   python manage.py runserver 8001
   ```

4. **Missing Dependencies**
   ```bash
   # Install all requirements
   pip install -r requirements.txt
   
   # Or install individually
   pip install Django djangorestframework django-cors-headers
   ```

### Debug Mode

Enable debug mode in `.env`:
```env
DEBUG=True
```

### Logs

Check Django logs for errors:
```bash
python manage.py runserver --verbosity=2
```

## 📚 Documentation

- Django Documentation: https://docs.djangoproject.com/
- Django REST Framework: https://www.django-rest-framework.org/
- API Documentation: Available at `/api/` endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `python manage.py test`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Ready to build amazing ride-sharing features!** 🚗💨
