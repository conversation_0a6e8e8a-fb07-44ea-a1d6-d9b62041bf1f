# CoJourneyHub - Complete Django Backend Setup Guide

This guide will help you set up the complete Django backend for the CoJourneyHub ride-sharing application with full integration to the React frontend.

## 🚀 Quick Start

### Prerequisites

- Python 3.11+ 
- Node.js 18+
- PostgreSQL 13+ (optional, SQLite works for development)
- Redis (for real-time features)

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env

# Run setup script
python setup.py
```

### 2. Frontend Configuration

Update your frontend environment variables:

```bash
# In the root directory, update .env
VITE_API_BASE_URL=http://localhost:8000/api
```

### 3. Start Development Servers

```bash
# Terminal 1: Start Django backend
cd backend
python manage.py runserver

# Terminal 2: Start React frontend
npm run dev

# Terminal 3: Start Redis (if using real-time features)
redis-server
```

## 📋 Detailed Setup Instructions

### Backend Configuration

1. **Database Setup**

   For development (SQLite - default):
   ```bash
   # Already configured in .env
   DATABASE_URL=sqlite:///db.sqlite3
   ```

   For production (PostgreSQL):
   ```bash
   # Update .env
   DATABASE_URL=postgresql://username:password@localhost:5432/cojourneyhub
   ```

2. **Run Migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

3. **Populate Location Data**
   ```bash
   python populate_locations.py
   ```

4. **Create Superuser**
   ```bash
   python manage.py createsuperuser
   ```

### Frontend Integration

The frontend has been updated to work with the Django API. Key changes:

1. **API Client**: New `apiClient` in `src/lib/supabase.ts`
2. **Authentication**: JWT token-based auth with Django
3. **Services**: Updated ride service to use Django endpoints

## 🗺️ Map Integration (Free)

The application uses **OpenStreetMap with Leaflet** for free map functionality:

### Features Included:
- ✅ Interactive maps with zoom/pan
- ✅ Location markers for origin/destination  
- ✅ Route visualization between points
- ✅ Tunisia-specific location database
- ✅ Location autocomplete search
- ✅ Distance and duration calculations

### Map Components:
- `src/components/Map.tsx` - Main map component
- `src/components/LocationSearch.tsx` - Location autocomplete
- `backend/locations/` - Location management API

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/profile/` - Get user profile

### Rides
- `GET /api/rides/` - List active rides
- `POST /api/rides/` - Create new ride
- `GET /api/rides/{id}/` - Get ride details
- `POST /api/rides/search/` - Search rides with filters
- `POST /api/rides/{id}/book/` - Book a ride

### Locations
- `GET /api/locations/` - List locations
- `GET /api/locations/autocomplete/` - Location search
- `POST /api/locations/routes/calculate/` - Calculate route

### Messaging
- `GET /api/messaging/conversations/` - List conversations
- `POST /api/messaging/conversations/` - Start conversation
- `GET /api/messaging/conversations/{id}/messages/` - Get messages

### Reviews
- `GET /api/reviews/` - List reviews
- `POST /api/reviews/` - Create review
- `GET /api/reviews/users/{id}/stats/` - User review stats

## 🧪 Testing

### Run Backend Tests
```bash
cd backend
python manage.py test
# or with pytest
pytest
```

### Run Frontend Tests
```bash
npm test
```

### Test Coverage
```bash
cd backend
coverage run --source='.' manage.py test
coverage report
coverage html  # Generate HTML report
```

## 🚀 Production Deployment

### Using Docker

1. **Build and run with Docker Compose**
   ```bash
   cd backend
   docker-compose up --build
   ```

2. **Environment Variables for Production**
   ```bash
   SECRET_KEY=your-production-secret-key
   DEBUG=False
   ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
   DATABASE_URL=******************************/cojourneyhub
   REDIS_URL=redis://redis:6379
   ```

### Manual Deployment

1. **Install Production Dependencies**
   ```bash
   pip install gunicorn psycopg2-binary
   ```

2. **Collect Static Files**
   ```bash
   python manage.py collectstatic
   ```

3. **Run with Gunicorn**
   ```bash
   gunicorn cojourneyhub.wsgi:application --bind 0.0.0.0:8000
   ```

## 🔒 Security Features

- ✅ JWT Authentication with refresh tokens
- ✅ Row-level security with Django permissions
- ✅ CORS configuration for frontend
- ✅ Input validation with Django serializers
- ✅ SQL injection protection
- ✅ XSS protection with Django middleware

## 📊 Database Schema

### Core Models:
- **User** - Extended Django user with ride statistics
- **Ride** - Ride offers with location and pricing
- **RideBooking** - Booking relationships
- **Message** - Real-time messaging
- **Review** - User ratings and feedback
- **TunisiaLocation** - Location database with coordinates

## 🔄 Real-time Features

### WebSocket Support:
- Real-time messaging with Django Channels
- Live ride updates
- Typing indicators
- Read receipts

### Setup Redis for Real-time:
```bash
# Install Redis
# Ubuntu/Debian:
sudo apt install redis-server

# macOS:
brew install redis

# Start Redis
redis-server
```

## 🌍 Internationalization

The backend supports multiple languages:
- English (default)
- French
- Arabic

Location names are stored in multiple languages for better user experience.

## 📱 Mobile-Ready

The API is designed to support future mobile applications:
- RESTful API design
- JWT authentication
- Push notification support (ready for implementation)
- Optimized for mobile data usage

## 🔧 Development Tools

### Admin Interface
Access Django admin at: `http://localhost:8000/admin/`

### API Documentation
- Swagger/OpenAPI documentation (can be added)
- Postman collection available

### Database Management
```bash
# Create migration
python manage.py makemigrations

# Apply migrations  
python manage.py migrate

# Database shell
python manage.py dbshell

# Django shell
python manage.py shell
```

## 🐛 Troubleshooting

### Common Issues:

1. **Port already in use**
   ```bash
   # Kill process on port 8000
   lsof -ti:8000 | xargs kill -9
   ```

2. **Database connection errors**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Reset database
   python manage.py flush
   ```

3. **Redis connection errors**
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Start Redis
   redis-server
   ```

4. **Frontend API connection issues**
   - Ensure `VITE_API_BASE_URL` is set correctly
   - Check CORS settings in Django
   - Verify Django server is running

## 📈 Performance Optimization

### Database:
- Indexes on frequently queried fields
- Database connection pooling
- Query optimization with select_related/prefetch_related

### Caching:
- Redis caching for frequent queries
- Static file caching
- API response caching

### Frontend:
- API response caching
- Lazy loading of components
- Optimized bundle size

## 🔮 Future Enhancements

### Planned Features:
- Payment integration (Stripe/PayPal)
- SMS notifications
- Advanced route optimization
- Machine learning for ride recommendations
- Mobile app (React Native)
- Admin dashboard
- Analytics and reporting

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review Django logs: `tail -f django.log`
3. Check browser console for frontend errors
4. Ensure all environment variables are set correctly

The application is now ready for production use with a complete backend infrastructure!
