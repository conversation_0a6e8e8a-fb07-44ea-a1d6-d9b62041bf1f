-- Row Level Security (RLS) Policies for CoJourneyHub
-- These policies ensure data security and proper access control

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rides ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ride_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view all profiles" ON public.profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Rides policies
CREATE POLICY "Anyone can view active rides" ON public.rides
    FOR SELECT USING (status = 'active');

CREATE POLICY "Users can create their own rides" ON public.rides
    FOR INSERT WITH CHECK (auth.uid() = driver_id);

CREATE POLICY "Drivers can update their own rides" ON public.rides
    FOR UPDATE USING (auth.uid() = driver_id);

CREATE POLICY "Drivers can delete their own rides" ON public.rides
    FOR DELETE USING (auth.uid() = driver_id);

-- Ride bookings policies
CREATE POLICY "Users can view their own bookings" ON public.ride_bookings
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() = (SELECT driver_id FROM public.rides WHERE id = ride_id)
    );

CREATE POLICY "Users can create their own bookings" ON public.ride_bookings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own bookings" ON public.ride_bookings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own bookings" ON public.ride_bookings
    FOR DELETE USING (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Users can view their own messages" ON public.messages
    FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

CREATE POLICY "Users can send messages" ON public.messages
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update their received messages" ON public.messages
    FOR UPDATE USING (auth.uid() = recipient_id);

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Reviews policies
CREATE POLICY "Anyone can view reviews" ON public.reviews
    FOR SELECT USING (true);

CREATE POLICY "Users can create reviews for rides they participated in" ON public.reviews
    FOR INSERT WITH CHECK (
        auth.uid() = reviewer_id AND (
            auth.uid() = (SELECT driver_id FROM public.rides WHERE id = ride_id) OR
            auth.uid() IN (SELECT user_id FROM public.ride_bookings WHERE ride_id = reviews.ride_id)
        )
    );

CREATE POLICY "Users can update their own reviews" ON public.reviews
    FOR UPDATE USING (auth.uid() = reviewer_id);

-- Function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update user rating after a review
CREATE OR REPLACE FUNCTION public.update_user_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.profiles
    SET rating = (
        SELECT AVG(rating)::DECIMAL(3,2)
        FROM public.reviews
        WHERE reviewed_id = NEW.reviewed_id
    )
    WHERE id = NEW.reviewed_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update user rating after a review
CREATE OR REPLACE TRIGGER on_review_created
    AFTER INSERT ON public.reviews
    FOR EACH ROW EXECUTE FUNCTION public.update_user_rating();

-- Function to update ride counts and loyalty points
CREATE OR REPLACE FUNCTION public.update_ride_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        -- Update driver stats
        UPDATE public.profiles
        SET 
            rides_offered = rides_offered + 1,
            loyalty_points = loyalty_points + 50
        WHERE id = NEW.driver_id;
        
        -- Update passenger stats
        UPDATE public.profiles
        SET 
            rides_completed = rides_completed + 1,
            loyalty_points = loyalty_points + 25
        WHERE id IN (
            SELECT user_id FROM public.ride_bookings 
            WHERE ride_id = NEW.id AND status = 'completed'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update ride stats when ride is completed
CREATE OR REPLACE TRIGGER on_ride_completed
    AFTER UPDATE ON public.rides
    FOR EACH ROW EXECUTE FUNCTION public.update_ride_stats();
