# Generated by Django 4.2.7 on 2025-05-27 17:18

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ride',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin', models.CharField(max_length=255)),
                ('destination', models.CharField(max_length=255)),
                ('origin_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('origin_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('destination_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('destination_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('departure_time', models.DateTimeField()),
                ('arrival_time', models.DateTimeField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('distance', models.CharField(max_length=50)),
                ('seats_available', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(8)])),
                ('total_seats', models.PositiveIntegerField(default=4)),
                ('car_model', models.CharField(max_length=100)),
                ('car_color', models.CharField(max_length=50)),
                ('features', models.JSONField(blank=True, default=list)),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offered_rides', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'rides',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RideRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin', models.CharField(max_length=255)),
                ('destination', models.CharField(max_length=255)),
                ('origin_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('origin_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('destination_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('destination_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('preferred_departure_time', models.DateTimeField()),
                ('flexible_time', models.BooleanField(default=True)),
                ('passengers_count', models.PositiveIntegerField(default=1)),
                ('max_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('preferred_features', models.JSONField(blank=True, default=list)),
                ('notes', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('matched', 'Matched'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('matched_ride', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='rides.ride')),
                ('passenger', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ride_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'ride_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RideWaypoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(max_length=255)),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('order', models.PositiveIntegerField()),
                ('estimated_arrival_time', models.DateTimeField(blank=True, null=True)),
                ('ride', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waypoints', to='rides.ride')),
            ],
            options={
                'db_table': 'ride_waypoints',
                'ordering': ['order'],
                'unique_together': {('ride', 'order')},
            },
        ),
        migrations.CreateModel(
            name='RideBooking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('seats_booked', models.PositiveIntegerField(default=1)),
                ('status', models.CharField(choices=[('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('completed', 'Completed')], default='confirmed', max_length=20)),
                ('pickup_location', models.CharField(blank=True, max_length=255)),
                ('dropoff_location', models.CharField(blank=True, max_length=255)),
                ('special_requests', models.TextField(blank=True)),
                ('amount_paid', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('payment_method', models.CharField(default='cash', max_length=50)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('ride', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='rides.ride')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ride_bookings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'ride_bookings',
                'ordering': ['-created_at'],
                'unique_together': {('ride', 'user')},
            },
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['origin'], name='rides_origin_7d2835_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['destination'], name='rides_destina_2bae43_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['departure_time'], name='rides_departu_80b9cb_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['status'], name='rides_status_376a74_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['driver'], name='rides_driver__8fdcf2_idx'),
        ),
    ]
