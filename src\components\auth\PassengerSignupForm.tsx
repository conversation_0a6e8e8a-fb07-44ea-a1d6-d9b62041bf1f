import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

interface PassengerSignupFormProps {
  onSubmit: (data: PassengerSignupData) => void;
  isLoading: boolean;
  errors: Record<string, string>;
}

export interface PassengerSignupData {
  // User fields
  email: string;
  username: string;
  full_name: string;
  password: string;
  password_confirm: string;
  phone_number: string;
  city: string;
  country: string;
  
  // Passenger-specific fields
  id_card_number: string;
}

const PassengerSignupForm: React.FC<PassengerSignupFormProps> = ({ onSubmit, isLoading, errors }) => {
  const [formData, setFormData] = useState<PassengerSignupData>({
    email: '',
    username: '',
    full_name: '',
    password: '',
    password_confirm: '',
    phone_number: '',
    city: '',
    country: 'Tunisia',
    id_card_number: '',
  });

  const handleInputChange = (field: keyof PassengerSignupData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      username: formData.email.split('@')[0], // Generate username from email
    };
    
    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Your basic information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="full_name">Full Name *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                className={errors.full_name ? "border-red-500" : ""}
                required
              />
              {errors.full_name && <p className="text-sm text-red-500">{errors.full_name}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? "border-red-500" : ""}
                required
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone_number">Phone Number</Label>
              <Input
                id="phone_number"
                value={formData.phone_number}
                onChange={(e) => handleInputChange('phone_number', e.target.value)}
                placeholder="+216 XX XXX XXX"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="Tunis"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={errors.password ? "border-red-500" : ""}
                required
              />
              {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password_confirm">Confirm Password *</Label>
              <Input
                id="password_confirm"
                type="password"
                value={formData.password_confirm}
                onChange={(e) => handleInputChange('password_confirm', e.target.value)}
                className={errors.password_confirm ? "border-red-500" : ""}
                required
              />
              {errors.password_confirm && <p className="text-sm text-red-500">{errors.password_confirm}</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Identification */}
      <Card>
        <CardHeader>
          <CardTitle>Identification</CardTitle>
          <CardDescription>Your identification information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="id_card_number">ID Card Number *</Label>
            <Input
              id="id_card_number"
              value={formData.id_card_number}
              onChange={(e) => handleInputChange('id_card_number', e.target.value)}
              className={errors.id_card_number ? "border-red-500" : ""}
              placeholder="********"
              required
            />
            {errors.id_card_number && <p className="text-sm text-red-500">{errors.id_card_number}</p>}
            <p className="text-sm text-muted-foreground">
              Your ID card number is required for verification purposes
            </p>
          </div>
        </CardContent>
      </Card>

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? 'Creating Account...' : 'Create Passenger Account'}
      </Button>
    </form>
  );
};

export default PassengerSignupForm;
