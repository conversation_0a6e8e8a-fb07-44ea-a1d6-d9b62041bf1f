#!/usr/bin/env python3
"""
Script to start all required services for CoJourneyHub backend
"""

import subprocess
import sys
import time
import os
import signal
from pathlib import Path

def check_redis():
    """Check if Redis is running"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✓ Redis is running")
        return True
    except Exception as e:
        print(f"✗ Redis is not running: {e}")
        return False

def start_redis():
    """Start Redis server"""
    print("Starting Redis server...")
    try:
        # Try to start Redis (this might vary based on installation)
        if sys.platform == "win32":
            # Windows
            subprocess.Popen(["redis-server"], shell=True)
        else:
            # Linux/Mac
            subprocess.Popen(["redis-server"])
        
        # Wait a bit for Redis to start
        time.sleep(3)
        
        if check_redis():
            print("✓ Redis started successfully")
            return True
        else:
            print("✗ Failed to start Redis")
            return False
    except Exception as e:
        print(f"✗ Error starting Redis: {e}")
        print("Please install and start Redis manually:")
        print("  Windows: Download from https://redis.io/download")
        print("  Mac: brew install redis && brew services start redis")
        print("  Ubuntu: sudo apt install redis-server && sudo systemctl start redis")
        return False

def start_celery_worker():
    """Start Celery worker"""
    print("Starting Celery worker...")
    try:
        cmd = [sys.executable, "-m", "celery", "-A", "cojourneyhub", "worker", "--loglevel=info"]
        if sys.platform == "win32":
            cmd.extend(["--pool=solo"])  # Windows compatibility
        
        process = subprocess.Popen(cmd)
        print(f"✓ Celery worker started with PID {process.pid}")
        return process
    except Exception as e:
        print(f"✗ Error starting Celery worker: {e}")
        return None

def start_celery_beat():
    """Start Celery beat scheduler"""
    print("Starting Celery beat scheduler...")
    try:
        cmd = [sys.executable, "-m", "celery", "-A", "cojourneyhub", "beat", "--loglevel=info"]
        process = subprocess.Popen(cmd)
        print(f"✓ Celery beat started with PID {process.pid}")
        return process
    except Exception as e:
        print(f"✗ Error starting Celery beat: {e}")
        return None

def start_django():
    """Start Django development server"""
    print("Starting Django development server...")
    try:
        cmd = [sys.executable, "manage.py", "runserver", "0.0.0.0:8000"]
        process = subprocess.Popen(cmd)
        print(f"✓ Django server started with PID {process.pid}")
        print("✓ Django server available at http://localhost:8000")
        return process
    except Exception as e:
        print(f"✗ Error starting Django server: {e}")
        return None

def main():
    """Main function to start all services"""
    print("🚀 Starting CoJourneyHub Backend Services")
    print("=" * 50)
    
    # Change to backend directory
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)
    
    processes = []
    
    try:
        # Check/Start Redis
        if not check_redis():
            if not start_redis():
                print("❌ Cannot continue without Redis")
                return 1
        
        # Start Celery worker
        worker_process = start_celery_worker()
        if worker_process:
            processes.append(worker_process)
        
        # Start Celery beat
        beat_process = start_celery_beat()
        if beat_process:
            processes.append(beat_process)
        
        # Start Django
        django_process = start_django()
        if django_process:
            processes.append(django_process)
        
        print("\n" + "=" * 50)
        print("🎉 All services started successfully!")
        print("📊 Services running:")
        print("   - Redis: localhost:6379")
        print("   - Django: http://localhost:8000")
        print("   - Celery Worker: Background tasks")
        print("   - Celery Beat: Scheduled tasks")
        print("\n💡 Press Ctrl+C to stop all services")
        print("=" * 50)
        
        # Wait for keyboard interrupt
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping all services...")
            
            # Terminate all processes
            for process in processes:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                except Exception as e:
                    print(f"Error stopping process: {e}")
            
            print("✓ All services stopped")
            return 0
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
