# Generated by Django 4.2.7 on 2025-06-10 18:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_user_role'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('passenger', 'Passenger'), ('driver', 'Driver'), ('admin', 'Admin')], max_length=20),
        ),
        migrations.CreateModel(
            name='Passenger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_card_number', models.CharField(max_length=50, unique=True)),
                ('total_rides_booked', models.PositiveIntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('preferred_car_type', models.CharField(blank=True, max_length=50)),
                ('smoking_preference', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='passenger_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'passengers',
            },
        ),
        migrations.CreateModel(
            name='Driver',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('driver_license_number', models.CharField(max_length=50, unique=True)),
                ('license_expiry_date', models.DateField()),
                ('total_rides_offered', models.PositiveIntegerField(default=0)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('is_license_verified', models.BooleanField(default=False)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='driver_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'drivers',
            },
        ),
        migrations.AlterUniqueTogether(
            name='vehicle',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='driver',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to='accounts.driver'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='vehicle',
            unique_together={('driver', 'license_plate')},
        ),
        migrations.RemoveField(
            model_name='vehicle',
            name='owner',
        ),
    ]
