
import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Star, MessageCircle, Calendar, User, MapPin, Car, Users } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import ChatInterface from "./ChatInterface";
import { format } from "date-fns";

interface UserProfile {
  id: string;
  name: string;
  avatar?: string;
  rating: number;
  joinDate: string;
  ridesOffered: number;
  ridesCompleted: number;
  city?: string;
  country?: string;
  bio?: string;
}

interface UserProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: UserProfile;
}

const UserProfileDialog = ({ open, onOpenChange, user }: UserProfileDialogProps) => {
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  const [activeTab, setActiveTab] = useState<string>("profile");
  
  const joinDateFormatted = format(new Date(user.joinDate), "MMMM yyyy");
  
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{translations.userProfile || "User Profile"}</DialogTitle>
          <DialogDescription>
            {translations.userProfileDesc || "View details and chat with this user"}
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile">{translations.profile || "Profile"}</TabsTrigger>
            <TabsTrigger value="chat">{translations.chat || "Chat"}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="profile" className="space-y-4 mt-4">
            <div className="flex flex-col items-center text-center mb-4">
              <Avatar className="h-20 w-20 mb-2">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
              </Avatar>
              <h3 className="font-bold text-lg">{user.name}</h3>
              <div className="flex items-center mt-1">
                <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                <span className="ml-1">{user.rating}/5</span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <Card>
                <CardContent className="p-3 flex flex-col items-center justify-center">
                  <Car className="h-5 w-5 mb-1 text-primary" />
                  <span className="text-sm font-medium">{translations.ridesOffered || "Rides Offered"}</span>
                  <span className="text-lg font-bold">{user.ridesOffered}</span>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-3 flex flex-col items-center justify-center">
                  <Users className="h-5 w-5 mb-1 text-blue-500" />
                  <span className="text-sm font-medium">{translations.ridesCompleted || "Rides Completed"}</span>
                  <span className="text-lg font-bold">{user.ridesCompleted}</span>
                </CardContent>
              </Card>
            </div>
            
            <div className="space-y-2">
              {(user.city || user.country) && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{[user.city, user.country].filter(Boolean).join(", ")}</span>
                </div>
              )}
              
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span>{translations.memberSince || "Member since"}: {joinDateFormatted}</span>
              </div>
            </div>
            
            {user.bio && (
              <div className="pt-2">
                <h4 className="text-sm font-medium mb-1">{translations.bio || "Bio"}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">{user.bio}</p>
              </div>
            )}
            
            <Button 
              onClick={() => setActiveTab("chat")} 
              className="w-full mt-2"
            >
              <MessageCircle className="mr-2 h-4 w-4" />
              {translations.chatWithUser || "Chat with user"}
            </Button>
          </TabsContent>
          
          <TabsContent value="chat" className="mt-4">
            <ChatInterface recipientId={user.id} recipientName={user.name} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default UserProfileDialog;
