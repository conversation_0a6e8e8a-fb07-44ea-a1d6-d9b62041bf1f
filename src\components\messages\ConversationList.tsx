
import { useEffect } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUser } from "@/contexts/UserContext";
import { Contact, useMessageCenter } from "@/hooks/use-message-center";

interface ConversationListProps {
  isOpen: boolean;
  onSelectContact: (contactId: string) => void;
}

const ConversationList = ({ isOpen, onSelectContact }: ConversationListProps) => {
  const { user } = useUser();
  const {
    searchTerm,
    setSearchTerm,
    conversations,
    isLoading,
    loadConversations,
    getLastMessagePreview,
    formatTime
  } = useMessageCenter();

  useEffect(() => {
    if (isOpen && user) {
      loadConversations();
    }
  }, [isOpen, user, searchTerm, loadConversations]);

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-2 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search conversations..."
          className="pl-8"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      
      <ScrollArea className="h-[calc(100vh-12rem)]">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : conversations.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            {searchTerm ? "No conversations found" : "No conversations yet"}
          </div>
        ) : (
          <div className="space-y-2">
            {conversations.map((conversation) => (
              <ConversationItem
                key={conversation.id}
                conversation={conversation}
                onSelect={() => onSelectContact(conversation.id)}
              />
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};

interface ConversationItemProps {
  conversation: Contact;
  onSelect: () => void;
}

const ConversationItem = ({ conversation, onSelect }: ConversationItemProps) => {
  const { user } = useUser();
  const { getLastMessagePreview, formatTime } = useMessageCenter();
  
  const isUnread = conversation.lastMessage && 
    !conversation.lastMessage.read && 
    conversation.lastMessage.recipientId === user?.id;

  return (
    <div 
      className={`p-3 flex items-center gap-3 rounded-lg hover:bg-accent cursor-pointer ${
        isUnread ? "bg-accent/50" : ""
      }`}
      onClick={onSelect}
    >
      <Avatar>
        <AvatarImage src={conversation.avatar} />
        <AvatarFallback>{conversation.name.substring(0, 2).toUpperCase()}</AvatarFallback>
      </Avatar>
      <div className="flex-1 overflow-hidden">
        <div className="flex justify-between items-center">
          <h4 className="font-medium">{conversation.name}</h4>
          {conversation.lastMessage && (
            <span className="text-xs text-muted-foreground">
              {formatTime(conversation.lastMessage.timestamp)}
            </span>
          )}
        </div>
        <p className="text-sm text-muted-foreground truncate">
          {getLastMessagePreview(conversation.lastMessage)}
        </p>
      </div>
    </div>
  );
};

export default ConversationList;
