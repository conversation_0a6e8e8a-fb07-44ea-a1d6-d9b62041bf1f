import { apiClient } from '@/lib/supabase';
import { Vehicle } from '@/types/user';

export interface VehicleCreateData {
  make: string;
  model: string;
  year: number;
  color: string;
  license_plate: string;
  vehicle_type: string;
  seats: number;
  has_ac: boolean;
  has_music: boolean;
  is_smoking_allowed: boolean;
  has_wifi: boolean;
  is_active: boolean;
}

export interface VehicleUpdateData extends Partial<VehicleCreateData> {}

class VehicleService {
  async getUserVehicles(): Promise<Vehicle[]> {
    try {
      const response = await apiClient.get('/vehicles/');
      return this.transformDjangoVehicles(response.results || response);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      return [];
    }
  }

  async createVehicle(vehicleData: VehicleCreateData): Promise<Vehicle> {
    try {
      const response = await apiClient.post('/vehicles/', vehicleData);
      return this.transformDjangoVehicle(response);
    } catch (error) {
      console.error('Error creating vehicle:', error);
      throw error;
    }
  }

  async updateVehicle(vehicleId: string, vehicleData: VehicleUpdateData): Promise<Vehicle> {
    try {
      const response = await apiClient.patch(`/vehicles/${vehicleId}/`, vehicleData);
      return this.transformDjangoVehicle(response);
    } catch (error) {
      console.error('Error updating vehicle:', error);
      throw error;
    }
  }

  async deleteVehicle(vehicleId: string): Promise<boolean> {
    try {
      await apiClient.delete(`/vehicles/${vehicleId}/`);
      return true;
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      throw error;
    }
  }

  async getVehicleById(vehicleId: string): Promise<Vehicle | null> {
    try {
      const response = await apiClient.get(`/vehicles/${vehicleId}/`);
      return this.transformDjangoVehicle(response);
    } catch (error) {
      console.error('Error fetching vehicle:', error);
      return null;
    }
  }

  private transformDjangoVehicles(djangoVehicles: any[]): Vehicle[] {
    return djangoVehicles.map(vehicle => this.transformDjangoVehicle(vehicle));
  }

  private transformDjangoVehicle(djangoVehicle: any): Vehicle {
    return {
      id: djangoVehicle.id.toString(),
      make: djangoVehicle.make,
      model: djangoVehicle.model,
      year: djangoVehicle.year,
      color: djangoVehicle.color,
      licensePlate: djangoVehicle.license_plate,
      vehicleType: djangoVehicle.vehicle_type,
      seats: djangoVehicle.seats,
      hasAC: djangoVehicle.has_ac,
      hasMusic: djangoVehicle.has_music,
      isSmokingAllowed: djangoVehicle.is_smoking_allowed,
      hasWifi: djangoVehicle.has_wifi,
      isActive: djangoVehicle.is_active,
    };
  }

  // Helper methods for vehicle features
  getVehicleFeatures(vehicle: Vehicle): string[] {
    const features: string[] = [];
    
    if (vehicle.hasAC) features.push('Air Conditioning');
    if (vehicle.hasMusic) features.push('Music System');
    if (vehicle.hasWifi) features.push('WiFi');
    if (!vehicle.isSmokingAllowed) features.push('Non-Smoking');
    
    return features;
  }

  getVehicleDisplayName(vehicle: Vehicle): string {
    return `${vehicle.year} ${vehicle.make} ${vehicle.model}`;
  }

  getVehicleTypeDisplayName(vehicleType: string): string {
    const typeMap: { [key: string]: string } = {
      'sedan': 'Sedan',
      'hatchback': 'Hatchback',
      'suv': 'SUV',
      'coupe': 'Coupe',
      'convertible': 'Convertible',
      'wagon': 'Wagon',
      'van': 'Van',
      'pickup': 'Pickup Truck',
    };
    
    return typeMap[vehicleType] || vehicleType;
  }

  validateVehicleData(vehicleData: Partial<VehicleCreateData>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!vehicleData.make?.trim()) {
      errors.push('Vehicle make is required');
    }
    
    if (!vehicleData.model?.trim()) {
      errors.push('Vehicle model is required');
    }
    
    if (!vehicleData.license_plate?.trim()) {
      errors.push('License plate is required');
    }
    
    if (!vehicleData.year || vehicleData.year < 1900 || vehicleData.year > new Date().getFullYear() + 1) {
      errors.push('Please enter a valid year');
    }
    
    if (!vehicleData.seats || vehicleData.seats < 2 || vehicleData.seats > 8) {
      errors.push('Seats must be between 2 and 8');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Mock data for development/fallback
  getMockVehicles(): Vehicle[] {
    return [
      {
        id: 'mock-vehicle-1',
        make: 'Peugeot',
        model: '308',
        year: 2020,
        color: 'White',
        licensePlate: '123 TUN 456',
        vehicleType: 'hatchback',
        seats: 5,
        hasAC: true,
        hasMusic: true,
        isSmokingAllowed: false,
        hasWifi: false,
        isActive: true,
      },
      {
        id: 'mock-vehicle-2',
        make: 'Renault',
        model: 'Clio',
        year: 2019,
        color: 'Blue',
        licensePlate: '789 TUN 012',
        vehicleType: 'hatchback',
        seats: 4,
        hasAC: true,
        hasMusic: false,
        isSmokingAllowed: false,
        hasWifi: false,
        isActive: true,
      }
    ];
  }
}

export const vehicleService = new VehicleService();
