import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Car, User } from "lucide-react";

interface RoleSelectionProps {
  onRoleSelect: (role: 'driver' | 'passenger') => void;
  selectedRole?: 'driver' | 'passenger' | null;
}

const RoleSelection: React.FC<RoleSelectionProps> = ({ onRoleSelect, selectedRole }) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Choose Your Role</h2>
        <p className="text-muted-foreground mt-2">
          Select how you want to use CoJourneyHub
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card 
          className={`cursor-pointer transition-all hover:shadow-lg ${
            selectedRole === 'driver' ? 'ring-2 ring-primary' : ''
          }`}
          onClick={() => onRoleSelect('driver')}
        >
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <Car className="w-8 h-8 text-primary" />
            </div>
            <CardTitle>I'm a Driver</CardTitle>
            <CardDescription>
              Offer rides and earn money by sharing your car
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• Offer rides to passengers</li>
              <li>• Set your own prices</li>
              <li>• Manage your vehicle information</li>
              <li>• Rate passengers</li>
              <li>• Earn money from rides</li>
            </ul>
            <Button 
              className="w-full mt-4" 
              variant={selectedRole === 'driver' ? 'default' : 'outline'}
            >
              Select Driver
            </Button>
          </CardContent>
        </Card>

        <Card 
          className={`cursor-pointer transition-all hover:shadow-lg ${
            selectedRole === 'passenger' ? 'ring-2 ring-primary' : ''
          }`}
          onClick={() => onRoleSelect('passenger')}
        >
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <User className="w-8 h-8 text-primary" />
            </div>
            <CardTitle>I'm a Passenger</CardTitle>
            <CardDescription>
              Find and book rides with trusted drivers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• Search and book rides</li>
              <li>• Save money on transportation</li>
              <li>• Rate drivers</li>
              <li>• Track your ride history</li>
              <li>• Connect with other travelers</li>
            </ul>
            <Button 
              className="w-full mt-4" 
              variant={selectedRole === 'passenger' ? 'default' : 'outline'}
            >
              Select Passenger
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RoleSelection;
