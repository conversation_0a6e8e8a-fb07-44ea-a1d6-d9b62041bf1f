"""
Email service for ride-related notifications
"""

from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)


class RideEmailService:
    """Service for sending ride-related emails"""
    
    @staticmethod
    def send_booking_confirmation(booking):
        """Send booking confirmation email to passenger"""
        try:
            subject = f"Booking Confirmation - {booking.ride.origin} to {booking.ride.destination}"
            
            # Email context
            context = {
                'booking': booking,
                'ride': booking.ride,
                'passenger': booking.user,
                'driver': booking.ride.driver,
                'amount': booking.calculate_amount(),
                'booking_id': booking.id,
                'departure_time': booking.ride.departure_time.strftime('%B %d, %Y at %I:%M %p'),
                'arrival_time': booking.ride.arrival_time.strftime('%B %d, %Y at %I:%M %p'),
            }
            
            # Create HTML email
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Booking Confirmation</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #0D8ABC; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background: #f9f9f9; }}
                    .booking-details {{ background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }}
                    .footer {{ text-align: center; padding: 20px; color: #666; }}
                    .button {{ background: #0D8ABC; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚗 CoJourneyHub</h1>
                        <h2>Booking Confirmation</h2>
                    </div>
                    
                    <div class="content">
                        <h3>Hello {booking.user.full_name or booking.user.email}!</h3>
                        
                        <p>Your ride booking has been confirmed! Here are the details:</p>
                        
                        <div class="booking-details">
                            <h4>📍 Trip Details</h4>
                            <p><strong>From:</strong> {booking.ride.origin}</p>
                            <p><strong>To:</strong> {booking.ride.destination}</p>
                            <p><strong>Departure:</strong> {context['departure_time']}</p>
                            <p><strong>Arrival:</strong> {context['arrival_time']}</p>
                            <p><strong>Distance:</strong> {booking.ride.distance}</p>
                        </div>
                        
                        <div class="booking-details">
                            <h4>👤 Driver Information</h4>
                            <p><strong>Name:</strong> {booking.ride.driver.full_name or booking.ride.driver.email}</p>
                            <p><strong>Rating:</strong> {booking.ride.driver.rating}⭐ ({booking.ride.driver.total_ratings} reviews)</p>
                            <p><strong>Car:</strong> {booking.ride.car_color} {booking.ride.car_model}</p>
                        </div>
                        
                        <div class="booking-details">
                            <h4>💰 Booking Information</h4>
                            <p><strong>Booking ID:</strong> #{booking.id}</p>
                            <p><strong>Seats Booked:</strong> {booking.seats_booked}</p>
                            <p><strong>Total Amount:</strong> {context['amount']} TND</p>
                            <p><strong>Payment Method:</strong> {booking.payment_method.title()}</p>
                            {f'<p><strong>Pickup Location:</strong> {booking.pickup_location}</p>' if booking.pickup_location else ''}
                            {f'<p><strong>Dropoff Location:</strong> {booking.dropoff_location}</p>' if booking.dropoff_location else ''}
                        </div>
                        
                        {f'<div class="booking-details"><h4>📝 Special Requests</h4><p>{booking.special_requests}</p></div>' if booking.special_requests else ''}
                        
                        <p><strong>Important:</strong> Please arrive at the pickup location 5-10 minutes before departure time.</p>
                        
                        <p>You can contact your driver through the CoJourneyHub messaging system once the trip date approaches.</p>
                    </div>
                    
                    <div class="footer">
                        <p>Thank you for choosing CoJourneyHub!</p>
                        <p>Safe travels! 🚗💨</p>
                        <p><small>This is an automated email. Please do not reply to this email.</small></p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Create plain text version
            text_content = f"""
            CoJourneyHub - Booking Confirmation
            
            Hello {booking.user.full_name or booking.user.email}!
            
            Your ride booking has been confirmed! Here are the details:
            
            TRIP DETAILS:
            From: {booking.ride.origin}
            To: {booking.ride.destination}
            Departure: {context['departure_time']}
            Arrival: {context['arrival_time']}
            Distance: {booking.ride.distance}
            
            DRIVER INFORMATION:
            Name: {booking.ride.driver.full_name or booking.ride.driver.email}
            Rating: {booking.ride.driver.rating}⭐ ({booking.ride.driver.total_ratings} reviews)
            Car: {booking.ride.car_color} {booking.ride.car_model}
            
            BOOKING INFORMATION:
            Booking ID: #{booking.id}
            Seats Booked: {booking.seats_booked}
            Total Amount: {context['amount']} TND
            Payment Method: {booking.payment_method.title()}
            {f'Pickup Location: {booking.pickup_location}' if booking.pickup_location else ''}
            {f'Dropoff Location: {booking.dropoff_location}' if booking.dropoff_location else ''}
            
            {f'Special Requests: {booking.special_requests}' if booking.special_requests else ''}
            
            Important: Please arrive at the pickup location 5-10 minutes before departure time.
            
            You can contact your driver through the CoJourneyHub messaging system once the trip date approaches.
            
            Thank you for choosing CoJourneyHub!
            Safe travels! 🚗💨
            
            This is an automated email. Please do not reply to this email.
            """
            
            # Send email
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[booking.user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            logger.info(f"Booking confirmation email sent to {booking.user.email} for booking #{booking.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send booking confirmation email: {str(e)}")
            return False
    
    @staticmethod
    def send_booking_notification_to_driver(booking):
        """Send notification to driver about new booking"""
        try:
            subject = f"New Booking - {booking.ride.origin} to {booking.ride.destination}"
            
            context = {
                'booking': booking,
                'ride': booking.ride,
                'passenger': booking.user,
                'driver': booking.ride.driver,
                'amount': booking.calculate_amount(),
                'departure_time': booking.ride.departure_time.strftime('%B %d, %Y at %I:%M %p'),
            }
            
            text_content = f"""
            CoJourneyHub - New Booking Notification
            
            Hello {booking.ride.driver.full_name or booking.ride.driver.email}!
            
            You have a new booking for your ride:
            
            TRIP DETAILS:
            From: {booking.ride.origin}
            To: {booking.ride.destination}
            Departure: {context['departure_time']}
            
            PASSENGER INFORMATION:
            Name: {booking.user.full_name or booking.user.email}
            Seats Booked: {booking.seats_booked}
            Amount: {context['amount']} TND
            
            {f'Pickup Location: {booking.pickup_location}' if booking.pickup_location else ''}
            {f'Special Requests: {booking.special_requests}' if booking.special_requests else ''}
            
            You can contact the passenger through the CoJourneyHub messaging system.
            
            Thank you for using CoJourneyHub!
            """
            
            send_mail(
                subject=subject,
                message=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[booking.ride.driver.email],
                fail_silently=False
            )
            
            logger.info(f"Booking notification email sent to driver {booking.ride.driver.email} for booking #{booking.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send booking notification email to driver: {str(e)}")
            return False
