
import SimpleContentPage from "@/components/SimpleContentPage";
import { useLanguage } from "@/contexts/LanguageContext";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle, Shield, Car, Phone } from "lucide-react";

const SafetyPage = () => {
  const { translations } = useLanguage();
  
  return (
    <SimpleContentPage title={translations.safetyTitle || "Safety Guidelines"} subtitle="Your safety is our priority">
      <Alert className="mb-8 border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20">
        <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
        <AlertTitle>Important</AlertTitle>
        <AlertDescription>
          Always prioritize your safety when using ridesharing services. Trust your instincts and don't hesitate to cancel a ride if you feel uncomfortable.
        </AlertDescription>
      </Alert>
      
      <h2>Before the Ride</h2>
      <ul>
        <li>Verify the driver's profile information and ratings before accepting a ride</li>
        <li>Confirm the vehicle details (make, model, color) before entering</li>
        <li>Share your ride details with a friend or family member</li>
        <li>Plan to meet in public, well-lit areas</li>
        <li>Arrive on time to avoid rushing and potentially overlooking safety precautions</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>During the Ride</h2>
      <ul>
        <li>Confirm the driver knows your name and destination before entering the vehicle</li>
        <li>Sit in the back seat when possible to maintain personal space</li>
        <li>Keep your phone accessible and charged</li>
        <li>Follow the route on your own map application to ensure you're heading in the right direction</li>
        <li>Be respectful but maintain appropriate boundaries</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>After the Ride</h2>
      <ul>
        <li>Leave honest feedback about your experience</li>
        <li>Report any concerning behavior immediately through our reporting system</li>
        <li>Check that you have all your belongings before exiting the vehicle</li>
        <li>If you left something behind, use our lost item reporting feature rather than trying to contact the driver directly</li>
      </ul>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-8">
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg flex flex-col items-center text-center">
          <Shield className="text-primary h-10 w-10 mb-3" />
          <h3 className="text-lg font-semibold mb-2">Community Verification</h3>
          <p className="text-sm">All users are verified members of the Sesame University community with valid university credentials.</p>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg flex flex-col items-center text-center">
          <Car className="text-primary h-10 w-10 mb-3" />
          <h3 className="text-lg font-semibold mb-2">Vehicle Verification</h3>
          <p className="text-sm">Drivers must provide details about their vehicles, including license plate, make, model, and color.</p>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg flex flex-col items-center text-center">
          <Phone className="text-primary h-10 w-10 mb-3" />
          <h3 className="text-lg font-semibold mb-2">Emergency Support</h3>
          <p className="text-sm">Access to emergency assistance directly through the app during active rides.</p>
        </div>
      </div>
      
      <h2>Our Commitment to Safety</h2>
      <p>At CoSesameHub, we continuously review and enhance our safety features and policies. We conduct regular community reviews and take all reports seriously. Our goal is to create a trusted community of Sesame University students helping each other with transportation needs.</p>
      
      <h3>Report Safety Concerns</h3>
      <p>If you experience or witness behavior that makes you uncomfortable or violates our community guidelines, please report it immediately. We investigate all reports and take appropriate action to maintain a safe environment for all users.</p>
    </SimpleContentPage>
  );
};

export default SafetyPage;
