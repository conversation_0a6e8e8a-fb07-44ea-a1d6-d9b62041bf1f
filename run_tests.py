#!/usr/bin/env python
"""
Comprehensive test runner for CoJourneyHub
Runs both backend and frontend tests with coverage reporting
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description, cwd=None):
    """Run a shell command and handle errors"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=False,
            text=True
        )
        print(f"✅ {description} - PASSED")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED")
        print(f"Error: {e}")
        return False

def check_prerequisites():
    """Check if required tools are installed"""
    print("🔍 Checking prerequisites...")
    
    prerequisites = [
        ("python", "Python"),
        ("node", "Node.js"),
        ("npm", "NPM")
    ]
    
    missing = []
    for cmd, name in prerequisites:
        try:
            subprocess.run([cmd, "--version"], capture_output=True, check=True)
            print(f"✅ {name} is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {name} is not installed")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing prerequisites: {', '.join(missing)}")
        return False
    
    return True

def setup_backend_test_env():
    """Set up backend test environment"""
    print("\n🔧 Setting up backend test environment...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    # Check if virtual environment exists
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        print("📦 Creating virtual environment...")
        if not run_command("python -m venv venv", "Creating virtual environment", backend_dir):
            return False
    
    # Install dependencies
    activate_cmd = "venv\\Scripts\\activate" if os.name == 'nt' else "source venv/bin/activate"
    install_cmd = f"{activate_cmd} && pip install -r requirements.txt"
    
    if not run_command(install_cmd, "Installing backend dependencies", backend_dir):
        return False
    
    return True

def run_backend_tests():
    """Run Django backend tests"""
    print("\n🐍 Running Django Backend Tests")
    
    backend_dir = Path("backend")
    activate_cmd = "venv\\Scripts\\activate" if os.name == 'nt' else "source venv/bin/activate"
    
    # Set up test database
    setup_cmd = f"{activate_cmd} && python manage.py migrate --run-syncdb"
    if not run_command(setup_cmd, "Setting up test database", backend_dir):
        return False
    
    # Run Django tests
    test_cmd = f"{activate_cmd} && python manage.py test --verbosity=2"
    django_tests = run_command(test_cmd, "Django unit tests", backend_dir)
    
    # Run pytest tests
    pytest_cmd = f"{activate_cmd} && pytest tests/ -v"
    pytest_tests = run_command(pytest_cmd, "Pytest tests", backend_dir)
    
    # Generate coverage report
    coverage_cmd = f"{activate_cmd} && coverage run --source='.' manage.py test && coverage report"
    coverage_result = run_command(coverage_cmd, "Test coverage analysis", backend_dir)
    
    # Generate HTML coverage report
    if coverage_result:
        html_cmd = f"{activate_cmd} && coverage html"
        run_command(html_cmd, "Generating HTML coverage report", backend_dir)
        print("📊 Coverage report generated at backend/htmlcov/index.html")
    
    return django_tests and pytest_tests

def run_frontend_tests():
    """Run React frontend tests"""
    print("\n⚛️ Running React Frontend Tests")
    
    # Install dependencies if needed
    if not Path("node_modules").exists():
        if not run_command("npm install", "Installing frontend dependencies"):
            return False
    
    # Run frontend tests
    test_results = []
    
    # Unit tests
    unit_tests = run_command("npm test -- --watchAll=false", "Frontend unit tests")
    test_results.append(unit_tests)
    
    # Linting
    lint_tests = run_command("npm run lint", "ESLint checks")
    test_results.append(lint_tests)
    
    # Type checking
    type_tests = run_command("npx tsc --noEmit", "TypeScript type checking")
    test_results.append(type_tests)
    
    # Build test
    build_tests = run_command("npm run build", "Production build test")
    test_results.append(build_tests)
    
    return all(test_results)

def run_integration_tests():
    """Run integration tests"""
    print("\n🔗 Running Integration Tests")
    
    backend_dir = Path("backend")
    activate_cmd = "venv\\Scripts\\activate" if os.name == 'nt' else "source venv/bin/activate"
    
    # Start Django server in background
    print("🚀 Starting Django test server...")
    django_process = subprocess.Popen(
        f"{activate_cmd} && python manage.py runserver 8001",
        shell=True,
        cwd=backend_dir
    )
    
    # Wait for server to start
    time.sleep(5)
    
    try:
        # Test API endpoints
        api_tests = run_command(
            "curl -f http://localhost:8001/api/locations/ > /dev/null",
            "API endpoint connectivity test"
        )
        
        # Test frontend build with backend
        frontend_tests = run_command(
            "VITE_API_BASE_URL=http://localhost:8001/api npm run build",
            "Frontend integration build test"
        )
        
        return api_tests and frontend_tests
    
    finally:
        # Clean up
        print("🧹 Cleaning up test server...")
        django_process.terminate()
        django_process.wait()

def run_security_tests():
    """Run security tests"""
    print("\n🔒 Running Security Tests")
    
    backend_dir = Path("backend")
    activate_cmd = "venv\\Scripts\\activate" if os.name == 'nt' else "source venv/bin/activate"
    
    # Django security check
    security_cmd = f"{activate_cmd} && python manage.py check --deploy"
    security_tests = run_command(security_cmd, "Django security checks", backend_dir)
    
    # Frontend security audit
    audit_cmd = "npm audit --audit-level=moderate"
    audit_tests = run_command(audit_cmd, "NPM security audit")
    
    return security_tests and audit_tests

def generate_test_report():
    """Generate comprehensive test report"""
    print("\n📋 Generating Test Report")
    
    report = """
# CoJourneyHub Test Report

## Test Summary
- Backend Tests: Django + Pytest
- Frontend Tests: Jest + ESLint + TypeScript
- Integration Tests: API connectivity
- Security Tests: Django security + NPM audit

## Coverage Reports
- Backend: backend/htmlcov/index.html
- Frontend: coverage/lcov-report/index.html

## Next Steps
1. Review any failed tests above
2. Check coverage reports for areas needing more tests
3. Address any security vulnerabilities found
4. Run tests again after fixes

## Continuous Integration
Consider setting up GitHub Actions or similar CI/CD pipeline
to run these tests automatically on every commit.
"""
    
    with open("TEST_REPORT.md", "w") as f:
        f.write(report)
    
    print("📄 Test report saved to TEST_REPORT.md")

def main():
    """Main test runner function"""
    print("🚀 CoJourneyHub Comprehensive Test Suite")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Track test results
    test_results = []
    
    # Setup and run backend tests
    if setup_backend_test_env():
        backend_result = run_backend_tests()
        test_results.append(("Backend Tests", backend_result))
    else:
        test_results.append(("Backend Setup", False))
    
    # Run frontend tests
    frontend_result = run_frontend_tests()
    test_results.append(("Frontend Tests", frontend_result))
    
    # Run integration tests
    integration_result = run_integration_tests()
    test_results.append(("Integration Tests", integration_result))
    
    # Run security tests
    security_result = run_security_tests()
    test_results.append(("Security Tests", security_result))
    
    # Generate report
    generate_test_report()
    
    # Print final summary
    print("\n" + "=" * 60)
    print("📊 FINAL TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Your application is ready for deployment.")
        sys.exit(0)
    else:
        print("⚠️  SOME TESTS FAILED. Please review the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
