from django.urls import path
from . import views

urlpatterns = [
    # Location endpoints
    path('', views.LocationListView.as_view(), name='location-list'),
    path('autocomplete/', views.location_autocomplete, name='location-autocomplete'),
    path('nearby/', views.nearby_locations, name='nearby-locations'),
    path('governorates/', views.governorates, name='governorates'),
    path('log-selection/', views.log_location_selection, name='log-location-selection'),
    
    # Route endpoints
    path('routes/popular/', views.popular_routes, name='popular-routes'),
    path('routes/calculate/', views.calculate_route, name='calculate-route'),
]
