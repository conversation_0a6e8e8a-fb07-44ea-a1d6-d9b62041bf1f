
import SimpleContentPage from "@/components/SimpleContentPage";
import { useLanguage } from "@/contexts/LanguageContext";
import { Separator } from "@/components/ui/separator";

const CommunityRulesPage = () => {
  const { translations } = useLanguage();
  
  return (
    <SimpleContentPage title={translations.communityRulesTitle || "Community Rules"} subtitle="Guidelines for a positive community experience">
      <p>At CoSesameHub, we're building more than just a ridesharing platform – we're creating a community of Sesame University students helping each other. These community rules ensure that everyone has a positive, safe, and respectful experience.</p>
      
      <h2>Respect for All</h2>
      <p>Treat all members of the CoSesameHub community with respect, regardless of their background, identity, or beliefs.</p>
      <ul>
        <li>No discrimination based on race, gender, religion, sexual orientation, or any other personal characteristic</li>
        <li>No harassment, bullying, or intimidation</li>
        <li>Communicate in a civil and respectful manner, even during disagreements</li>
        <li>Respect personal boundaries and privacy</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Honesty & Integrity</h2>
      <p>Trust is the foundation of our community. Always be truthful and act with integrity.</p>
      <ul>
        <li>Provide accurate information in your profile and when offering rides</li>
        <li>Honor your commitments – if you offer or book a ride, follow through</li>
        <li>Leave honest and constructive feedback</li>
        <li>Report any safety concerns or violations of these rules</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Safety First</h2>
      <p>Prioritize safety in all interactions and ride arrangements.</p>
      <ul>
        <li>Drivers must follow all traffic laws and drive safely</li>
        <li>No driving under the influence of alcohol or drugs</li>
        <li>Maintain your vehicle in safe operating condition</li>
        <li>Meet in public places for pickups and drop-offs when possible</li>
        <li>Report any safety concerns immediately</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Reliability & Timeliness</h2>
      <p>Being reliable builds trust within our community.</p>
      <ul>
        <li>Be on time for scheduled rides</li>
        <li>Notify others as early as possible if you need to cancel or will be late</li>
        <li>Respond promptly to messages and ride requests</li>
        <li>Drivers: Arrive at the agreed pickup location</li>
        <li>Passengers: Be ready at the scheduled pickup time</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Clear Communication</h2>
      <p>Clear communication prevents misunderstandings and enhances the experience for everyone.</p>
      <ul>
        <li>Confirm all ride details before the trip begins</li>
        <li>Communicate any special requirements or restrictions in advance</li>
        <li>Drivers: Provide clear information about pickup locations</li>
        <li>Passengers: Send a message if you're running late</li>
        <li>Use respectful and appropriate language in all communications</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Appropriate Behavior</h2>
      <p>Maintain appropriate behavior during rides and in all platform interactions.</p>
      <ul>
        <li>No illegal activities or transportation of illegal items</li>
        <li>No romantic or sexual advances toward other users</li>
        <li>Respect the driver's vehicle rules (e.g., eating, drinking, smoking)</li>
        <li>Keep conversation appropriate and respectful</li>
        <li>Maintain a professional relationship with other users</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Handling Disputes</h2>
      <p>Conflicts may occasionally arise. Handle them constructively and respectfully.</p>
      <ul>
        <li>Address issues directly and calmly with the other person when possible</li>
        <li>If direct resolution isn't possible, use the platform's reporting system</li>
        <li>Provide factual, objective information when reporting issues</li>
        <li>Avoid public accusations or arguments</li>
        <li>Accept platform mediation when necessary</li>
      </ul>
      
      <Separator className="my-6" />
      
      <h2>Consequences for Violations</h2>
      <p>Violations of these community rules may result in:</p>
      <ul>
        <li>Warnings</li>
        <li>Temporary suspension from the platform</li>
        <li>Permanent removal from the community</li>
        <li>In cases involving safety or illegal activity, reporting to university authorities or law enforcement</li>
      </ul>
      
      <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg mt-8">
        <h3 className="text-xl font-semibold mb-2">Our Community Pledge</h3>
        <p>By using CoSesameHub, you agree to follow these community rules and help create a positive environment for all Sesame University students. Together, we can build a trusted network that makes transportation easier, more affordable, and more sustainable for everyone.</p>
      </div>
    </SimpleContentPage>
  );
};

export default CommunityRulesPage;
