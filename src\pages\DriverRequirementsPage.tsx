
import SimpleContentPage from "@/components/SimpleContentPage";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, X, Shield<PERSON>heck, Car, FileCheck, UserCheck } from "lucide-react";

const DriverRequirementsPage = () => {
  const { translations } = useLanguage();
  
  return (
    <SimpleContentPage title={translations.driverReqTitle || "Driver Requirements"} subtitle="Requirements for offering rides on CoSesameHub">
      <p>To maintain a safe and reliable platform, all drivers on CoSesameHub must meet certain requirements. These standards help ensure the quality and safety of rides for all members of our community.</p>
      
      <h2 className="flex items-center gap-2 mt-6">
        <ShieldCheck className="text-primary h-6 w-6" />
        Basic Requirements
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <Check className="text-green-500 h-5 w-5" />
              Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <Check className="text-green-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Be a current Sesame University student</span>
              </li>
              <li className="flex items-start gap-2">
                <Check className="text-green-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Have a valid driver's license</span>
              </li>
              <li className="flex items-start gap-2">
                <Check className="text-green-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Have current vehicle registration and insurance</span>
              </li>
              <li className="flex items-start gap-2">
                <Check className="text-green-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Complete profile verification</span>
              </li>
              <li className="flex items-start gap-2">
                <Check className="text-green-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Verify your phone number and email</span>
              </li>
            </ul>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <X className="text-red-500 h-5 w-5" />
              Disqualifying Factors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <X className="text-red-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Recent major traffic violations</span>
              </li>
              <li className="flex items-start gap-2">
                <X className="text-red-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Expired or invalid driver's license</span>
              </li>
              <li className="flex items-start gap-2">
                <X className="text-red-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Expired or insufficient insurance</span>
              </li>
              <li className="flex items-start gap-2">
                <X className="text-red-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Multiple complaints from passengers</span>
              </li>
              <li className="flex items-start gap-2">
                <X className="text-red-500 h-4 w-4 mt-1 flex-shrink-0" />
                <span>Violation of CoSesameHub community rules</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
      
      <h2 className="flex items-center gap-2 mt-8">
        <Car className="text-primary h-6 w-6" />
        Vehicle Requirements
      </h2>
      
      <p>Your vehicle must meet these basic requirements to be eligible for use on the platform:</p>
      
      <ul className="my-4">
        <li>Must be in good working condition</li>
        <li>Must have functioning seat belts for all passengers</li>
        <li>Must have valid registration and insurance documents</li>
        <li>Must be clean and well-maintained</li>
        <li>Must not have any significant body damage or mechanical issues that would impact safety</li>
      </ul>
      
      <h2 className="flex items-center gap-2 mt-8">
        <FileCheck className="text-primary h-6 w-6" />
        Document Verification
      </h2>
      
      <p>You'll need to provide the following documents for verification:</p>
      
      <ul className="my-4">
        <li>Current Sesame University student ID</li>
        <li>Valid driver's license</li>
        <li>Current vehicle registration</li>
        <li>Proof of vehicle insurance</li>
      </ul>
      
      <h2 className="flex items-center gap-2 mt-8">
        <UserCheck className="text-primary h-6 w-6" />
        Maintaining Eligibility
      </h2>
      
      <p>To remain eligible as a driver on CoSesameHub, you must:</p>
      
      <ul className="my-4">
        <li>Maintain a minimum rating of 4.0 stars</li>
        <li>Complete at least 80% of rides you offer</li>
        <li>Keep your documents up to date in the system</li>
        <li>Adhere to all community guidelines and safety protocols</li>
        <li>Maintain a clean driving record</li>
      </ul>
      
      <div className="bg-primary/10 p-6 rounded-lg mt-8">
        <h3 className="text-xl font-semibold mb-2">Ready to become a driver?</h3>
        <p className="mb-4">If you meet all requirements and have all necessary documents, you can start offering rides immediately after completing the verification process.</p>
        <a href="/offer-ride" className="text-primary hover:underline font-medium">Offer your first ride →</a>
      </div>
    </SimpleContentPage>
  );
};

export default DriverRequirementsPage;
