from django.contrib import admin
from .models import Ride, RideBooking, RideRequest, RideWaypoint


@admin.register(Ride)
class RideAdmin(admin.ModelAdmin):
    """Admin configuration for Ride model"""
    
    list_display = [
        'id', 'driver', 'origin', 'destination', 'departure_time',
        'price', 'seats_available', 'seats_remaining', 'status', 'created_at'
    ]
    list_filter = [
        'status', 'departure_time', 'price', 'seats_available',
        'car_model', 'created_at'
    ]
    search_fields = [
        'driver__email', 'driver__full_name', 'origin',
        'destination', 'car_model'
    ]
    ordering = ['-created_at']
    
    fieldsets = (
        ('Driver', {
            'fields': ('driver',)
        }),
        ('Route', {
            'fields': ('origin', 'destination', 'distance')
        }),
        ('Schedule', {
            'fields': ('departure_time', 'arrival_time')
        }),
        ('Pricing & Capacity', {
            'fields': ('price', 'seats_available', 'total_seats')
        }),
        ('Vehicle', {
            'fields': ('car_model', 'car_color')
        }),
        ('Details', {
            'fields': ('features', 'notes', 'status')
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(RideBooking)
class RideBookingAdmin(admin.ModelAdmin):
    """Admin configuration for RideBooking model"""
    
    list_display = [
        'id', 'user', 'ride', 'seats_booked', 'amount_paid',
        'payment_status', 'status', 'created_at'
    ]
    list_filter = [
        'status', 'payment_status', 'payment_method', 'created_at'
    ]
    search_fields = [
        'user__email', 'user__full_name', 'ride__origin',
        'ride__destination'
    ]
    ordering = ['-created_at']
    
    fieldsets = (
        ('Booking Details', {
            'fields': ('ride', 'user', 'seats_booked', 'status')
        }),
        ('Locations', {
            'fields': ('pickup_location', 'dropoff_location')
        }),
        ('Payment', {
            'fields': ('amount_paid', 'payment_method', 'payment_status')
        }),
        ('Additional Info', {
            'fields': ('special_requests',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(RideRequest)
class RideRequestAdmin(admin.ModelAdmin):
    """Admin configuration for RideRequest model"""
    
    list_display = [
        'id', 'passenger', 'origin', 'destination',
        'preferred_departure_time', 'passengers_count',
        'max_price', 'status', 'created_at'
    ]
    list_filter = [
        'status', 'flexible_time', 'preferred_departure_time', 'created_at'
    ]
    search_fields = [
        'passenger__email', 'passenger__full_name',
        'origin', 'destination'
    ]
    ordering = ['-created_at']
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(RideWaypoint)
class RideWaypointAdmin(admin.ModelAdmin):
    """Admin configuration for RideWaypoint model"""
    
    list_display = ['ride', 'location', 'order', 'estimated_arrival_time']
    list_filter = ['order']
    search_fields = ['ride__origin', 'ride__destination', 'location']
    ordering = ['ride', 'order']
