
import { useState } from "react";
import { MessageCircle } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ChatInterface from "./ChatInterface";
import ConversationList from "./messages/ConversationList";
import ChatHeader from "./messages/ChatHeader";
import { useMessageCenter } from "@/hooks/use-message-center";

const MessageCenter = () => {
  const { translations, language } = useLanguage();
  const [open, setOpen] = useState(false);
  const {
    selectedContact,
    setSelectedContact,
    conversations,
    unreadMessagesExist
  } = useMessageCenter();
  
  const isRTL = language === "ar";

  const selectedContactDetails = conversations.find(c => c.id === selectedContact);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <MessageCircle className="h-5 w-5" />
          {unreadMessagesExist && (
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent side={isRTL ? "left" : "right"} className="w-full max-w-md sm:max-w-lg">
        <Tabs defaultValue={selectedContact ? "chat" : "conversations"} className="h-full flex flex-col">
          <SheetHeader>
            <div className="flex items-center justify-between">
              <SheetTitle>{translations.messages || "Messages"}</SheetTitle>
              <TabsList>
                <TabsTrigger value="conversations" onClick={() => setSelectedContact(null)}>
                  {translations.conversations || "Conversations"}
                </TabsTrigger>
                {selectedContact && (
                  <TabsTrigger value="chat">
                    {translations.chat || "Chat"}
                  </TabsTrigger>
                )}
              </TabsList>
            </div>
          </SheetHeader>
          
          <TabsContent value="conversations" className="flex-1 mt-6">
            <ConversationList
              isOpen={open}
              onSelectContact={(contactId) => setSelectedContact(contactId)}
            />
          </TabsContent>
          
          {selectedContact && (
            <TabsContent value="chat" className="flex-1 h-full">
              <ChatHeader contact={selectedContactDetails} />
              <ChatInterface 
                recipientId={selectedContact} 
                recipientName={selectedContactDetails?.name || ""} 
              />
            </TabsContent>
          )}
        </Tabs>
      </SheetContent>
    </Sheet>
  );
};

export default MessageCenter;
