from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, MessageRead, BlockedUser
from accounts.serializers import UserSerializer

User = get_user_model()


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for messages"""

    sender = UserSerializer(read_only=True)
    is_own_message = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = [
            'id', 'conversation', 'sender', 'content', 'message_type',
            'image', 'location_lat', 'location_lng', 'location_name',
            'is_read', 'read_at', 'is_own_message', 'created_at', 'updated_at'
        ]
        read_only_fields = ['sender', 'created_at', 'updated_at']

    def get_is_own_message(self, obj):
        request = self.context.get('request')
        if request and request.user:
            return obj.sender == request.user
        return False


class MessageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating messages"""

    class Meta:
        model = Message
        fields = [
            'content', 'message_type',
            'image', 'location_lat', 'location_lng', 'location_name'
        ]

    def create(self, validated_data):
        # The conversation and sender will be set in the view's perform_create method
        return super().create(validated_data)


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversations"""

    participants = UserSerializer(many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    other_participant = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = [
            'id', 'participants', 'last_message', 'unread_count',
            'other_participant', 'related_ride', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_last_message(self, obj):
        last_message = obj.messages.order_by('-created_at').first()
        if last_message:
            return MessageSerializer(last_message).data
        return None

    def get_unread_count(self, obj):
        request = self.context.get('request')
        if request and request.user and request.user.is_authenticated:
            return obj.messages.filter(
                is_read=False
            ).exclude(sender=request.user).count()
        return 0

    def get_other_participant(self, obj):
        request = self.context.get('request')
        if request and request.user and request.user.is_authenticated:
            other_user = obj.get_other_participant(request.user)
            if other_user:
                return UserSerializer(other_user).data
        return None


class ConversationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating conversations"""

    participant_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Conversation
        fields = ['participant_id', 'related_ride']

    def validate_participant_id(self, value):
        try:
            user = User.objects.get(id=value)
            # Only check for self-conversation if user is authenticated
            if self.context['request'].user.is_authenticated:
                if user == self.context['request'].user:
                    raise serializers.ValidationError("Cannot create conversation with yourself")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")

    def create(self, validated_data):
        participant_id = validated_data.pop('participant_id')
        participant = User.objects.get(id=participant_id)

        # Get current user (authenticated or default for testing)
        if self.context['request'].user.is_authenticated:
            current_user = self.context['request'].user
        else:
            # For unauthenticated requests, use the first user
            current_user = User.objects.first()
            if not current_user:
                # Create a default user for testing
                current_user = User.objects.create_user(
                    username='default_user',
                    email='<EMAIL>',
                    full_name='Default User',
                    password='testpass123'
                )

        # Check if conversation already exists
        existing_conversation = Conversation.objects.filter(
            participants=current_user
        ).filter(
            participants=participant
        ).first()

        if existing_conversation:
            return existing_conversation

        # Create new conversation
        conversation = Conversation.objects.create(**validated_data)
        conversation.participants.add(current_user, participant)

        return conversation


class BlockedUserSerializer(serializers.ModelSerializer):
    """Serializer for blocked users"""

    blocked = UserSerializer(read_only=True)

    class Meta:
        model = BlockedUser
        fields = ['id', 'blocked', 'reason', 'created_at']
        read_only_fields = ['created_at']


class BlockUserSerializer(serializers.Serializer):
    """Serializer for blocking a user"""

    user_id = serializers.IntegerField()
    reason = serializers.CharField(required=False, allow_blank=True)

    def validate_user_id(self, value):
        try:
            user = User.objects.get(id=value)
            # Only check for self-blocking if user is authenticated
            if self.context['request'].user.is_authenticated:
                if user == self.context['request'].user:
                    raise serializers.ValidationError("Cannot block yourself")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")
