
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import LocationSearch from "@/components/LocationSearch";
import { useLanguage } from "@/contexts/LanguageContext";
import { Control } from "react-hook-form";

interface LocationFieldsProps {
  control: Control<any>;
  isRTL: boolean;
  onOriginChange?: (coords?: { lat: number; lng: number }) => void;
  onDestinationChange?: (coords?: { lat: number; lng: number }) => void;
}

const LocationFields = ({ control, isRTL, onOriginChange, onDestinationChange }: LocationFieldsProps) => {
  const { translations } = useLanguage();

  return (
    <>
      <FormField
        control={control}
        name="origin"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.origin}</FormLabel>
            <FormControl>
              <LocationSearch
                placeholder={`e.g. Tu<PERSON>`}
                value={field.value}
                onChange={(location, coords) => {
                  field.onChange(location);
                  onOriginChange?.(coords);
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="destination"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.destination}</FormLabel>
            <FormControl>
              <LocationSearch
                placeholder={`e.g. Sousse`}
                value={field.value}
                onChange={(location, coords) => {
                  field.onChange(location);
                  onDestinationChange?.(coords);
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default LocationFields;
