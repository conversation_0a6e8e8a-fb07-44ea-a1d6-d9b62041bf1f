import React, { useState, useEffect } from 'react';
import { MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { apiService } from '@/services/apiService';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UserContext';

interface Conversation {
  id: string;
  other_user: {
    id: string;
    full_name: string;
    avatar?: string;
  };
  last_message: {
    content: string;
    created_at: string;
    sender: string;
  };
  unread_count: number;
}

const MessageBell: React.FC = () => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [totalUnread, setTotalUnread] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const { user } = useUser();

  useEffect(() => {
    if (user) {
      fetchConversations();
      // Set up polling for new messages
      const interval = setInterval(fetchConversations, 15000); // Poll every 15 seconds
      return () => clearInterval(interval);
    }
  }, [user]);

  const fetchConversations = async () => {
    try {
      const data = await apiService.getConversations();
      setConversations(data.slice(0, 10)); // Show only latest 10
      const unreadTotal = data.reduce((sum: number, conv: Conversation) => sum + conv.unread_count, 0);
      setTotalUnread(unreadTotal);
    } catch (error) {
      console.error('Error fetching conversations:', error);
    }
  };

  const handleConversationClick = (conversation: Conversation) => {
    navigate(`/messages?conversation=${conversation.id}`);
    setIsOpen(false);
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours}h`;
    } else {
      const days = Math.floor(diffInHours / 24);
      return `${days}d`;
    }
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  if (!user) return null;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <MessageCircle className="h-5 w-5" />
          {totalUnread > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0"
            >
              {totalUnread > 99 ? '99+' : totalUnread}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Messages</span>
          {totalUnread > 0 && (
            <Badge variant="secondary" className="text-xs">
              {totalUnread} unread
            </Badge>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {conversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No messages</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            {conversations.map((conversation) => (
              <DropdownMenuItem
                key={conversation.id}
                className="p-3 cursor-pointer hover:bg-gray-50"
                onClick={() => handleConversationClick(conversation)}
              >
                <div className="flex items-start gap-3 w-full">
                  <Avatar className="w-10 h-10 flex-shrink-0">
                    <AvatarImage src={conversation.other_user.avatar} />
                    <AvatarFallback>
                      {conversation.other_user.full_name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between gap-2">
                      <h4 className="font-medium text-sm truncate">
                        {conversation.other_user.full_name}
                      </h4>
                      <div className="flex items-center gap-1 flex-shrink-0">
                        <span className="text-xs text-gray-400">
                          {formatTime(conversation.last_message.created_at)}
                        </span>
                        {conversation.unread_count > 0 && (
                          <Badge 
                            variant="destructive" 
                            className="h-5 w-5 flex items-center justify-center text-xs p-0"
                          >
                            {conversation.unread_count}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <p className={`text-xs mt-1 line-clamp-2 ${
                      conversation.unread_count > 0 ? 'font-medium text-gray-900' : 'text-gray-600'
                    }`}>
                      {conversation.last_message.sender === user.id ? 'You: ' : ''}
                      {truncateMessage(conversation.last_message.content)}
                    </p>
                  </div>
                </div>
              </DropdownMenuItem>
            ))}
          </ScrollArea>
        )}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="text-center justify-center"
          onClick={() => {
            navigate('/messages');
            setIsOpen(false);
          }}
        >
          View all messages
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MessageBell;
