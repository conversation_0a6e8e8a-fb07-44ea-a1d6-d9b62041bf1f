
import { But<PERSON> } from "@/components/ui/button";
import { User, MessageCircle } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";

interface RideActionsProps {
  isRideBooked: boolean;
  seatsAvailable: number;
  onBookRide: () => void;
  onViewProfile: () => void;
  onContactDriver: () => void;
  profileOpen: boolean;
  chatOpen: boolean;
}

export const RideActions = ({ 
  isRideBooked, 
  seatsAvailable, 
  onBookRide, 
  onViewProfile, 
  onContactDriver,
  profileOpen,
  chatOpen
}: RideActionsProps) => {
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";

  return (
    <div className={`border-t p-4 bg-gray-50 dark:bg-gray-800 ${isRTL ? 'flex-row-reverse' : ''} flex justify-between flex-wrap gap-2`}>
      <div className="flex space-x-2 rtl:space-x-reverse">
        <Dialog open={profileOpen}>
          <DialogTrigger asChild>
            <Button 
              variant="outline"
              onClick={onViewProfile}
              className="flex items-center gap-2"
            >
              <User className="h-4 w-4" />
              {translations.viewProfile || "View Profile"}
            </Button>
          </DialogTrigger>
        </Dialog>
        
        <Dialog open={chatOpen}>
          <DialogTrigger asChild>
            <Button 
              variant="outline"
              onClick={onContactDriver}
              className="flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" />
              {translations.contactDriver || "Contact Driver"}
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>
      
      <Button 
        onClick={onBookRide}
        disabled={isRideBooked || seatsAvailable === 0}
      >
        {isRideBooked 
          ? (translations.alreadyBooked || "Already Booked") 
          : seatsAvailable === 0 
            ? (translations.fullyBooked || "Fully Booked") 
            : (translations.bookNow || "Book Now")}
      </Button>
    </div>
  );
};
