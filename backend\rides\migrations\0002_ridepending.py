# Generated by Django 4.2.7 on 2025-06-10 18:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rides', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RidePending',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('seats_requested', models.PositiveIntegerField(default=1)),
                ('pickup_location', models.CharField(blank=True, max_length=255)),
                ('dropoff_location', models.CharField(blank=True, max_length=255)),
                ('passenger_name', models.CharField(max_length=255)),
                ('passenger_phone', models.CharField(max_length=20)),
                ('special_requests', models.TextField(blank=True)),
                ('status', models.Char<PERSON>ield(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('driver_response', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('passenger', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pending_bookings', to=settings.AUTH_USER_MODEL)),
                ('ride', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pending_bookings', to='rides.ride')),
            ],
            options={
                'db_table': 'ride_pending',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['ride', 'status'], name='ride_pendin_ride_id_c06ff3_idx'), models.Index(fields=['passenger', 'status'], name='ride_pendin_passeng_7c75bd_idx'), models.Index(fields=['status', '-created_at'], name='ride_pendin_status_b2da39_idx')],
                'unique_together': {('ride', 'passenger')},
            },
        ),
    ]
