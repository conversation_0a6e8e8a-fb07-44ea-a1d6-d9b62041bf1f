import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface DriverSignupFormProps {
  onSubmit: (data: DriverSignupData) => void;
  isLoading: boolean;
  errors: Record<string, string>;
}

export interface DriverSignupData {
  // User fields
  email: string;
  username: string;
  full_name: string;
  password: string;
  password_confirm: string;
  phone_number: string;
  city: string;
  country: string;
  
  // Driver-specific fields
  driver_license_number: string;
  license_expiry_date: string;
  
  // Vehicle fields
  vehicle_make: string;
  vehicle_model: string;
  vehicle_year: number;
  vehicle_color: string;
  vehicle_license_plate: string;
  vehicle_type: string;
  vehicle_seats: number;
}

const DriverSignupForm: React.FC<DriverSignupFormProps> = ({ onSubmit, isLoading, errors }) => {
  const [formData, setFormData] = useState<DriverSignupData>({
    email: '',
    username: '',
    full_name: '',
    password: '',
    password_confirm: '',
    phone_number: '',
    city: '',
    country: 'Tunisia',
    driver_license_number: '',
    license_expiry_date: '',
    vehicle_make: '',
    vehicle_model: '',
    vehicle_year: new Date().getFullYear(),
    vehicle_color: '',
    vehicle_license_plate: '',
    vehicle_type: 'sedan',
    vehicle_seats: 4,
  });

  const [licenseExpiryDate, setLicenseExpiryDate] = useState<Date>();

  const handleInputChange = (field: keyof DriverSignupData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      license_expiry_date: licenseExpiryDate ? format(licenseExpiryDate, 'yyyy-MM-dd') : '',
      username: formData.email.split('@')[0], // Generate username from email
    };
    
    onSubmit(submitData);
  };

  const vehicleTypes = [
    { value: 'sedan', label: 'Sedan' },
    { value: 'hatchback', label: 'Hatchback' },
    { value: 'suv', label: 'SUV' },
    { value: 'coupe', label: 'Coupe' },
    { value: 'convertible', label: 'Convertible' },
    { value: 'wagon', label: 'Wagon' },
    { value: 'van', label: 'Van' },
    { value: 'pickup', label: 'Pickup Truck' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Your basic information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="full_name">Full Name *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                className={errors.full_name ? "border-red-500" : ""}
                required
              />
              {errors.full_name && <p className="text-sm text-red-500">{errors.full_name}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? "border-red-500" : ""}
                required
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone_number">Phone Number</Label>
              <Input
                id="phone_number"
                value={formData.phone_number}
                onChange={(e) => handleInputChange('phone_number', e.target.value)}
                placeholder="+216 XX XXX XXX"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="Tunis"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={errors.password ? "border-red-500" : ""}
                required
              />
              {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password_confirm">Confirm Password *</Label>
              <Input
                id="password_confirm"
                type="password"
                value={formData.password_confirm}
                onChange={(e) => handleInputChange('password_confirm', e.target.value)}
                className={errors.password_confirm ? "border-red-500" : ""}
                required
              />
              {errors.password_confirm && <p className="text-sm text-red-500">{errors.password_confirm}</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Driver License Information */}
      <Card>
        <CardHeader>
          <CardTitle>Driver License Information</CardTitle>
          <CardDescription>Your driving license details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="driver_license_number">License Number *</Label>
              <Input
                id="driver_license_number"
                value={formData.driver_license_number}
                onChange={(e) => handleInputChange('driver_license_number', e.target.value)}
                className={errors.driver_license_number ? "border-red-500" : ""}
                required
              />
              {errors.driver_license_number && <p className="text-sm text-red-500">{errors.driver_license_number}</p>}
            </div>
            
            <div className="space-y-2">
              <Label>License Expiry Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !licenseExpiryDate && "text-muted-foreground",
                      errors.license_expiry_date && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {licenseExpiryDate ? format(licenseExpiryDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={licenseExpiryDate}
                    onSelect={setLicenseExpiryDate}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.license_expiry_date && <p className="text-sm text-red-500">{errors.license_expiry_date}</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Information */}
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Information</CardTitle>
          <CardDescription>Details about your first vehicle</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vehicle_make">Make *</Label>
              <Input
                id="vehicle_make"
                value={formData.vehicle_make}
                onChange={(e) => handleInputChange('vehicle_make', e.target.value)}
                placeholder="Toyota"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vehicle_model">Model *</Label>
              <Input
                id="vehicle_model"
                value={formData.vehicle_model}
                onChange={(e) => handleInputChange('vehicle_model', e.target.value)}
                placeholder="Corolla"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vehicle_year">Year *</Label>
              <Input
                id="vehicle_year"
                type="number"
                value={formData.vehicle_year}
                onChange={(e) => handleInputChange('vehicle_year', parseInt(e.target.value))}
                min="1990"
                max={new Date().getFullYear() + 1}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vehicle_color">Color *</Label>
              <Input
                id="vehicle_color"
                value={formData.vehicle_color}
                onChange={(e) => handleInputChange('vehicle_color', e.target.value)}
                placeholder="White"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vehicle_license_plate">License Plate *</Label>
              <Input
                id="vehicle_license_plate"
                value={formData.vehicle_license_plate}
                onChange={(e) => handleInputChange('vehicle_license_plate', e.target.value)}
                placeholder="123 TUN 456"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vehicle_type">Vehicle Type</Label>
              <Select value={formData.vehicle_type} onValueChange={(value) => handleInputChange('vehicle_type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {vehicleTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="vehicle_seats">Number of Seats</Label>
            <Input
              id="vehicle_seats"
              type="number"
              value={formData.vehicle_seats}
              onChange={(e) => handleInputChange('vehicle_seats', parseInt(e.target.value))}
              min="2"
              max="8"
            />
          </div>
        </CardContent>
      </Card>

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? 'Creating Account...' : 'Create Driver Account'}
      </Button>
    </form>
  );
};

export default DriverSignupForm;
