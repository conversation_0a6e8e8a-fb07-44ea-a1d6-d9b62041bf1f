
import PageContainer from "@/components/PageContainer";
import { useLanguage } from "@/contexts/LanguageContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Github, Linkedin, Twitter } from "lucide-react";

const TeamPage = () => {
  const { translations } = useLanguage();
  
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "Founder & CEO",
      bio: "Former Sesame University student who experienced transportation challenges firsthand. Passionate about community building and sustainable transportation.",
      avatar: "FZ"
    },
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON>",
      bio: "Computer Science graduate with expertise in developing secure, user-friendly platforms. Led development of our innovative ride-matching algorithm.",
      avatar: "MA"
    },
    {
      name: "<PERSON><PERSON>",
      role: "Head of Community",
      bio: "Sociology graduate who oversees our user safety protocols and community guidelines. Ensures CoSesameHub remains a safe space for all users.",
      avatar: "NB"
    },
    {
      name: "<PERSON>",
      role: "Marketing Director",
      bio: "Marketing specialist who helped grow our user base from a small group of early adopters to a thriving community of thousands of students.",
      avatar: "AT"
    }
  ];

  const advisors = [
    {
      name: "Prof. Karim Ben Salem",
      role: "Academic Advisor",
      bio: "Transportation Studies professor who provides guidance on sustainable mobility and helps connect CoSesameHub with university resources.",
      avatar: "KS"
    },
    {
      name: "Leila Mansour",
      role: "Business Mentor",
      bio: "Experienced entrepreneur who advises on business strategy, growth planning, and securing partnerships to expand our services.",
      avatar: "LM"
    }
  ];

  return (
    <PageContainer title={translations.teamTitle || "Our Team"}>
      <div className="space-y-6">
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Meet the passionate team behind CoSesameHub, dedicated to making student transportation safer, more affordable, and more community-oriented.
        </p>

        <h2 className="text-2xl font-bold mb-6 dark:text-white">Leadership Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {teamMembers.map((member, index) => (
            <Card key={index} className="transition-all hover:shadow-md hover:-translate-y-1 animate-fade-in" style={{animationDelay: `${index * 0.1}s`}}>
              <CardHeader className="flex flex-row items-center gap-4">
                <Avatar className="h-14 w-14 border-2 border-primary">
                  <AvatarFallback>{member.avatar}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle>{member.name}</CardTitle>
                  <CardDescription>{member.role}</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300">{member.bio}</p>
              </CardContent>
              <CardFooter className="flex gap-2 text-gray-500">
                <Twitter className="h-4 w-4 cursor-pointer hover:text-primary" />
                <Linkedin className="h-4 w-4 cursor-pointer hover:text-primary" />
                <Github className="h-4 w-4 cursor-pointer hover:text-primary" />
              </CardFooter>
            </Card>
          ))}
        </div>

        <h2 className="text-2xl font-bold mt-10 mb-6 dark:text-white">Advisory Board</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {advisors.map((advisor, index) => (
            <Card key={index} className="transition-all hover:shadow-md hover:-translate-y-1 animate-fade-in" style={{animationDelay: `${index * 0.1}s`}}>
              <CardHeader className="flex flex-row items-center gap-4">
                <Avatar className="h-14 w-14 border-2 border-secondary">
                  <AvatarFallback>{advisor.avatar}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle>{advisor.name}</CardTitle>
                  <CardDescription>{advisor.role}</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300">{advisor.bio}</p>
              </CardContent>
              <CardFooter className="flex gap-2 text-gray-500">
                <Linkedin className="h-4 w-4 cursor-pointer hover:text-primary" />
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="mt-10 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold mb-4 dark:text-white">Join Our Team</h2>
          <p className="text-gray-700 dark:text-gray-300">
            We're always looking for talented individuals who are passionate about our mission to join our team. 
            Visit our <a href="/careers" className="text-primary hover:underline">Careers page</a> to see our current openings.
          </p>
        </div>
      </div>
    </PageContainer>
  );
};

export default TeamPage;
