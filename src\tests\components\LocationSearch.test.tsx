import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LocationSearch from '@/components/LocationSearch';

describe('LocationSearch', () => {
  it('renders with placeholder text', () => {
    render(<LocationSearch placeholder="Search location..." />);
    expect(screen.getByPlaceholderText('Search location...')).toBeInTheDocument();
  });

  it('shows suggestions when typing', async () => {
    const mockOnChange = vi.fn();
    render(<LocationSearch onChange={mockOnChange} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'Tunis' } });
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('Tunis');
    });
  });

  it('filters locations based on input', async () => {
    render(<LocationSearch />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'Tun' } });
    
    await waitFor(() => {
      // Should show <PERSON>nis in suggestions
      expect(screen.getByText('Tunis')).toBeInTheDocument();
    });
  });

  it('calls onSelect when suggestion is clicked', async () => {
    const mockOnSelect = vi.fn();
    render(<LocationSearch onSelect={mockOnSelect} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'Tunis' } });
    
    await waitFor(() => {
      const suggestion = screen.getByText('Tunis');
      fireEvent.click(suggestion);
      expect(mockOnSelect).toHaveBeenCalled();
    });
  });

  it('handles keyboard navigation', async () => {
    render(<LocationSearch />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'Tu' } });
    
    await waitFor(() => {
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      fireEvent.keyDown(input, { key: 'Enter' });
    });
  });

  it('clears input when clear button is clicked', () => {
    const mockOnChange = vi.fn();
    render(<LocationSearch value="Tunis" onChange={mockOnChange} />);
    
    const clearButton = screen.getByRole('button');
    fireEvent.click(clearButton);
    
    expect(mockOnChange).toHaveBeenCalledWith('');
  });
});
