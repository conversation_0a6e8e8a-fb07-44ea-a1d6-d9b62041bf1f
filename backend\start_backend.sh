#!/bin/bash

echo "🚀 Starting CoJourneyHub Backend Services..."
echo "================================================"

# Function to check if <PERSON><PERSON> is running
check_redis() {
    if redis-cli ping >/dev/null 2>&1; then
        echo "✓ Redis is running"
        return 0
    else
        echo "✗ Redis is not running"
        return 1
    fi
}

# Function to start Redis if not running
start_redis() {
    echo "Starting Redis..."
    if command -v redis-server >/dev/null 2>&1; then
        redis-server --daemonize yes
        sleep 2
        if check_redis; then
            echo "✓ Redis started successfully"
            return 0
        else
            echo "✗ Failed to start Redis"
            return 1
        fi
    else
        echo "✗ Redis not installed. Please install Redis:"
        echo "  Ubuntu/Debian: sudo apt install redis-server"
        echo "  CentOS/RHEL: sudo yum install redis"
        echo "  macOS: brew install redis"
        return 1
    fi
}

# Check if Red<PERSON> is running, start if not
if ! check_redis; then
    if ! start_redis; then
        echo "❌ Cannot continue without Redis"
        exit 1
    fi
fi

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping all services..."
    
    # Kill background jobs
    jobs -p | xargs -r kill
    
    echo "✓ All services stopped"
    exit 0
}

# Set trap to cleanup on Ctrl+C
trap cleanup SIGINT SIGTERM

# Start Celery worker in background
echo "Starting Celery worker..."
python -m celery -A cojourneyhub worker --loglevel=info &
CELERY_WORKER_PID=$!

# Start Celery beat in background
echo "Starting Celery beat..."
python -m celery -A cojourneyhub beat --loglevel=info &
CELERY_BEAT_PID=$!

# Wait a moment for services to start
sleep 3

# Start Django server in foreground
echo "Starting Django server..."
echo ""
echo "================================================"
echo "🎉 All services started successfully!"
echo "📊 Services running:"
echo "   - Redis: localhost:6379"
echo "   - Django: http://localhost:8000"
echo "   - Celery Worker: Background tasks (PID: $CELERY_WORKER_PID)"
echo "   - Celery Beat: Scheduled tasks (PID: $CELERY_BEAT_PID)"
echo ""
echo "💡 Press Ctrl+C to stop all services"
echo "================================================"
echo ""

# Start Django server (this will run in foreground)
python manage.py runserver 0.0.0.0:8000
