from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Count, Sum
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from datetime import datetime, timedelta
from .models import Ride, RideBooking, RideRequest, RidePending
from .serializers import (
    RideSerializer, RideCreateSerializer, RideListSerializer,
    RideBookingSerializer, RideBookingCreateSerializer,
    RideRequestSerializer, RideSearchSerializer, RideStatsSerializer,
    RidePendingSerializer, RidePendingCreateSerializer, RidePendingResponseSerializer
)
from .email_service import RideEmailService


@method_decorator(csrf_exempt, name='dispatch')
class RideListCreateView(generics.ListCreateAPIView):
    """List and create rides"""

    def get_permissions(self):
        """Allow anyone to view and create rides for testing"""
        return [permissions.AllowAny()]

    def get_queryset(self):
        return Ride.objects.filter(status='active').select_related('driver')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RideCreateSerializer
        return RideListSerializer

    def perform_create(self, serializer):
        # Only drivers can create rides
        if not self.request.user.is_authenticated:
            raise permissions.PermissionDenied("Authentication required")

        if self.request.user.role != 'driver':
            raise permissions.PermissionDenied("Only drivers can create rides")

        # Get the driver's User instance (not Driver model)
        driver_user = self.request.user
        serializer.save(driver=driver_user)

    def create(self, request, *args, **kwargs):
        """Override create to return full ride details with driver info"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        # Get the created ride with full details
        ride = serializer.instance
        ride.refresh_from_db()

        # Return full ride details using RideSerializer
        response_serializer = RideSerializer(ride)
        headers = self.get_success_headers(response_serializer.data)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class RideDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Ride detail, update, and delete"""

    serializer_class = RideSerializer

    def get_permissions(self):
        """Allow anyone to view ride details, but require auth for updates/deletes"""
        if self.request.method == 'GET':
            return [permissions.AllowAny()]
        return [permissions.IsAuthenticated()]

    def get_queryset(self):
        return Ride.objects.select_related('driver').prefetch_related('waypoints', 'bookings')

    def perform_update(self, serializer):
        # Only the driver can update their ride
        if serializer.instance.driver != self.request.user:
            raise permissions.PermissionDenied("You can only update your own rides")
        serializer.save()

    def perform_destroy(self, instance):
        # Only the driver can delete their ride
        if instance.driver != self.request.user:
            raise permissions.PermissionDenied("You can only delete your own rides")
        instance.delete()


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def search_rides(request):
    """Advanced ride search with filters"""

    serializer = RideSearchSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    filters = Q(status='active')
    data = serializer.validated_data

    # Text-based location search
    if data.get('origin'):
        filters &= Q(origin__icontains=data['origin'])

    if data.get('destination'):
        filters &= Q(destination__icontains=data['destination'])

    # Date and time filters
    if data.get('departure_date'):
        departure_date = data['departure_date']
        filters &= Q(departure_time__date=departure_date)

    if data.get('departure_time_from'):
        filters &= Q(departure_time__time__gte=data['departure_time_from'])

    if data.get('departure_time_to'):
        filters &= Q(departure_time__time__lte=data['departure_time_to'])

    # Price filter
    if data.get('max_price'):
        filters &= Q(price__lte=data['max_price'])

    # Seats filter
    if data.get('min_seats'):
        filters &= Q(seats_available__gte=data['min_seats'])

    # Features filter
    if data.get('features'):
        for feature in data['features']:
            filters &= Q(features__contains=[feature])

    rides = Ride.objects.filter(filters).select_related('driver')

    # TODO: Add geographic search if coordinates are provided
    # This would require PostGIS for efficient spatial queries

    serializer = RideListSerializer(rides, many=True)
    return Response(serializer.data)


class UserRidesView(generics.ListAPIView):
    """Get user's offered rides"""

    serializer_class = RideListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Ride.objects.filter(driver=self.request.user).order_by('-created_at')


class RideBookingListCreateView(generics.ListCreateAPIView):
    """List and create ride bookings"""

    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        # For unauthenticated users, get bookings for the first user
        if self.request.user.is_authenticated:
            user = self.request.user
        else:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user = User.objects.first()
            if not user:
                return RideBooking.objects.none()

        return RideBooking.objects.filter(user=user).select_related('ride', 'ride__driver')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RideBookingCreateSerializer
        return RideBookingSerializer

    def perform_create(self, serializer):
        # Get user (authenticated or default for testing)
        if self.request.user.is_authenticated:
            user = self.request.user
        else:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user = User.objects.first()
            if not user:
                # Create a default user for testing
                user = User.objects.create_user(
                    username='default_passenger',
                    email='<EMAIL>',
                    full_name='Default Passenger',
                    password='testpass123'
                )

        # Save the booking
        booking = serializer.save(user=user)

        # Send email confirmations
        try:
            # Send confirmation to passenger
            RideEmailService.send_booking_confirmation(booking)

            # Send notification to driver
            RideEmailService.send_booking_notification_to_driver(booking)
        except Exception as e:
            # Log error but don't fail the booking
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send booking emails: {str(e)}")

        return booking


class RideBookingDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Ride booking detail, update, and cancel"""

    serializer_class = RideBookingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return RideBooking.objects.filter(user=self.request.user)

    def perform_destroy(self, instance):
        # Cancel the booking instead of deleting
        instance.status = 'cancelled'
        instance.save()


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def book_ride(request, ride_id):
    """Book a specific ride"""

    try:
        ride = Ride.objects.get(id=ride_id, status='active')
    except Ride.DoesNotExist:
        return Response({'error': 'Ride not found'}, status=status.HTTP_404_NOT_FOUND)

    # Get user (authenticated or default for testing)
    if request.user.is_authenticated:
        user = request.user
    else:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = User.objects.first()
        if not user:
            # Create a default user for testing
            user = User.objects.create_user(
                username='default_passenger_2',
                email='<EMAIL>',
                full_name='Default Passenger 2',
                password='testpass123'
            )

    # Check if user can book this ride
    can_book, message = ride.can_be_booked_by(user)
    if not can_book:
        return Response({'error': message}, status=status.HTTP_400_BAD_REQUEST)

    # Create booking
    booking_data = {
        'ride': ride.id,
        'seats_booked': request.data.get('seats_booked', 1),
        'pickup_location': request.data.get('pickup_location', ''),
        'dropoff_location': request.data.get('dropoff_location', ''),
        'special_requests': request.data.get('special_requests', ''),
    }

    serializer = RideBookingCreateSerializer(data=booking_data, context={'request': request})
    serializer.is_valid(raise_exception=True)
    booking = serializer.save(user=user)

    # Send email confirmations
    try:
        # Send confirmation to passenger
        RideEmailService.send_booking_confirmation(booking)

        # Send notification to driver
        RideEmailService.send_booking_notification_to_driver(booking)
    except Exception as e:
        # Log error but don't fail the booking
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send booking emails: {str(e)}")

    return Response(RideBookingSerializer(booking).data, status=status.HTTP_201_CREATED)


@api_view(['POST'])
def cancel_booking(request, booking_id):
    """Cancel a ride booking"""

    try:
        booking = RideBooking.objects.get(id=booking_id, user=request.user)
    except RideBooking.DoesNotExist:
        return Response({'error': 'Booking not found'}, status=status.HTTP_404_NOT_FOUND)

    if booking.status == 'cancelled':
        return Response({'error': 'Booking already cancelled'}, status=status.HTTP_400_BAD_REQUEST)

    booking.status = 'cancelled'
    booking.save()

    return Response({'message': 'Booking cancelled successfully'})


class RideRequestListCreateView(generics.ListCreateAPIView):
    """List and create ride requests"""

    serializer_class = RideRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return RideRequest.objects.filter(passenger=self.request.user)


@api_view(['GET'])
def ride_stats(request):
    """Get ride statistics for the current user"""

    user = request.user

    # Driver statistics
    offered_rides = Ride.objects.filter(driver=user)
    driver_stats = offered_rides.aggregate(
        total_rides=Count('id'),
        active_rides=Count('id', filter=Q(status='active')),
        completed_rides=Count('id', filter=Q(status='completed')),
        cancelled_rides=Count('id', filter=Q(status='cancelled')),
    )

    # Passenger statistics
    bookings = RideBooking.objects.filter(user=user)
    passenger_stats = bookings.aggregate(
        total_bookings=Count('id'),
        total_spent=Sum('amount_paid'),
    )

    # Revenue from offered rides
    revenue = RideBooking.objects.filter(
        ride__driver=user,
        status='completed'
    ).aggregate(total_revenue=Sum('amount_paid'))

    stats = {
        **driver_stats,
        'total_bookings': passenger_stats['total_bookings'] or 0,
        'total_spent': passenger_stats['total_spent'] or 0,
        'total_revenue': revenue['total_revenue'] or 0,
    }

    serializer = RideStatsSerializer(stats)
    return Response(serializer.data)


@api_view(['GET'])
def nearby_rides(request):
    """Get rides near user's location"""

    lat = request.GET.get('lat')
    lng = request.GET.get('lng')
    radius = int(request.GET.get('radius', 50))  # km

    if not lat or not lng:
        return Response({'error': 'Latitude and longitude required'}, status=status.HTTP_400_BAD_REQUEST)

    # For now, return all active rides
    # TODO: Implement geographic search with PostGIS
    rides = Ride.objects.filter(status='active').select_related('driver')
    serializer = RideListSerializer(rides, many=True)
    return Response(serializer.data)


class RidePendingListCreateView(generics.ListCreateAPIView):
    """List and create pending ride bookings"""

    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.role == 'driver':
            # Drivers see pending bookings for their rides
            return RidePending.objects.filter(
                ride__driver=user,
                status='pending'
            ).select_related('ride', 'passenger')
        elif user.role == 'passenger':
            # Passengers see their own pending bookings
            return RidePending.objects.filter(
                passenger=user
            ).select_related('ride', 'ride__driver')
        return RidePending.objects.none()

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RidePendingCreateSerializer
        return RidePendingSerializer

    def perform_create(self, serializer):
        # Only passengers can create pending bookings
        if self.request.user.role != 'passenger':
            raise permissions.PermissionDenied("Only passengers can request ride bookings")

        pending_booking = serializer.save()

        # Send notification to driver using Celery
        from notifications.tasks import send_ride_booking_notification
        send_ride_booking_notification.delay(
            pending_booking.ride.driver.id,
            pending_booking.passenger.full_name,
            pending_booking.ride.origin,
            pending_booking.ride.destination
        )

        return pending_booking


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def respond_to_pending_booking(request, pending_id):
    """Driver responds to a pending booking request"""

    try:
        pending_booking = RidePending.objects.get(
            id=pending_id,
            ride__driver=request.user,
            status='pending'
        )
    except RidePending.DoesNotExist:
        return Response({'error': 'Pending booking not found'}, status=status.HTTP_404_NOT_FOUND)

    # Only drivers can respond to pending bookings
    if request.user.role != 'driver':
        return Response({'error': 'Only drivers can respond to booking requests'}, status=status.HTTP_403_FORBIDDEN)

    serializer = RidePendingResponseSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    action = serializer.validated_data['action']
    driver_response = serializer.validated_data.get('driver_response', '')

    if action == 'confirm':
        booking = pending_booking.confirm_booking(driver_response)
        if booking:
            # Send notification to passenger using Celery
            from notifications.tasks import send_booking_confirmation_notification
            send_booking_confirmation_notification.delay(
                pending_booking.passenger.id,
                pending_booking.ride.origin,
                pending_booking.ride.destination,
                'confirmed'
            )

            return Response({
                'message': 'Booking confirmed successfully',
                'booking': RideBookingSerializer(booking).data
            })
        else:
            return Response({'error': 'Unable to confirm booking - no seats available'}, status=status.HTTP_400_BAD_REQUEST)

    elif action == 'reject':
        pending_booking.reject_booking(driver_response)

        # Send notification to passenger using Celery
        from notifications.tasks import send_booking_confirmation_notification
        send_booking_confirmation_notification.delay(
            pending_booking.passenger.id,
            pending_booking.ride.origin,
            pending_booking.ride.destination,
            'rejected'
        )

        return Response({'message': 'Booking rejected'})

    return Response({'error': 'Invalid action'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def request_ride_booking(request, ride_id):
    """Create a pending booking request for a ride"""

    try:
        ride = Ride.objects.get(id=ride_id, status='active')
    except Ride.DoesNotExist:
        return Response({'error': 'Ride not found'}, status=status.HTTP_404_NOT_FOUND)

    # Only passengers can request bookings
    if request.user.role != 'passenger':
        return Response({'error': 'Only passengers can request ride bookings'}, status=status.HTTP_403_FORBIDDEN)

    # Prepare data for pending booking
    booking_data = {
        'ride': ride.id,
        'seats_requested': request.data.get('seats_requested', 1),
        'pickup_location': request.data.get('pickup_location', ''),
        'dropoff_location': request.data.get('dropoff_location', ''),
        'passenger_name': request.data.get('passenger_name', request.user.full_name),
        'passenger_phone': request.data.get('passenger_phone', request.user.phone_number),
        'special_requests': request.data.get('special_requests', ''),
    }

    serializer = RidePendingCreateSerializer(data=booking_data, context={'request': request})
    serializer.is_valid(raise_exception=True)
    pending_booking = serializer.save()

    # Send notification to driver using Celery
    from notifications.tasks import send_ride_booking_notification
    send_ride_booking_notification.delay(
        ride.driver.id,
        request.user.full_name,
        ride.origin,
        ride.destination
    )

    return Response(RidePendingSerializer(pending_booking).data, status=status.HTTP_201_CREATED)
