import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import Conversation, Message

User = get_user_model()


class ChatConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time chat"""
    
    async def connect(self):
        self.conversation_id = self.scope['url_route']['kwargs']['conversation_id']
        self.room_group_name = f'chat_{self.conversation_id}'
        
        # Check if user is authenticated and participant in conversation
        user = self.scope["user"]
        if user.is_anonymous:
            await self.close()
            return
        
        is_participant = await self.is_conversation_participant(user, self.conversation_id)
        if not is_participant:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type', 'chat_message')
        
        if message_type == 'chat_message':
            await self.handle_chat_message(text_data_json)
        elif message_type == 'typing':
            await self.handle_typing(text_data_json)
        elif message_type == 'mark_read':
            await self.handle_mark_read(text_data_json)
    
    async def handle_chat_message(self, data):
        message_content = data['message']
        user = self.scope["user"]
        
        # Save message to database
        message = await self.save_message(
            user, 
            self.conversation_id, 
            message_content,
            data.get('message_type', 'text')
        )
        
        if message:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': {
                        'id': message.id,
                        'content': message.content,
                        'message_type': message.message_type,
                        'sender': {
                            'id': message.sender.id,
                            'full_name': message.sender.full_name,
                            'avatar': message.sender.avatar.url if message.sender.avatar else None
                        },
                        'created_at': message.created_at.isoformat(),
                        'is_read': message.is_read
                    }
                }
            )
    
    async def handle_typing(self, data):
        user = self.scope["user"]
        is_typing = data.get('is_typing', False)
        
        # Send typing indicator to room group (excluding sender)
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator',
                'user_id': user.id,
                'user_name': user.full_name,
                'is_typing': is_typing
            }
        )
    
    async def handle_mark_read(self, data):
        user = self.scope["user"]
        message_id = data.get('message_id')
        
        if message_id:
            await self.mark_message_read(user, message_id)
            
            # Notify other participants that message was read
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'message_read',
                    'message_id': message_id,
                    'reader_id': user.id
                }
            )
    
    # Receive message from room group
    async def chat_message(self, event):
        message = event['message']
        
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': message
        }))
    
    async def typing_indicator(self, event):
        user = self.scope["user"]
        
        # Don't send typing indicator to the user who is typing
        if event['user_id'] != user.id:
            await self.send(text_data=json.dumps({
                'type': 'typing_indicator',
                'user_id': event['user_id'],
                'user_name': event['user_name'],
                'is_typing': event['is_typing']
            }))
    
    async def message_read(self, event):
        user = self.scope["user"]
        
        # Don't send read receipt to the reader
        if event['reader_id'] != user.id:
            await self.send(text_data=json.dumps({
                'type': 'message_read',
                'message_id': event['message_id'],
                'reader_id': event['reader_id']
            }))
    
    @database_sync_to_async
    def is_conversation_participant(self, user, conversation_id):
        """Check if user is a participant in the conversation"""
        try:
            conversation = Conversation.objects.get(id=conversation_id)
            return conversation.participants.filter(id=user.id).exists()
        except Conversation.DoesNotExist:
            return False
    
    @database_sync_to_async
    def save_message(self, user, conversation_id, content, message_type='text'):
        """Save message to database"""
        try:
            conversation = Conversation.objects.get(id=conversation_id)
            message = Message.objects.create(
                conversation=conversation,
                sender=user,
                content=content,
                message_type=message_type
            )
            
            # Update conversation timestamp
            conversation.save(update_fields=['updated_at'])
            
            return message
        except Conversation.DoesNotExist:
            return None
    
    @database_sync_to_async
    def mark_message_read(self, user, message_id):
        """Mark a message as read"""
        try:
            message = Message.objects.get(id=message_id)
            if message.sender != user:
                message.mark_as_read(user)
        except Message.DoesNotExist:
            pass
