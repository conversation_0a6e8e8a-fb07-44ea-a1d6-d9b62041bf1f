
import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { MapPin, Loader2, Navigation } from "lucide-react";
import { enhancedTunisiaLocations } from "@/data/enhancedTunisiaLocations";
import { useLanguage } from "@/contexts/LanguageContext";
import { useOnClickOutside } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface LocationInputProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

// Add more locations if needed
const additionalLocations = [
  // Add any missing Tunisian locations here
  "Carthage", "La Marsa", "Manouba", "Ben Arous", "Ariana", "Monastir Airport",
  "Hammamet", "Mahdia", "Tabarka", "Kélibia", "Zarzis", "Tataouine",
  "Djerba", "El Jem", "Kairou<PERSON>", "Sid<PERSON> Bou Said", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
  "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>agh<PERSON>an", "<PERSON><PERSON><PERSON>"
];

// Combine all locations
const allLocations = [...new Set([...enhancedTunisiaLocations, ...additionalLocations])].sort();

const LocationInput = ({ placeholder, value, onChange, className }: LocationInputProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(value || "");
  const [filteredLocations, setFilteredLocations] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const { language, translations } = useLanguage();
  const isRTL = language === "ar";

  // Handle clicks outside of the component
  useOnClickOutside(wrapperRef, () => setIsOpen(false));

  // Update component when value prop changes
  useEffect(() => {
    setSearchTerm(value);
  }, [value]);

  // Filter locations when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredLocations([]);
      return;
    }

    // Simulate loading state
    setIsLoading(true);
    
    // Use setTimeout to simulate API call
    const timerId = setTimeout(() => {
      // More sophisticated matching algorithm
      // This will search for matches at the beginning of words, and rank them higher
      const searchTermLower = searchTerm.toLowerCase();
      const matches = allLocations
        .filter(location => location.toLowerCase().includes(searchTermLower))
        .sort((a, b) => {
          // Prioritize exact matches and matches at the beginning
          const aLower = a.toLowerCase();
          const bLower = b.toLowerCase();
          
          // Exact match gets highest priority
          if (aLower === searchTermLower) return -1;
          if (bLower === searchTermLower) return 1;
          
          // Starts with gets second priority
          const aStartsWith = aLower.startsWith(searchTermLower);
          const bStartsWith = bLower.startsWith(searchTermLower);
          if (aStartsWith && !bStartsWith) return -1;
          if (!aStartsWith && bStartsWith) return 1;
          
          // Word starts with gets third priority
          const aWordStarts = aLower.split(' ').some(word => word.startsWith(searchTermLower));
          const bWordStarts = bLower.split(' ').some(word => word.startsWith(searchTermLower));
          if (aWordStarts && !bWordStarts) return -1;
          if (!aWordStarts && bWordStarts) return 1;
          
          // Default to alphabetical sorting
          return a.localeCompare(b);
        })
        .slice(0, 10); // Show more suggestions but not too many
      
      setFilteredLocations(matches);
      setIsLoading(false);
    }, 300); // Slight delay to prevent excessive computation
    
    return () => clearTimeout(timerId);
  }, [searchTerm]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    if (newValue.trim() === "") {
      onChange("");
    }
    setIsOpen(true);
  };

  const handleLocationSelect = (location: string) => {
    setSearchTerm(location);
    onChange(location);
    setIsOpen(false);
  };

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error(translations.locationNotSupported || "Geolocation is not supported by your browser");
      return;
    }

    setIsGettingLocation(true);
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        // In a real app, you would use the coordinates to get the location name from an API
        // Here we'll simulate finding a location
        const getNearbyLocation = () => {
          // Simulate finding a nearby city based on common Tunisian coordinates
          const randomCity = allLocations[Math.floor(Math.random() * allLocations.length)];
          return randomCity;
        };
        
        const nearbyLocation = getNearbyLocation();
        setSearchTerm(nearbyLocation);
        onChange(nearbyLocation);
        toast.success(translations.locationFound || "Location found");
        setIsGettingLocation(false);
      },
      (error) => {
        console.error("Error getting location:", error);
        toast.error(translations.locationError || "Could not get your location");
        setIsGettingLocation(false);
      },
      { timeout: 5000 }
    );
  };

  return (
    <div ref={wrapperRef} className="relative w-full">
      <div className="relative">
        <div className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none`}>
          <MapPin className="h-5 w-5" />
        </div>
        <Input
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          className={`${isRTL ? 'pr-10 text-right' : 'pl-10'} pr-10 w-full py-2 dark:bg-gray-800 dark:text-white dark:border-gray-700 ${className || ''}`}
        />
        <div className="absolute top-1/2 transform -translate-y-1/2 right-3">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 p-0 text-gray-400 hover:text-primary"
            onClick={getCurrentLocation}
            disabled={isGettingLocation}
            title={translations.useCurrentLocation || "Use current location"}
          >
            {isGettingLocation ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Navigation className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
      
      {isOpen && (
        <div className="absolute z-20 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
          {isLoading ? (
            <div className="p-4 text-center">
              <Loader2 className="h-5 w-5 animate-spin mx-auto text-primary" />
              <p className="text-sm text-gray-500 dark:text-gray-300 mt-2">
                {translations.searchingLocations || "Searching locations..."}
              </p>
            </div>
          ) : filteredLocations.length > 0 ? (
            <ul className={`py-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {filteredLocations.map((location, index) => (
                <li
                  key={index}
                  className="px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white transition-colors"
                  onClick={() => handleLocationSelect(location)}
                >
                  <div className="flex items-center">
                    <MapPin className={`h-4 w-4 text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span className="flex-1">{location}</span>
                  </div>
                </li>
              ))}
            </ul>
          ) : searchTerm.trim() !== "" ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-300">
              {translations.noLocationsFound || "No locations found"}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default LocationInput;
