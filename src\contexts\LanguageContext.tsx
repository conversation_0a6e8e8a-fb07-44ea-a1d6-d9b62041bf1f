
import React, { createContext, useContext, useState, useEffect } from "react";

type Language = "en" | "fr" | "ar";

type LanguageContextType = {
  language: Language;
  setLanguage: (language: Language) => void;
  translations: Record<string, string>;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// English is our default/fallback language
const translations = {
  en: {
    findRide: "Find a Ride",
    offerRide: "Offer a Ride",
    howItWorks: "How It Works",
    login: "Login",
    signup: "Sign Up",
    from: "From",
    to: "To",
    date: "Date",
    passengers: "Passengers",
    search: "Search",
    travelTogether: "Travel together, Sesame Students",
    travelDescription: "Join fellow Sesame University students sharing rides across Tunisia. Save on travel costs and make new connections on your way to classes and back home.",
    origin: "Origin",
    destination: "Destination",
    selectDate: "Select Date",
    departureDate: "Departure Date",
    departureTime: "Departure Time",
    availableSeats: "Available Seats",
    pricePerSeat: "Price per Seat (TND)",
    carModel: "Car Model",
    carColor: "Car Color",
    publishing: "Publishing...",
    publishRide: "Publish Ride",
    offerRideTitle: "Offer a Ride",
    offerRideDescription: "Share your journey with fellow Sesame students and help build our community while reducing costs.",
    searchingForRides: "Searching for rides...",
    pleaseEnterStartingPoint: "Please enter a starting point",
    pleaseEnterDestination: "Please enter a destination",
    pleaseSelectTravelDate: "Please select a travel date",
    ridePostedSuccessfully: "Your ride has been posted successfully!",
    // Login page translations
    enterCredentials: "Enter your credentials to access your account",
    email: "Email",
    password: "Password",
    forgotPassword: "Forgot password?",
    loggingIn: "Logging in...",
    dontHaveAccount: "Don't have an account?",
    loginSuccessful: "Login Successful",
    welcomeBack: "Welcome back to CoSesameHub!",
    loginFailed: "Login Failed",
    invalidCredentials: "Invalid email or password",
    // Signup page translations
    createAccount: "Create an account",
    enterInformation: "Enter your information to create an account",
    fullName: "Full Name",
    confirmPassword: "Confirm Password",
    creatingAccount: "Creating account...",
    alreadyHaveAccount: "Already have an account?",
    accountCreated: "Account created",
    welcomeToCoSesameHub: "Welcome to CoSesameHub!",
    signupFailed: "Signup Failed",
    errorCreatingAccount: "There was an error creating your account",
    // Ride list translations
    availableRides: "Available Rides",
    noRidesFound: "No rides found",
    noRidesDescription: "Try adjusting your search criteria or offer a ride yourself!",
    departingFrom: "Departing from",
    goingTo: "Going to",
    seatsLeft: "seats left",
    bookNow: "Book Now",
    // Loyalty program translations
    loyaltyPoints: "Loyalty Points",
    pointsEarned: "Points Earned",
    redeemPoints: "Redeem Points",
    pointsNeededForReward: "points needed for this reward",
    earnPointsBy: "Earn points by",
    offeringRides: "Offering rides",
    completingRides: "Completing rides",
    referringFriends: "Referring friends",
    redeemForRewards: "Redeem for Rewards",
    // Additional feature translations
    pickupPoint: "Pickup Point",
    additionalNotes: "Additional Notes",
    cancelRide: "Cancel Ride",
    reportIssue: "Report Issue",
    saved: "Saved",
    yourLocation: "Your Location",
    enterAddress: "Enter address, place, or landmark...",
    searchingLocation: "Searching location...",
    noResults: "No results found",
    tryDifferentSearch: "Try a different search term",
    // Add missing loyalty program translations
    pointsHistoryTitle: "Points History",
    recentActivity: "Recent Activity",
    earnedOn: "Earned on",
    noRecentActivity: "No recent activity",
    redeemableRewards: "Redeemable Rewards",
    // Footer pages and sections
    company: "Company",
    aboutUsTitle: "About Us",
    aboutUsIntro: "Welcome to CoSesameHub",
    teamTitle: "Our Team",
    careersTitle: "Careers",
    resources: "Resources",
    helpCenterTitle: "Help Center",
    safetyTitle: "Safety Guidelines",
    driverReqTitle: "Driver Requirements",
    communityRulesTitle: "Community Rules",
    legal: "Legal",
    termsTitle: "Terms of Service",
    privacyTitle: "Privacy Policy",
    cookieTitle: "Cookie Policy",
    trustTitle: "Trust & Safety",
    footerTagline: "Connecting travelers for convenient, affordable, and eco-friendly journeys.",
    allRightsReserved: "All rights reserved.",
    copyrightText: "© 2025 CoSesameHub. All rights reserved.",
    // Forgot password
    forgotPasswordTitle: "Forgot Password",
    forgotPasswordDescription: "Enter your email and we'll send you a link to reset your password",
    resetPasswordButton: "Send Reset Link",
    backToLogin: "Back to Login",
    resetLinkSent: "Reset link sent",
    checkEmail: "Please check your email for the reset link",
    resetFailed: "Reset link failed",
    emailNotFound: "Email not found in our system",
    emailPlaceholder: "Enter your email address",
    // Ride publish success
    loyaltyPointsAdded: "{points} loyalty points added to your account!",
    loginRequiredForOfferRide: "Please login to offer a ride",
    // Date in other languages
    today: "Today",
    month: "Month",
    year: "Year",
    day: "Day",
    weekdays: "Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday",
    months: "January,February,March,April,May,June,July,August,September,October,November,December",
    // New features translations
    clear: "Clear",
    filtersCleared: "Filters cleared - showing all available rides",
    loginRequiredForBooking: "Please login to book this ride",
    alreadyBooked: "You have already booked this ride",
    rideBookedSuccess: "Ride booked successfully!",
    loginRequiredForContact: "Please login to contact the driver",
    pleaseLoginToSearch: "Please login to search for rides",
    searchResults: "Search Results",
    allRides: "All available rides",
    list: "List",
    map: "Map",
    searchRides: "Search Rides",
    findPerfectRide: "Find the perfect ride for your journey",
    messages: "Messages",
    stayConnected: "Stay connected with your fellow travelers",
    selectConversation: "Select a conversation",
    selectConversationDesc: "Choose a conversation from the list to start chatting",
    noConversations: "No conversations yet",
    noConversationsDesc: "Start by booking a ride or offering one to connect with other travelers",
    findRides: "Find Rides",
    conversations: "Conversations",
    chat: "Chat",
    back: "Back",
  },
  fr: {
    findRide: "Trouver un Trajet",
    offerRide: "Proposer un Trajet",
    howItWorks: "Comment ça Marche",
    login: "Connexion",
    signup: "S'inscrire",
    from: "De",
    to: "À",
    date: "Date",
    passengers: "Passagers",
    search: "Rechercher",
    travelTogether: "Voyagez ensemble, Étudiants de Sésame",
    travelDescription: "Rejoignez d'autres étudiants de l'Université Sésame partageant des trajets à travers la Tunisie. Économisez sur les frais de voyage et créez de nouvelles connexions sur le chemin des cours et de retour chez vous.",
    origin: "Origine",
    destination: "Destination",
    selectDate: "Sélectionner la Date",
    departureDate: "Date de Départ",
    departureTime: "Heure de Départ",
    availableSeats: "Sièges Disponibles",
    pricePerSeat: "Prix par Siège (TND)",
    carModel: "Modèle de Voiture",
    carColor: "Couleur de Voiture",
    publishing: "Publication en cours...",
    publishRide: "Publier le Trajet",
    offerRideTitle: "Proposer un Trajet",
    offerRideDescription: "Partagez votre voyage avec d'autres étudiants de Sésame et aidez à construire notre communauté tout en réduisant les coûts.",
    searchingForRides: "Recherche de trajets...",
    pleaseEnterStartingPoint: "Veuillez entrer un point de départ",
    pleaseEnterDestination: "Veuillez entrer une destination",
    pleaseSelectTravelDate: "Veuillez sélectionner une date de voyage",
    ridePostedSuccessfully: "Votre trajet a été publié avec succès !",
    // Login page translations
    enterCredentials: "Entrez vos identifiants pour accéder à votre compte",
    email: "Email",
    password: "Mot de passe",
    forgotPassword: "Mot de passe oublié ?",
    loggingIn: "Connexion en cours...",
    dontHaveAccount: "Vous n'avez pas de compte ?",
    loginSuccessful: "Connexion Réussie",
    welcomeBack: "Bienvenue à nouveau sur CoSesameHub !",
    loginFailed: "Échec de Connexion",
    invalidCredentials: "Email ou mot de passe invalide",
    // Signup page translations
    createAccount: "Créer un compte",
    enterInformation: "Entrez vos informations pour créer un compte",
    fullName: "Nom Complet",
    confirmPassword: "Confirmer le Mot de passe",
    creatingAccount: "Création du compte...",
    alreadyHaveAccount: "Vous avez déjà un compte ?",
    accountCreated: "Compte créé",
    welcomeToCoSesameHub: "Bienvenue sur CoSesameHub !",
    signupFailed: "Échec de l'Inscription",
    errorCreatingAccount: "Une erreur s'est produite lors de la création de votre compte",
    // Ride list translations
    availableRides: "Trajets Disponibles",
    noRidesFound: "Aucun trajet trouvé",
    noRidesDescription: "Essayez d'ajuster vos critères de recherche ou proposez vous-même un trajet !",
    departingFrom: "Départ de",
    goingTo: "Destination",
    seatsLeft: "places restantes",
    bookNow: "Réserver",
    // Loyalty program translations
    loyaltyPoints: "Points de Fidélité",
    pointsEarned: "Points Gagnés",
    redeemPoints: "Échanger des Points",
    pointsNeededForReward: "points nécessaires pour cette récompense",
    earnPointsBy: "Gagnez des points en",
    offeringRides: "Proposant des trajets",
    completingRides: "Complétant des trajets",
    referringFriends: "Parrainant des amis",
    redeemForRewards: "Échanger contre des Récompenses",
    // Additional feature translations
    pickupPoint: "Point de Ramassage",
    additionalNotes: "Notes Supplémentaires",
    cancelRide: "Annuler le Trajet",
    reportIssue: "Signaler un Problème",
    saved: "Enregistré",
    yourLocation: "Votre Emplacement",
    enterAddress: "Entrez une adresse, un lieu ou un point de repère...",
    searchingLocation: "Recherche d'emplacement...",
    noResults: "Aucun résultat trouvé",
    tryDifferentSearch: "Essayez un terme de recherche différent",
    // Add missing loyalty program translations
    pointsHistoryTitle: "Historique des Points",
    recentActivity: "Activité Récente",
    earnedOn: "Gagné le",
    noRecentActivity: "Aucune activité récente",
    redeemableRewards: "Récompenses Échangeables",
    // Footer pages and sections
    company: "Entreprise",
    aboutUsTitle: "À Propos de Nous",
    aboutUsIntro: "Bienvenue à CoSesameHub",
    teamTitle: "Notre Équipe",
    careersTitle: "Carrières",
    resources: "Ressources",
    helpCenterTitle: "Centre d'Aide",
    safetyTitle: "Consignes de Sécurité",
    driverReqTitle: "Exigences pour les Conducteurs",
    communityRulesTitle: "Règles de la Communauté",
    legal: "Mentions Légales",
    termsTitle: "Conditions d'Utilisation",
    privacyTitle: "Politique de Confidentialité",
    cookieTitle: "Politique des Cookies",
    trustTitle: "Confiance et Sécurité",
    footerTagline: "Connecter les voyageurs pour des trajets pratiques, abordables et écologiques.",
    allRightsReserved: "Tous droits réservés.",
    copyrightText: "© 2025 CoSesameHub. Tous droits réservés.",
    // Forgot password
    forgotPasswordTitle: "Mot de Passe Oublié",
    forgotPasswordDescription: "Entrez votre email et nous vous enverrons un lien pour réinitialiser votre mot de passe",
    resetPasswordButton: "Envoyer le Lien de Réinitialisation",
    backToLogin: "Retour à la Connexion",
    resetLinkSent: "Lien de réinitialisation envoyé",
    checkEmail: "Veuillez vérifier votre email pour le lien de réinitialisation",
    resetFailed: "Échec du lien de réinitialisation",
    emailNotFound: "Email introuvable dans notre système",
    emailPlaceholder: "Entrez votre adresse email",
    // Ride publish success
    loyaltyPointsAdded: "{points} points de fidélité ajoutés à votre compte !",
    loginRequiredForOfferRide: "Veuillez vous connecter pour proposer un trajet",
    // Date in other languages
    today: "Aujourd'hui",
    month: "Mois",
    year: "Année",
    day: "Jour",
    weekdays: "Dimanche,Lundi,Mardi,Mercredi,Jeudi,Vendredi,Samedi",
    months: "Janvier,Février,Mars,Avril,Mai,Juin,Juillet,Août,Septembre,Octobre,Novembre,Décembre",
    // New features translations
    clear: "Effacer",
    filtersCleared: "Filtres effacés - affichage de tous les trajets disponibles",
    loginRequiredForBooking: "Veuillez vous connecter pour réserver ce trajet",
    alreadyBooked: "Vous avez déjà réservé ce trajet",
    rideBookedSuccess: "Trajet réservé avec succès !",
    loginRequiredForContact: "Veuillez vous connecter pour contacter le conducteur",
    pleaseLoginToSearch: "Veuillez vous connecter pour rechercher des trajets",
    searchResults: "Résultats de recherche",
    allRides: "Tous les trajets disponibles",
    list: "Liste",
    map: "Carte",
    searchRides: "Rechercher des Trajets",
    findPerfectRide: "Trouvez le trajet parfait pour votre voyage",
    messages: "Messages",
    stayConnected: "Restez connecté avec vos compagnons de voyage",
    selectConversation: "Sélectionner une conversation",
    selectConversationDesc: "Choisissez une conversation dans la liste pour commencer à discuter",
    noConversations: "Aucune conversation pour le moment",
    noConversationsDesc: "Commencez par réserver un trajet ou en proposer un pour vous connecter avec d'autres voyageurs",
    findRides: "Trouver des Trajets",
    conversations: "Conversations",
    chat: "Chat",
    back: "Retour",
  },
  ar: {
    findRide: "ابحث عن رحلة",
    offerRide: "اقترح رحلة",
    howItWorks: "كيف يعمل",
    login: "تسجيل الدخول",
    signup: "إنشاء حساب",
    from: "من",
    to: "إلى",
    date: "التاريخ",
    passengers: "الركاب",
    search: "بحث",
    travelTogether: "سافروا معًا، طلاب سيزام",
    travelDescription: "انضم إلى طلاب جامعة سيزام الذين يشاركون الرحلات في جميع أنحاء تونس. وفر في تكاليف السفر وكوّن علاقات جديدة في طريقك إلى الفصول والعودة إلى المنزل.",
    origin: "المصدر",
    destination: "الوجهة",
    selectDate: "اختر التاريخ",
    departureDate: "تاريخ المغادرة",
    departureTime: "وقت المغادرة",
    availableSeats: "المقاعد المتاحة",
    pricePerSeat: "السعر لكل مقعد (د.ت)",
    carModel: "موديل السيارة",
    carColor: "لون السيارة",
    publishing: "جاري النشر...",
    publishRide: "نشر الرحلة",
    offerRideTitle: "اقترح رحلة",
    offerRideDescription: "شارك رحلتك مع زملائك طلاب سيزام وساعد في بناء مجتمعنا مع تقليل التكاليف.",
    searchingForRides: "جاري البحث عن رحلات...",
    pleaseEnterStartingPoint: "الرجاء إدخال نقطة البداية",
    pleaseEnterDestination: "الرجاء إدخال الوجهة",
    pleaseSelectTravelDate: "الرجاء اختيار تاريخ السفر",
    ridePostedSuccessfully: "تم نشر رحلتك بنجاح!",
    // Login page translations
    enterCredentials: "أدخل بيانات الاعتماد الخاصة بك للوصول إلى حسابك",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    forgotPassword: "نسيت كلمة المرور؟",
    loggingIn: "جاري تسجيل الدخول...",
    dontHaveAccount: "ليس لديك حساب؟",
    loginSuccessful: "تم تسجيل الدخول بنجاح",
    welcomeBack: "مرحبًا بعودتك إلى CoSesameHub!",
    loginFailed: "فشل تسجيل الدخول",
    invalidCredentials: "بريد إلكتروني أو كلمة مرور غير صالحة",
    // Signup page translations
    createAccount: "إنشاء حساب",
    enterInformation: "أدخل معلوماتك لإنشاء حساب",
    fullName: "الاسم الكامل",
    confirmPassword: "تأكيد كلمة المرور",
    creatingAccount: "جاري إنشاء الحساب...",
    alreadyHaveAccount: "لديك حساب بالفعل؟",
    accountCreated: "تم إنشاء الحساب",
    welcomeToCoSesameHub: "مرحبًا بك في CoSesameHub!",
    signupFailed: "فشل التسجيل",
    errorCreatingAccount: "حدث خطأ أثناء إنشاء حسابك",
    // Ride list translations
    availableRides: "الرحلات المتاحة",
    noRidesFound: "لم يتم العثور على رحلات",
    noRidesDescription: "حاول تعديل معايير البحث أو اقترح رحلة بنفسك!",
    departingFrom: "المغادرة من",
    goingTo: "الذهاب إلى",
    seatsLeft: "مقاعد متبقية",
    bookNow: "احجز الآن",
    // Loyalty program translations
    loyaltyPoints: "نقاط الولاء",
    pointsEarned: "النقاط المكتسبة",
    redeemPoints: "استبدال النقاط",
    pointsNeededForReward: "النقاط اللازمة لهذه المكافأة",
    earnPointsBy: "اكسب النقاط عن طريق",
    offeringRides: "تقديم الرحلات",
    completingRides: "إكمال الرحلات",
    referringFriends: "دعوة الأصدقاء",
    redeemForRewards: "استبدلها بمكافآت",
    // Additional feature translations
    pickupPoint: "نقطة الالتقاء",
    additionalNotes: "ملاحظات إضافية",
    cancelRide: "إلغاء الرحلة",
    reportIssue: "الإبلاغ عن مشكلة",
    saved: "تم الحفظ",
    yourLocation: "موقعك",
    enterAddress: "أدخل عنوانًا أو مكانًا أو معلمًا...",
    searchingLocation: "جاري البحث عن الموقع...",
    noResults: "لم يتم العثور على نتائج",
    tryDifferentSearch: "جرب مصطلح بحث مختلف",
    // Add missing loyalty program translations
    pointsHistoryTitle: "سجل النقاط",
    recentActivity: "النشاط الأخير",
    earnedOn: "تم الحصول عليها في",
    noRecentActivity: "لا يوجد نشاط حديث",
    redeemableRewards: "المكافآت القابلة للاستبدال",
    // Footer pages and sections
    company: "الشركة",
    aboutUsTitle: "من نحن",
    aboutUsIntro: "مرحبًا بكم في CoSesameHub",
    teamTitle: "فريقنا",
    careersTitle: "الوظائف",
    resources: "الموارد",
    helpCenterTitle: "مركز المساعدة",
    safetyTitle: "إرشادات السلامة",
    driverReqTitle: "متطلبات السائق",
    communityRulesTitle: "قواعد المجتمع",
    legal: "القانونية",
    termsTitle: "شروط الخدمة",
    privacyTitle: "سياسة الخصوصية",
    cookieTitle: "سياسة ملفات تعريف الارتباط",
    trustTitle: "الثقة والأمان",
    footerTagline: "ربط المسافرين لرحلات مريحة وبأسعار معقولة وصديقة للبيئة.",
    allRightsReserved: "جميع الحقوق محفوظة.",
    copyrightText: "© 2025 CoSesameHub. جميع الحقوق محفوظة.",
    // Forgot password
    forgotPasswordTitle: "نسيت كلمة المرور",
    forgotPasswordDescription: "أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور",
    resetPasswordButton: "إرسال رابط إعادة التعيين",
    backToLogin: "العودة لتسجيل الدخول",
    resetLinkSent: "تم إرسال رابط إعادة التعيين",
    checkEmail: "يرجى التحقق من بريدك الإلكتروني للحصول على رابط إعادة التعيين",
    resetFailed: "فشل رابط إعادة التعيين",
    emailNotFound: "البريد الإلكتروني غير موجود في نظامنا",
    emailPlaceholder: "أدخل عنوان بريدك الإلكتروني",
    // Ride publish success
    loyaltyPointsAdded: "تمت إضافة {points} نقطة ولاء إلى حسابك!",
    loginRequiredForOfferRide: "يرجى تسجيل الدخول لاقتراح رحلة",
    // Date in other languages
    today: "اليوم",
    month: "الشهر",
    year: "السنة",
    day: "اليوم",
    weekdays: "الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس,الجمعة,السبت",
    months: "يناير,فبراير,مارس,أبريل,مايو,يونيو,يوليو,أغسطس,سبتمبر,أكتوبر,نوفمبر,ديسمبر",
    // New features translations
    clear: "مسح",
    filtersCleared: "تم مسح المرشحات - عرض جميع الرحلات المتاحة",
    loginRequiredForBooking: "يرجى تسجيل الدخول لحجز هذه الرحلة",
    alreadyBooked: "لقد حجزت هذه الرحلة بالفعل",
    rideBookedSuccess: "تم حجز الرحلة بنجاح!",
    loginRequiredForContact: "يرجى تسجيل الدخول للتواصل مع السائق",
    pleaseLoginToSearch: "يرجى تسجيل الدخول للبحث عن رحلات",
    searchResults: "نتائج البحث",
    allRides: "جميع الرحلات المتاحة",
    list: "قائمة",
    map: "خريطة",
    searchRides: "البحث عن رحلات",
    findPerfectRide: "ابحث عن الرحلة المثالية لرحلتك",
    messages: "الرسائل",
    stayConnected: "ابق على تواصل مع رفاق السفر",
    selectConversation: "اختر محادثة",
    selectConversationDesc: "اختر محادثة من القائمة لبدء الدردشة",
    noConversations: "لا توجد محادثات بعد",
    noConversationsDesc: "ابدأ بحجز رحلة أو عرض واحدة للتواصل مع مسافرين آخرين",
    findRides: "البحث عن رحلات",
    conversations: "المحادثات",
    chat: "دردشة",
    back: "رجوع",
  }
};

// Special CSS for RTL languages (Arabic)
const setRTLMode = (isRTL: boolean) => {
  if (isRTL) {
    document.documentElement.dir = 'rtl';
    document.documentElement.classList.add('rtl');
  } else {
    document.documentElement.dir = 'ltr';
    document.documentElement.classList.remove('rtl');
  }
};

export const LanguageProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  // Try to get the language from localStorage or use browser language, defaulting to 'en'
  const [language, setLanguageState] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem("language") as Language;
    if (savedLanguage && ["en", "fr", "ar"].includes(savedLanguage)) {
      return savedLanguage;
    }
    const browserLang = navigator.language.split('-')[0];
    if (browserLang === "fr") return "fr";
    if (browserLang === "ar") return "ar";
    return "en";
  });

  // Set the language and update localStorage
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    localStorage.setItem("language", newLanguage);
  };

  // Apply RTL styling for Arabic
  useEffect(() => {
    setRTLMode(language === "ar");
  }, [language]);

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      translations: translations[language]
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
