
import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Gift, Award, Star, Users, Car, Check } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { toast } from "sonner";

interface Reward {
  id: string;
  name: string;
  pointsRequired: number;
  description: string;
  icon: React.ReactNode;
}

interface LoyaltyPointsProps {
  isDialog?: boolean;
}

const LoyaltyPoints = ({ isDialog = false }: LoyaltyPointsProps) => {
  const { translations, language } = useLanguage();
  const { user, addLoyaltyPoints } = useUser();
  const [selectedTab, setSelectedTab] = useState("overview");
  const isRTL = language === "ar";

  const rewards: Reward[] = [
    {
      id: "free-ride",
      name: "Free Ride",
      pointsRequired: 500,
      description: "Get a free ride up to 20 TND value",
      icon: <Car className="h-6 w-6" />
    },
    {
      id: "priority",
      name: "Priority Matching",
      pointsRequired: 300,
      description: "Get priority in ride matching for 1 month",
      icon: <Star className="h-6 w-6" />
    },
    {
      id: "discount",
      name: "10% Discount",
      pointsRequired: 200,
      description: "10% discount on your next 5 rides",
      icon: <Award className="h-6 w-6" />
    }
  ];

  const handleRedeemPoints = async (reward: Reward) => {
    if (!user) return;
    
    if (user.loyaltyPoints >= reward.pointsRequired) {
      // Process the reward redemption through Supabase
      try {
        await addLoyaltyPoints(-reward.pointsRequired);
        toast.success(`You've redeemed ${reward.name}!`);
      } catch (error) {
        toast.error("Failed to redeem points. Please try again.");
        console.error("Error redeeming points:", error);
      }
    } else {
      toast.error(`You need ${reward.pointsRequired - user.loyaltyPoints} more points for this reward.`);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="w-full">
      <CardHeader className="px-0">
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse justify-end' : 'justify-between'}`}>
          <CardTitle className={isRTL ? 'text-right' : ''}>
            {translations.loyaltyPoints}
          </CardTitle>
          <Badge variant="outline" className="text-lg font-semibold bg-primary/10 px-3 py-1">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            {user.loyaltyPoints}
          </Badge>
        </div>
        <CardDescription className={isRTL ? 'text-right' : ''}>
          {translations.earnPointsBy}
        </CardDescription>
      </CardHeader>
      <CardContent className="px-0">
        <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview" className="px-2 py-1.5 text-sm">
              <span className="truncate">{translations.recentActivity || "Overview"}</span>
            </TabsTrigger>
            <TabsTrigger value="rewards" className="px-2 py-1.5 text-sm">
              <span className="truncate">{translations.redeemableRewards || "Rewards"}</span>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Car className="h-4 w-4 text-primary flex-shrink-0" />
                    <span className="truncate">{translations.offeringRides}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-1">
                  <div className="text-2xl font-bold">{user.ridesOffered || 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">+50 {translations.pointsEarned}</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="truncate">{translations.completingRides}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-1">
                  <div className="text-2xl font-bold">{user.ridesCompleted || 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">+30 {translations.pointsEarned}</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-500 flex-shrink-0" />
                    <span className="truncate">{translations.referringFriends}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-1">
                  <div className="text-2xl font-bold">{user.referrals || 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">+100 {translations.pointsEarned}</p>
                </CardContent>
              </Card>
            </div>
            
            <Button 
              variant="outline" 
              className="w-full mt-4"
              onClick={() => setSelectedTab("rewards")}
            >
              <Gift className="mr-2 h-4 w-4" /> {translations.redeemForRewards}
            </Button>
          </TabsContent>
          
          <TabsContent value="rewards" className="space-y-4 mt-4">
            <div className="grid gap-4">
              {rewards.map((reward) => (
                <Card key={reward.id} className="overflow-hidden">
                  <div className="flex flex-col md:flex-row">
                    <div className="p-6 flex items-center justify-center bg-primary/10 md:w-1/6 min-w-[80px]">
                      {reward.icon}
                    </div>
                    <div className="flex-1 p-4 md:p-6">
                      <CardTitle className="text-lg mb-2 truncate max-w-full">{reward.name}</CardTitle>
                      <CardDescription className="line-clamp-2 max-w-full text-gray-600 dark:text-gray-300">{reward.description}</CardDescription>
                      <div className="flex flex-wrap justify-between items-center mt-4 gap-2">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 mr-1 flex-shrink-0" />
                          <span className="text-sm whitespace-nowrap">
                            {reward.pointsRequired} 
                            <span className="hidden sm:inline ml-1">
                              {(translations.pointsNeeded || "points needed")}
                            </span>
                          </span>
                        </div>
                        <Button 
                          variant={user.loyaltyPoints >= reward.pointsRequired ? "default" : "outline"}
                          size="sm"
                          className="whitespace-nowrap"
                          onClick={() => handleRedeemPoints(reward)}
                          disabled={user.loyaltyPoints < reward.pointsRequired}
                        >
                          <span className="truncate max-w-[120px]">{translations.redeemPoints}</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="border-t pt-4 px-0">
        <p className="text-xs text-muted-foreground text-center w-full line-clamp-2 px-2">
          {translations.earnPointsBy} {translations.offeringRides?.toLowerCase()}, {translations.completingRides?.toLowerCase()}, 
          {translations.referringFriends?.toLowerCase()}
        </p>
      </CardFooter>
    </div>
  );
};

export default LoyaltyPoints;
