
import PageContainer from "@/components/PageContainer";
import { useLanguage } from "@/contexts/LanguageContext";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, Clock, Briefcase } from "lucide-react";

const CareersPage = () => {
  const { translations } = useLanguage();
  
  const openings = [
    {
      title: "Mobile App Developer",
      location: "Tunis, Tunisia (Remote)",
      type: "Full-time",
      description: "We're looking for an experienced mobile developer to help build our upcoming native mobile apps for iOS and Android.",
    },
    {
      title: "Community Manager",
      location: "Tunis, Tunisia (Hybrid)",
      type: "Full-time",
      description: "Help grow and manage our community of users, respond to inquiries, and ensure a positive user experience.",
    },
    {
      title: "Marketing Intern",
      location: "Tunis, Tunisia (On-site)",
      type: "Internship",
      description: "Assist with social media, campus outreach, and promotional events to increase awareness of our platform.",
    },
  ];
  
  return (
    <PageContainer title={translations.careersTitle || "Careers"} subtitle="Join our growing team">
      <div className="space-y-8">
        <div className="prose prose-lg dark:prose-invert max-w-none">
          <p>At CoSesameHub, we're building a platform that makes a real difference in the lives of Sesame University students. We're a small but growing team of passionate individuals who believe in our mission to make student transportation more accessible, affordable, and community-oriented.</p>
          
          <h2>Why Work With Us?</h2>
          <ul>
            <li><strong>Impact:</strong> Your work will directly affect thousands of students' daily lives</li>
            <li><strong>Growth:</strong> As a startup, we offer opportunities to wear multiple hats and grow your skills</li>
            <li><strong>Culture:</strong> We value innovation, collaboration, and work-life balance</li>
            <li><strong>Benefits:</strong> Competitive salary, flexible working arrangements, and learning opportunities</li>
          </ul>
        </div>
        
        <h2 className="text-2xl font-bold mt-10 mb-6 dark:text-white">Current Openings</h2>
        <div className="grid grid-cols-1 gap-6">
          {openings.map((job, index) => (
            <Card key={index} className="transition-all hover:shadow-md">
              <CardHeader>
                <CardTitle>{job.title}</CardTitle>
                <CardDescription className="flex flex-col sm:flex-row sm:gap-4 mt-2">
                  <span className="flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    {job.location}
                  </span>
                  <span className="flex items-center gap-1">
                    <Briefcase className="h-4 w-4 text-gray-500" />
                    {job.type}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300">{job.description}</p>
              </CardContent>
              <CardFooter>
                <Button className="transition-all hover:scale-[1.02]">Apply Now</Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg mt-10">
          <h2 className="text-xl font-bold mb-4 dark:text-white">Don't see a fitting role?</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-4">We're always interested in connecting with talented individuals. Send your resume and tell us how you could contribute to CoSesameHub.</p>
          <Button variant="outline">Send Open Application</Button>
        </div>
      </div>
    </PageContainer>
  );
};

export default CareersPage;
