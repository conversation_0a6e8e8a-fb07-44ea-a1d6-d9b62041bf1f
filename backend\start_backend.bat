@echo off
echo Starting CoJourneyHub Backend Services...
echo.

REM Check if Redis is installed
redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo Redis is not running. Please install and start Redis first.
    echo Download from: https://redis.io/download
    pause
    exit /b 1
)

echo Redis is running ✓
echo.

REM Start services in separate windows
echo Starting Celery Worker...
start "Celery Worker" cmd /k "python -m celery -A cojourneyhub worker --loglevel=info --pool=solo"

timeout /t 3 /nobreak >nul

echo Starting Celery Beat...
start "Celery Beat" cmd /k "python -m celery -A cojourneyhub beat --loglevel=info"

timeout /t 3 /nobreak >nul

echo Starting Django Server...
start "Django Server" cmd /k "python manage.py runserver 0.0.0.0:8000"

echo.
echo All services started! ✓
echo.
echo Services running:
echo - Redis: localhost:6379
echo - Django: http://localhost:8000
echo - Celery Worker: Background tasks
echo - Celery Beat: Scheduled tasks
echo.
echo Close this window or press Ctrl+C to stop all services
pause
