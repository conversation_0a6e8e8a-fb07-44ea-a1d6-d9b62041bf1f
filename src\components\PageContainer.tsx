
import React from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useLanguage } from "@/contexts/LanguageContext";

interface PageContainerProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
}

const PageContainer: React.FC<PageContainerProps> = ({ title, subtitle, children }) => {
  const { language } = useLanguage();
  const isRTL = language === "ar";
  
  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <Navbar />
      <main className="flex-1 container mx-auto px-4 py-12 max-w-4xl animate-fade-in">
        <div className={`mb-8 ${isRTL ? 'text-right' : 'text-left'} animate-fade-in animation-delay-150`}>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">{title}</h1>
          {subtitle && <p className="text-lg text-gray-600 dark:text-gray-400">{subtitle}</p>}
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 md:p-8 transition-all hover:shadow-md animate-fade-in animation-delay-300">
          {children}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default PageContainer;
