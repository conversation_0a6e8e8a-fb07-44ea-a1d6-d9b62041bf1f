import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ReviewsSection from "@/components/ReviewsSection";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { rideService } from "@/services/rideService";
import { toast } from "sonner";
import {
  MapPin,
  Clock,
  Users,
  Car,
  Star,
  MessageCircle,
  ArrowLeft,
  Calendar,
  DollarSign,
  Navigation,
  Shield,
  Wifi,
  Music,
  Snowflake,
  Ban
} from "lucide-react";

interface Ride {
  id: string;
  origin: string;
  destination: string;
  departure_time: string;
  arrival_time: string;
  price: number;
  distance: string;
  seats_available: number;
  car_model: string;
  car_color: string;
  driver: {
    id: string;
    full_name: string;
    email: string;
    rating: number;
    total_ratings: number;
  };
  features: string[];
}

const RideDetailsPage = () => {
  const { rideId } = useParams<{ rideId: string }>();
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const { user } = useUser();
  const isRTL = language === "ar";

  const [ride, setRide] = useState<Ride | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (rideId) {
      fetchRide();
    }
  }, [rideId]);

  const fetchRide = async () => {
    try {
      setLoading(true);
      const response = await rideService.getRide(rideId!);
      setRide(response);
    } catch (error) {
      console.error("Error fetching ride:", error);
      toast.error("Failed to load ride details");
      navigate("/search");
    } finally {
      setLoading(false);
    }
  };

  const handleStartChat = async () => {
    if (!ride) return;
    
    try {
      // Start a ride-specific conversation
      const response = await fetch(`/api/messaging/ride/${ride.id}/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const conversation = await response.json();
        navigate(`/messages?conversation=${conversation.id}`);
      } else {
        toast.error("Failed to start conversation");
      }
    } catch (error) {
      console.error("Error starting chat:", error);
      toast.error("Failed to start conversation");
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " at " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getFeatureIcon = (feature: string) => {
    const featureIcons: { [key: string]: any } = {
      'Air Conditioning': Snowflake,
      'Music': Music,
      'WiFi': Wifi,
      'GPS': Navigation,
      'Non-Smoking': Ban,
      'Safety': Shield,
    };
    
    const IconComponent = featureIcons[feature] || Shield;
    return <IconComponent className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!ride) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Ride not found
          </h2>
          <Button onClick={() => navigate("/search")}>
            Back to Search
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Ride Details
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="details" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">Trip Details</TabsTrigger>
                <TabsTrigger value="driver">Driver Info</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-6">
                {/* Trip Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Trip Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-lg">{ride.origin}</p>
                        <p className="text-sm text-gray-500">Departure</p>
                      </div>
                      <div className="text-center">
                        <div className="w-16 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
                        <p className="text-xs text-gray-500 mt-1">{ride.distance}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-lg">{ride.destination}</p>
                        <p className="text-sm text-gray-500">Arrival</p>
                      </div>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-500">Departure</p>
                          <p className="font-medium">{formatDateTime(ride.departure_time)}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-500">Arrival</p>
                          <p className="font-medium">{formatDateTime(ride.arrival_time)}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{ride.seats_available} seats available</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-semibold">{ride.price} TND per seat</span>
                      </div>
                    </div>

                    {ride.features.length > 0 && (
                      <div>
                        <p className="text-sm text-gray-500 mb-2">Features</p>
                        <div className="grid grid-cols-2 gap-2">
                          {ride.features.map((feature, index) => (
                            <div key={index} className="flex items-center gap-2">
                              {getFeatureIcon(feature)}
                              <span className="text-sm">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="driver" className="space-y-6">
                {/* Driver Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Car className="h-5 w-5" />
                      Driver & Vehicle
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 dark:text-blue-300 font-semibold text-xl">
                            {ride.driver.full_name?.charAt(0) || ride.driver.email.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-semibold text-lg">{ride.driver.full_name || ride.driver.email}</p>
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm">
                              {ride.driver.rating} ({ride.driver.total_ratings} reviews)
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">
                            {ride.car_color} {ride.car_model}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        onClick={handleStartChat}
                        className="flex items-center gap-2"
                      >
                        <MessageCircle className="h-4 w-4" />
                        Chat with Driver
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reviews" className="space-y-6">
                <ReviewsSection 
                  userId={ride.driver.id}
                  rideId={ride.id}
                  canLeaveReview={false}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Booking Sidebar */}
          <div>
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="text-center">
                  {ride.price} TND
                  <span className="text-sm font-normal text-gray-500 block">per seat</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center space-y-2">
                  <p className="text-sm text-gray-500">
                    {ride.seats_available} seats available
                  </p>
                  <p className="text-xs text-gray-400">
                    Payment in cash when you meet the driver
                  </p>
                </div>

                <Button
                  onClick={() => navigate(`/rides/${ride.id}/book`)}
                  className="w-full"
                  size="lg"
                >
                  Book This Ride
                </Button>

                <Button
                  variant="outline"
                  onClick={handleStartChat}
                  className="w-full flex items-center gap-2"
                >
                  <MessageCircle className="h-4 w-4" />
                  Contact Driver
                </Button>

                <Separator />

                <div className="text-xs text-gray-500 space-y-1">
                  <p>• Free cancellation up to 24h before departure</p>
                  <p>• Instant booking confirmation</p>
                  <p>• Secure payment</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RideDetailsPage;
