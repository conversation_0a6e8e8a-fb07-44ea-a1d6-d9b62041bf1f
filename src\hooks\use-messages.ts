
import { useCallback } from "react";
import { supabase } from "@/lib/supabase";
import { Message } from "@/data/rides";
import { useNotifications } from "./use-notifications";
import { messagingService } from "@/services/messagingService";

export function useMessages(userId: string | undefined) {
  const { addNotification } = useNotifications();

  const sendMessage = useCallback(async (recipientId: string, content: string): Promise<Message | undefined> => {
    if (!userId) return;

    try {
      console.log('Sending message to:', recipientId, 'content:', content);

      // First, create or get conversation with the recipient
      const conversation = await messagingService.createConversation(recipientId);
      if (!conversation) {
        console.error("Failed to create conversation");
        throw new Error("Failed to create conversation");
      }

      // Send the message via Django backend
      const message = await messagingService.sendMessage(conversation.id, content);
      if (!message) {
        console.error("Failed to send message");
        throw new Error("Failed to send message");
      }

      console.log('Message sent successfully:', message);

      // Add notification
      await addNotification({
        userId: recipientId,
        title: "New message",
        content: `Someone sent you a message: "${content.substring(0, 30)}${content.length > 30 ? '...' : ''}"`,
        type: "new_message",
        relatedId: message.id
      });

      // Convert Django message format to frontend format
      return {
        id: message.id,
        senderId: message.sender.id,
        recipientId: recipientId,
        content: message.content,
        timestamp: message.created_at,
        read: message.is_read
      };
    } catch (error) {
      console.error("Error sending message:", error);
      throw error;
    }
  }, [userId, addNotification]);

  const getMessages = useCallback(async (chatUserId: string): Promise<Message[]> => {
    if (!userId) return [];

    try {
      console.log('Fetching messages with user:', chatUserId);

      // Get conversations to find the one with the chat user
      const conversations = await messagingService.getConversations();
      const conversation = conversations.find(conv =>
        conv.participants.some(p => p.id === chatUserId)
      );

      if (!conversation) {
        console.log('No conversation found with user:', chatUserId);
        return [];
      }

      // Get messages for this conversation
      const messages = await messagingService.getMessages(conversation.id);
      console.log('Messages fetched:', messages);

      // Convert Django message format to frontend format
      return messages.map(msg => ({
        id: msg.id,
        senderId: msg.sender.id,
        recipientId: msg.sender.id === userId ? chatUserId : userId,
        content: msg.content,
        timestamp: msg.created_at,
        read: msg.is_read
      }));
    } catch (error) {
      console.error("Error fetching messages:", error);
      return [];
    }
  }, [userId]);

  const markMessageAsRead = useCallback(async (messageId: string): Promise<void> => {
    try {
      console.log('Marking message as read:', messageId);
      await messagingService.markMessageAsRead(messageId);
    } catch (error) {
      console.error("Error marking message as read:", error);
    }
  }, []);

  const hasUnreadMessages = useCallback(async (): Promise<boolean> => {
    if (!userId) return false;

    try {
      const conversations = await messagingService.getConversations();
      return conversations.some(conv => conv.unread_count > 0);
    } catch (error) {
      console.error("Error checking unread messages:", error);
      return false;
    }
  }, [userId]);

  return {
    sendMessage,
    getMessages,
    markMessageAsRead,
    hasUnreadMessages
  };
}
