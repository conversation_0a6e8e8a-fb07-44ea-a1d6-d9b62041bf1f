
import { useState, useEffect, useCallback } from "react";
import { apiClient } from "@/lib/supabase";
import { User } from "@/types/user";
import { toast } from "sonner";

export function useAuth() {
  const [user, setUserState] = useState<User | null>(() => {
    // Try to restore user from localStorage on initialization
    try {
      const savedUser = localStorage.getItem('user');
      const isAuthenticated = localStorage.getItem('isAuthenticated');
      if (savedUser && isAuthenticated === 'true') {
        return JSON.parse(savedUser);
      }
    } catch (error) {
      console.error('Error restoring user from localStorage:', error);
    }
    return null;
  });
  const [isLoading, setIsLoading] = useState(false);

  // Updated to ensure isAuthenticated shows true whenever we have a user object
  const isAuthenticated = !!user;

  // Login function
  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      console.log('Attempting Django login with:', { email });
      const response = await apiClient.post('/auth/login/', {
        email,
        password
      });

      console.log('Login response received:', response);

      if (response && response.user) {
        // Transform Django user to frontend User format
        const userData: User = {
          id: response.user.id.toString(),
          email: response.user.email,
          name: response.user.full_name || response.user.username,
          fullName: response.user.full_name,
          avatar: response.user.avatar || '',
          joinDate: response.user.created_at,
          ridesCompleted: response.user.rides_completed || 0,
          ridesOffered: response.user.rides_offered || 0,
          rating: parseFloat(response.user.rating) || 4.5,
          rideCount: response.user.ride_count || 0,
          loyaltyPoints: response.user.loyalty_points || 100,
          referrals: response.user.referrals || 0,
          role: response.user.role || 'passenger',
          phoneNumber: response.user.phone_number,
          city: response.user.city,
          country: response.user.country,
          bio: response.user.bio,
          dateOfBirth: response.user.date_of_birth,
          isVerified: response.user.is_verified || false,
          vehicles: response.user.vehicles || [],
        };

        console.log('Transformed user data:', userData);

        // Set user in state and localStorage
        setUserState(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isAuthenticated', 'true');

        toast.success('Login successful!');
        return true;
      } else {
        console.error('No user data in response:', response);
        return false;
      }
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle Django validation errors
      if (error.response?.data) {
        const errors = error.response.data;
        if (errors.non_field_errors) {
          toast.error(errors.non_field_errors[0]);
        } else if (errors.detail) {
          toast.error(errors.detail);
        } else {
          toast.error('Invalid email or password');
        }
      } else {
        // Fallback authentication for development
        console.warn('Django login failed, using fallback authentication for development');

        if (email.includes('@') && password.length >= 3) {
          const fallbackUser: User = {
            id: `fallback-${Date.now()}`,
            email: email,
            name: email.split('@')[0],
            fullName: email.split('@')[0],
            avatar: '',
            joinDate: new Date().toISOString(),
            ridesCompleted: 0,
            ridesOffered: 0,
            rating: 4.5,
            rideCount: 0,
            loyaltyPoints: 100,
            referrals: 0,
            role: 'both', // Allow both passenger and driver for fallback
            phoneNumber: '',
            city: '',
            country: 'Tunisia',
            bio: '',
            isVerified: false,
            vehicles: [],
          };

          setUserState(fallbackUser);
          localStorage.setItem('user', JSON.stringify(fallbackUser));
          localStorage.setItem('isAuthenticated', 'true');

          toast.success('Login successful (development mode)!');
          return true;
        }

        toast.error('Please enter valid email and password');
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register function
  const register = useCallback(async (userData: {
    email: string;
    password: string;
    full_name: string;
    phone_number?: string;
  }): Promise<boolean> => {
    setIsLoading(true);
    try {
      console.log('Attempting Django registration...');

      // Generate username from email
      const username = userData.email.split('@')[0];

      const registrationData = {
        email: userData.email,
        username: username,
        full_name: userData.full_name,
        password: userData.password,
        password_confirm: userData.password,
        phone_number: userData.phone_number || '',
        city: '',
        country: 'Tunisia'
      };

      console.log('Registration data:', registrationData);
      const response = await apiClient.post('/auth/register/', registrationData);

      console.log('Registration response:', response);

      if (response.user) {
        // Transform Django user to frontend User format
        const user: User = {
          id: response.user.id.toString(),
          email: response.user.email,
          name: response.user.full_name || response.user.username,
          fullName: response.user.full_name,
          avatar: response.user.avatar || '',
          joinDate: response.user.created_at,
          ridesCompleted: response.user.rides_completed || 0,
          ridesOffered: response.user.rides_offered || 0,
          rating: parseFloat(response.user.rating) || 4.5,
          rideCount: response.user.ride_count || 0,
          loyaltyPoints: response.user.loyalty_points || 100,
          referrals: response.user.referrals || 0,
          role: response.user.role || 'passenger',
          phoneNumber: response.user.phone_number,
          city: response.user.city,
          country: response.user.country,
          bio: response.user.bio,
          dateOfBirth: response.user.date_of_birth,
          isVerified: response.user.is_verified || false,
          vehicles: response.user.vehicles || [],
        };

        // Set user in state and localStorage
        setUserState(user);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('isAuthenticated', 'true');

        toast.success('Registration successful!');
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Registration error:', error);

      // Handle Django validation errors
      if (error.response?.data) {
        const errors = error.response.data;
        let errorMessage = 'Registration failed: ';

        if (errors.email) {
          errorMessage += errors.email[0] + ' ';
        }
        if (errors.username) {
          errorMessage += errors.username[0] + ' ';
        }
        if (errors.password) {
          errorMessage += errors.password[0] + ' ';
        }

        toast.error(errorMessage);
      } else {
        // Fallback registration for development
        console.warn('Django registration failed, using fallback registration for development');

        const fallbackUser: User = {
          id: `fallback-${Date.now()}`,
          email: userData.email,
          name: userData.full_name,
          fullName: userData.full_name,
          avatar: '',
          joinDate: new Date().toISOString(),
          ridesCompleted: 0,
          ridesOffered: 0,
          rating: 4.5,
          rideCount: 0,
          loyaltyPoints: 100,
          referrals: 0,
          role: 'both', // Allow both passenger and driver for fallback
          phoneNumber: userData.phone_number || '',
          city: '',
          country: 'Tunisia',
          bio: '',
          isVerified: false,
          vehicles: [],
        };

        setUserState(fallbackUser);
        localStorage.setItem('user', JSON.stringify(fallbackUser));
        localStorage.setItem('isAuthenticated', 'true');

        toast.success('Registration successful (fallback mode)!');
        return true;
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Set user function
  const setUser = useCallback((newUser: User | null) => {
    if (newUser) {
      console.log("Setting user state:", newUser);
      setUserState(newUser);

      // Persist user to localStorage
      localStorage.setItem('user', JSON.stringify(newUser));
      localStorage.setItem('isAuthenticated', 'true');
    } else {
      setUserState(null);

      // Clear localStorage
      localStorage.removeItem('user');
      localStorage.removeItem('isAuthenticated');
    }
  }, []);

  // Add loyalty points function
  const addLoyaltyPoints = useCallback(async (points: number) => {
    if (!user || !user.id) return;

    try {
      console.log('Adding loyalty points via Django API...');
      await apiClient.post('/auth/add-loyalty-points/', { points });

      // Update local state
      const newPoints = (user.loyaltyPoints || 0) + points;
      setUserState(prev => prev ? {
        ...prev,
        loyaltyPoints: newPoints
      } : null);

      toast.success(`${points} loyalty points added!`);
    } catch (error) {
      console.error("Failed to update loyalty points:", error);
      toast.error("Failed to update loyalty points");
    }
  }, [user]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      console.log('Logging out via Django API...');
      await apiClient.post('/auth/logout/');
    } catch (error) {
      console.error("Error during logout:", error);
    } finally {
      // Always clear the user state
      setUserState(null);

      // Clear localStorage
      localStorage.removeItem('user');
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('pendingSearch');

      toast.success('Logged out successfully');
    }
  }, []);

  return {
    user,
    setUser,
    isAuthenticated,
    addLoyaltyPoints,
    logout,
    login,
    register,
    isLoading
  };
}
