# Generated by Django 4.2.7 on 2025-05-27 17:17

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('full_name', models.CharField(blank=True, max_length=255)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('bio', models.TextField(blank=True, max_length=500)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('city', models.CharField(blank=True, max_length=100)),
                ('country', models.CharField(default='Tunisia', max_length=100)),
                ('rides_offered', models.PositiveIntegerField(default=0)),
                ('rides_completed', models.PositiveIntegerField(default=0)),
                ('loyalty_points', models.PositiveIntegerField(default=100)),
                ('referrals', models.PositiveIntegerField(default=0)),
                ('rating', models.DecimalField(decimal_places=2, default=5.0, max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(5)])),
                ('total_ratings', models.PositiveIntegerField(default=0)),
                ('is_verified', models.BooleanField(default=False)),
                ('email_notifications', models.BooleanField(default=True)),
                ('sms_notifications', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'users',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_language', models.CharField(choices=[('en', 'English'), ('fr', 'French'), ('ar', 'Arabic')], default='en', max_length=10)),
                ('preferred_currency', models.CharField(default='TND', max_length=3)),
                ('is_driver', models.BooleanField(default=False)),
                ('driver_license_number', models.CharField(blank=True, max_length=50)),
                ('driver_license_expiry', models.DateField(blank=True, null=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=255)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20)),
                ('facebook_profile', models.URLField(blank=True)),
                ('linkedin_profile', models.URLField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_profiles',
            },
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('make', models.CharField(max_length=50)),
                ('model', models.CharField(max_length=50)),
                ('year', models.PositiveIntegerField()),
                ('color', models.CharField(max_length=30)),
                ('license_plate', models.CharField(max_length=20, unique=True)),
                ('vehicle_type', models.CharField(choices=[('sedan', 'Sedan'), ('hatchback', 'Hatchback'), ('suv', 'SUV'), ('coupe', 'Coupe'), ('convertible', 'Convertible'), ('wagon', 'Wagon'), ('van', 'Van'), ('pickup', 'Pickup Truck')], default='sedan', max_length=20)),
                ('seats', models.PositiveIntegerField(default=4, validators=[django.core.validators.MinValueValidator(2), django.core.validators.MaxValueValidator(8)])),
                ('has_ac', models.BooleanField(default=True)),
                ('has_music', models.BooleanField(default=True)),
                ('is_smoking_allowed', models.BooleanField(default=False)),
                ('has_wifi', models.BooleanField(default=False)),
                ('insurance_expiry', models.DateField(blank=True, null=True)),
                ('registration_expiry', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'vehicles',
                'unique_together': {('owner', 'license_plate')},
            },
        ),
    ]
