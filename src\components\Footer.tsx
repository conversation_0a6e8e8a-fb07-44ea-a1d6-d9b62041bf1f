
import { Link } from "react-router-dom";
import { Car, Facebook, Twitter, Instagram } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const Footer = () => {
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  
  return (
    <footer className="bg-gray-50 border-t border-gray-100 mt-12 dark:bg-gray-900 dark:border-gray-800">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Car className="h-6 w-6 text-primary" />
              <span className="font-bold text-xl dark:text-white">CoSesameHub</span>
            </div>
            <p className="text-gray-500 mb-4 dark:text-gray-400">
              {translations.footerTagline || "Connect with trusted drivers from Sesame University for affordable ridesharing."}
            </p>
            <div className="flex gap-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-primary">
                <Facebook size={20} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-primary">
                <Twitter size={20} />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-primary">
                <Instagram size={20} />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4 dark:text-white">{translations.company || "Company"}</h3>
            <ul className="space-y-3">
              <li><Link to="/about" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.aboutUsTitle || "About Us"}</Link></li>
              <li><Link to="/how-it-works" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.howItWorks || "How It Works"}</Link></li>
              <li><Link to="/team" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.teamTitle || "Team"}</Link></li>
              <li><Link to="/careers" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.careersTitle || "Careers"}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4 dark:text-white">{translations.resources || "Resources"}</h3>
            <ul className="space-y-3">
              <li><Link to="/help" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.helpCenterTitle || "Help Center"}</Link></li>
              <li><Link to="/safety" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.safetyTitle || "Safety"}</Link></li>
              <li><Link to="/driver-requirements" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.driverReqTitle || "Driver Requirements"}</Link></li>
              <li><Link to="/community-rules" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.communityRulesTitle || "Community Rules"}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4 dark:text-white">{translations.legal || "Legal"}</h3>
            <ul className="space-y-3">
              <li><Link to="/terms" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.termsTitle || "Terms"}</Link></li>
              <li><Link to="/privacy" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.privacyTitle || "Privacy"}</Link></li>
              <li><Link to="/cookies" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.cookieTitle || "Cookies"}</Link></li>
              <li><Link to="/trust" className="text-gray-500 hover:text-primary dark:text-gray-400">{translations.trustTitle || "Trust & Safety"}</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-200 dark:border-gray-800 mt-8 pt-8 text-center text-gray-500 dark:text-gray-400">
          <p>{translations.copyrightText || `© ${new Date().getFullYear()} CoSesameHub. ${translations.allRightsReserved || "All rights reserved."}`}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
