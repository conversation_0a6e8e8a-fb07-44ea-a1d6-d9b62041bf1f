from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()


class Review(models.Model):
    """Model for user reviews"""
    
    ride = models.ForeignKey(
        'rides.Ride', 
        on_delete=models.CASCADE, 
        related_name='reviews'
    )
    reviewer = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='reviews_given'
    )
    reviewed = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='reviews_received'
    )
    
    # Review content
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    comment = models.TextField(blank=True)
    
    # Review categories
    punctuality_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )
    communication_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )
    cleanliness_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )
    safety_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )
    
    # Review type (driver or passenger)
    is_driver_review = models.BooleanField(default=False)  # True if reviewing the driver
    
    # Status
    is_public = models.BooleanField(default=True)
    is_flagged = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'reviews'
        unique_together = ['ride', 'reviewer', 'reviewed']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['reviewed', '-created_at']),
            models.Index(fields=['reviewer']),
            models.Index(fields=['rating']),
            models.Index(fields=['is_public']),
        ]
    
    def __str__(self):
        return f"{self.reviewer.full_name} → {self.reviewed.full_name} ({self.rating}★)"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # Update the reviewed user's rating
        self.update_user_rating()
    
    def update_user_rating(self):
        """Update the reviewed user's average rating"""
        reviews = Review.objects.filter(
            reviewed=self.reviewed,
            is_public=True
        )
        
        if reviews.exists():
            avg_rating = reviews.aggregate(
                models.Avg('rating')
            )['rating__avg']
            
            self.reviewed.rating = round(avg_rating, 2)
            self.reviewed.total_ratings = reviews.count()
            self.reviewed.save(update_fields=['rating', 'total_ratings'])


class ReviewResponse(models.Model):
    """Model for responses to reviews"""
    
    review = models.OneToOneField(
        Review, 
        on_delete=models.CASCADE, 
        related_name='response'
    )
    responder = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='review_responses'
    )
    content = models.TextField()
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'review_responses'
    
    def __str__(self):
        return f"Response to review {self.review.id}"


class ReviewFlag(models.Model):
    """Model for flagged reviews"""
    
    REASON_CHOICES = [
        ('inappropriate', 'Inappropriate Content'),
        ('spam', 'Spam'),
        ('fake', 'Fake Review'),
        ('harassment', 'Harassment'),
        ('other', 'Other'),
    ]
    
    review = models.ForeignKey(
        Review, 
        on_delete=models.CASCADE, 
        related_name='flags'
    )
    flagger = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='review_flags'
    )
    reason = models.CharField(max_length=20, choices=REASON_CHOICES)
    description = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'review_flags'
        unique_together = ['review', 'flagger']
    
    def __str__(self):
        return f"Flag on review {self.review.id} by {self.flagger.full_name}"
