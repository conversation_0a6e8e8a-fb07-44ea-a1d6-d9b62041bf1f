
import { Message, Notification, Ride } from "@/data/rides";
import { User as SupabaseUser } from "@supabase/supabase-js";

export type User = {
  id?: string;
  fullName?: string;
  name?: string; // Display name
  email: string;
  loyaltyPoints: number;
  ridesOffered: number;
  ridesCompleted: number;
  referrals: number;
  joinDate: Date | string;
  bookedRides?: string[]; // Array of ride IDs booked by user
  messages?: Message[]; // Messages sent to or by this user
  notifications?: Notification[]; // Notifications for this user
  profilePicture?: string; // User profile picture
  avatar?: string; // Avatar URL
  city?: string; // User's city
  country?: string; // User's country
  dateOfBirth?: string; // User's date of birth
  bio?: string; // User's bio
  phoneNumber?: string; // User's phone number
  role?: 'passenger' | 'driver' | 'both' | 'admin'; // User role
  rating?: number; // User rating
  rideCount?: number; // Total ride count
  isVerified?: boolean; // Account verification status
  vehicles?: Vehicle[]; // User's vehicles
};

export type Vehicle = {
  id: string;
  make: string;
  model: string;
  year: number;
  color: string;
  licensePlate: string;
  vehicleType: string;
  seats: number;
  hasAC: boolean;
  hasMusic: boolean;
  isSmokingAllowed: boolean;
  hasWifi: boolean;
  isActive: boolean;
};

export type PendingRideOffer = {
  origin: string;
  destination: string;
  departureDate: string;
  departureTime: string;
  availableSeats: string;
  price: string;
  carModel: string;
  carColor: string;
};

export type PendingRideRequest = {
  origin: string;
  destination: string;
  date: string;
  passengers: number;
};
