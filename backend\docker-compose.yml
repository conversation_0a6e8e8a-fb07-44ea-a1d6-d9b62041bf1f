version: '3.8'

services:
  db:
    image: postgis/postgis:13-3.1
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=cojourneyhub
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=True
      - DATABASE_URL=**************************************/cojourneyhub
      - REDIS_URL=redis://redis:6379

  celery:
    build: .
    command: celery -A cojourneyhub worker -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=True
      - DATABASE_URL=**************************************/cojourneyhub
      - REDIS_URL=redis://redis:6379

  celery-beat:
    build: .
    command: celery -A cojourneyhub beat -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=True
      - DATABASE_URL=**************************************/cojourneyhub
      - REDIS_URL=redis://redis:6379

volumes:
  postgres_data:
