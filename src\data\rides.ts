
export interface Message {
  id: string;
  senderId: string;
  recipientId: string;
  content: string;
  timestamp: string;
  read: boolean;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  type: 'ride_booked' | 'ride_canceled' | 'new_message' | 'system';
  relatedId?: string; // Could be rideId, messageId, etc.
}

export interface Driver {
  id?: string;
  name: string;
  avatar: string;
  rating: number;
  rideCount: number;
  joinDate?: string;
  ridesCompleted?: number;
  ridesOffered?: number;
}

export interface Ride {
  id: string;
  origin: string;
  destination: string;
  departureTime: string;
  arrivalTime: string;
  price: number;
  distance: string;
  seatsAvailable: number;
  carModel: string;
  carColor: string;
  driver: Driver;
  features: string[];
  passengers?: string[]; // Array of userIds who booked this ride
  driverId?: string; // The driver's userId
}

// Starting with an empty array as we're removing the fake rides
export const rides: Ride[] = [];

// Store messages between users
export const messages: Message[] = [];

// Store notifications
export const notifications: Notification[] = [];
