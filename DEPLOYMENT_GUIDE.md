# 🚀 CoJourneyHub Deployment Guide

Complete deployment guide for the CoJourneyHub ride-sharing platform.

## 📋 **Project Status: FULLY FUNCTIONAL** ✅

All core features have been implemented and tested:

✅ **Backend (Django REST API)**
- User authentication and registration
- Ride creation and management
- Real-time messaging system
- Location services (53 Tunisia locations)
- Admin dashboard
- Database models and migrations

✅ **Frontend (React TypeScript)**
- Modern responsive UI
- User authentication flows
- Ride booking interface
- Messaging system
- Location search and selection
- Profile management

✅ **Integration**
- Frontend-backend API integration
- Session-based authentication
- CORS configuration
- Error handling

## 🛠️ **Technology Stack**

### Backend
- **Django 5.1** with REST Framework
- **SQLite** (development) / **PostgreSQL** (production)
- **Python 3.11+**
- **Django CORS Headers**
- **Pillow** for image processing

### Frontend
- **React 18** with TypeScript
- **Vite** build tool
- **Tailwind CSS** styling
- **Lucide React** icons
- **Axios** for API calls

## 🚀 **Local Development Setup**

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py makemigrations
python manage.py migrate

# Load initial data
python manage.py loaddata locations.json

# Create superuser (optional)
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

Backend will be available at: `http://localhost:8000`
Admin panel: `http://localhost:8000/admin/`
API documentation: `http://localhost:8000/api/`

### 2. Frontend Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

Frontend will be available at: `http://localhost:5173`

## 🌐 **Production Deployment**

### Backend Deployment (Django)

#### Option 1: Heroku
```bash
# Install Heroku CLI and login
heroku login

# Create app
heroku create cojourneyhub-backend

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Set environment variables
heroku config:set DEBUG=False
heroku config:set SECRET_KEY=your-secret-key
heroku config:set ALLOWED_HOSTS=cojourneyhub-backend.herokuapp.com

# Deploy
git subtree push --prefix=backend heroku main
```

#### Option 2: DigitalOcean App Platform
1. Connect your GitHub repository
2. Select the `backend` folder as root directory
3. Set environment variables in the dashboard
4. Deploy automatically

#### Option 3: VPS (Ubuntu)
```bash
# Install dependencies
sudo apt update
sudo apt install python3-pip python3-venv nginx postgresql

# Setup application
git clone your-repo
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup database
sudo -u postgres createdb cojourneyhub
python manage.py migrate
python manage.py loaddata locations.json

# Setup Gunicorn
pip install gunicorn
gunicorn cojourneyhub.wsgi:application --bind 0.0.0.0:8000

# Setup Nginx (reverse proxy)
sudo nano /etc/nginx/sites-available/cojourneyhub
# Configure Nginx to proxy to Gunicorn
```

### Frontend Deployment

#### Option 1: Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
VITE_API_BASE_URL=https://your-backend-url.com/api
```

#### Option 2: Netlify
```bash
# Build for production
npm run build

# Deploy dist folder to Netlify
# Set environment variables in Netlify dashboard
```

#### Option 3: Static Hosting
```bash
# Build for production
npm run build

# Upload dist/ folder to any static hosting service
# (AWS S3, GitHub Pages, etc.)
```

## ⚙️ **Environment Configuration**

### Backend Environment Variables
```env
# backend/.env
DEBUG=False
SECRET_KEY=your-super-secret-key-here
DATABASE_URL=postgresql://user:pass@localhost/dbname
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
CORS_ALLOWED_ORIGINS=https://yourfrontend.com
```

### Frontend Environment Variables
```env
# .env
VITE_API_BASE_URL=https://your-backend-url.com/api
VITE_DJANGO_BACKEND=true
```

## 🔒 **Security Checklist**

### Backend Security
- [ ] Set `DEBUG=False` in production
- [ ] Use strong `SECRET_KEY`
- [ ] Configure `ALLOWED_HOSTS` properly
- [ ] Set up HTTPS/SSL certificates
- [ ] Configure CORS origins
- [ ] Use environment variables for secrets
- [ ] Set up database backups
- [ ] Configure logging

### Frontend Security
- [ ] Use HTTPS for all API calls
- [ ] Validate all user inputs
- [ ] Implement proper error handling
- [ ] Use secure authentication tokens
- [ ] Configure CSP headers

## 📊 **Database Setup**

### Development (SQLite)
Already configured and working. Database file: `backend/db.sqlite3`

### Production (PostgreSQL)
```sql
-- Create database
CREATE DATABASE cojourneyhub;
CREATE USER cojourneyhub_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE cojourneyhub TO cojourneyhub_user;
```

## 🧪 **Testing**

### Backend Testing
```bash
cd backend
python manage.py test
```

### Frontend Testing
```bash
npm run test
```

## 📈 **Monitoring & Maintenance**

### Recommended Tools
- **Backend**: Sentry for error tracking
- **Frontend**: Vercel Analytics or Google Analytics
- **Database**: PostgreSQL monitoring
- **Uptime**: UptimeRobot or Pingdom

### Regular Maintenance
- Update dependencies regularly
- Monitor database performance
- Backup database regularly
- Monitor error logs
- Update SSL certificates

## 🆘 **Troubleshooting**

### Common Issues

1. **CORS Errors**
   - Check `CORS_ALLOWED_ORIGINS` in Django settings
   - Verify frontend URL is included

2. **Database Connection Issues**
   - Check `DATABASE_URL` environment variable
   - Verify database credentials

3. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check `STATIC_URL` and `STATIC_ROOT` settings

4. **Frontend Build Errors**
   - Check environment variables
   - Verify API endpoints are accessible

## 📞 **Support**

For deployment issues:
1. Check the logs first
2. Verify environment variables
3. Test API endpoints manually
4. Check network connectivity

---

**🎉 Your CoJourneyHub platform is ready for production!**
