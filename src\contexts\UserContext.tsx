
import React, { createContext, useContext, useMemo } from "react";
import { User } from "@/types/user";
import { useAuth } from "@/hooks/use-auth";
import { useRides } from "@/hooks/use-rides";
import { useMessages } from "@/hooks/use-messages";
import { useNotifications } from "@/hooks/use-notifications";
import { rideService } from "@/services/rideService";
import { Ride, Message, Notification } from "@/data/rides";

import { PendingRideOffer, PendingRideRequest } from "@/types/user";

type UserContextType = {
  user: User | null;
  setUser: (user: User | null) => void;
  isAuthenticated: boolean;
  addLoyaltyPoints: (points: number) => void;
  logout: () => void;
  pendingRideOffer: PendingRideOffer | null;
  setPendingRideOffer: (offer: PendingRideOffer | null) => void;
  pendingRideRequest: PendingRideRequest | null;
  setPendingRideRequest: (request: PendingRideRequest | null) => void;
  clearPendingRides: () => void;
  bookRide: (rideId: string) => Promise<void>;
  cancelRideBooking: (rideId: string) => Promise<void>;
  getBookedRides: () => Promise<Ride[]>;
  getUserRides: (type: 'offered' | 'booked') => Promise<Ride[]>;
  sendMessage: (recipientId: string, content: string) => Promise<Message | undefined>;
  getMessages: (userId: string) => Promise<Message[]>;
  markMessageAsRead: (messageId: string) => Promise<void>;
  getNotifications: () => Promise<Notification[]>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  hasUnreadNotifications: () => Promise<boolean>;
  hasUnreadMessages: () => Promise<boolean>;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => Promise<void>;
};

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const {
    user,
    setUser,
    isAuthenticated,
    addLoyaltyPoints,
    logout,
    isLoading
  } = useAuth();

  const {
    pendingRideOffer,
    setPendingRideOffer,
    pendingRideRequest,
    setPendingRideRequest,
    clearPendingRides,
    bookRide,
    cancelRideBooking,
    getBookedRides
  } = useRides(user?.id);

  const {
    sendMessage,
    getMessages,
    markMessageAsRead,
    hasUnreadMessages
  } = useMessages(user?.id);

  const {
    addNotification,
    getNotifications: getUserNotifications,
    markNotificationAsRead,
    hasUnreadNotifications: checkUnreadNotifications
  } = useNotifications();

  // Wrapper functions that use the current user ID
  const getNotifications = async () => {
    return user?.id ? getUserNotifications(user.id) : [];
  };

  const hasUnreadNotifications = async () => {
    return user?.id ? checkUnreadNotifications(user.id) : false;
  };

  const getUserRides = async (type: 'offered' | 'booked') => {
    if (!user?.id) return [];
    return rideService.getUserRides(user.id, type);
  };

  const contextValue = useMemo(() => ({
    user,
    setUser,
    isAuthenticated,
    addLoyaltyPoints,
    logout,
    pendingRideOffer,
    setPendingRideOffer,
    pendingRideRequest,
    setPendingRideRequest,
    clearPendingRides,
    bookRide,
    cancelRideBooking,
    getBookedRides,
    getUserRides,
    sendMessage,
    getMessages,
    markMessageAsRead,
    getNotifications,
    markNotificationAsRead,
    hasUnreadNotifications,
    hasUnreadMessages,
    addNotification
  }), [
    user, setUser, isAuthenticated, addLoyaltyPoints, logout,
    pendingRideOffer, setPendingRideOffer, pendingRideRequest, setPendingRideRequest,
    clearPendingRides, bookRide, cancelRideBooking, getBookedRides, sendMessage,
    getMessages, markMessageAsRead, getUserNotifications, markNotificationAsRead,
    checkUnreadNotifications, hasUnreadMessages, addNotification
  ]);

  if (isLoading) {
    return null;
  }

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
