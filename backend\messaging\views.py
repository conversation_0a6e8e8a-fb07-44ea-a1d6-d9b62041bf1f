from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Prefetch
from django.contrib.auth import get_user_model
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import Conversation, Message, BlockedUser
from .serializers import (
    ConversationSerializer, ConversationCreateSerializer,
    MessageSerializer, MessageCreateSerializer,
    BlockedUserSerializer, BlockUserSerializer
)
from rides.models import Ride

User = get_user_model()


@method_decorator(csrf_exempt, name='dispatch')
class ConversationListCreateView(generics.ListCreateAPIView):
    """List and create conversations"""

    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # For unauthenticated users, get conversations for the first user
        if self.request.user.is_authenticated:
            user = self.request.user
        else:
            user = User.objects.first()
            if not user:
                return Conversation.objects.none()

        # Ensure we have a valid User instance, not Anonymous<PERSON>ser
        if hasattr(user, 'is_anonymous') and user.is_anonymous:
            user = User.objects.first()
            if not user:
                return Conversation.objects.none()

        return Conversation.objects.filter(
            participants=user
        ).prefetch_related(
            'participants',
            'messages'
        ).order_by('-updated_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ConversationCreateSerializer
        return ConversationSerializer

    def create(self, request, *args, **kwargs):
        """Override create to return full conversation data"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        from django.contrib.auth import get_user_model
        User = get_user_model()

        # If user is authenticated, use them
        # Otherwise, use the first user (for testing)
        if request.user.is_authenticated:
            current_user = request.user
        else:
            current_user = User.objects.first()
            if not current_user:
                # Create a default user for testing
                current_user = User.objects.create_user(
                    username='default_user',
                    email='<EMAIL>',
                    full_name='Default User',
                    password='testpass123'
                )

        conversation = serializer.save()
        conversation.participants.add(current_user)

        # Add the other participant if specified
        participant_id = request.data.get('participant_id')
        if participant_id:
            try:
                other_user = User.objects.get(id=participant_id)
                conversation.participants.add(other_user)
            except User.DoesNotExist:
                pass

        # Return full conversation data
        from rest_framework.response import Response
        from rest_framework import status
        response_serializer = ConversationSerializer(conversation, context={'request': request})
        headers = self.get_success_headers(response_serializer.data)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class ConversationDetailView(generics.RetrieveAPIView):
    """Get conversation details"""

    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Conversation.objects.filter(
            participants=self.request.user
        ).prefetch_related('participants')


@method_decorator(csrf_exempt, name='dispatch')
class MessageListCreateView(generics.ListCreateAPIView):
    """List and create messages in a conversation"""

    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        from django.contrib.auth import get_user_model
        User = get_user_model()

        conversation_id = self.kwargs['conversation_id']

        # Get user (authenticated or first user for testing)
        if self.request.user.is_authenticated:
            user = self.request.user
        else:
            user = User.objects.first()
            if not user:
                return Message.objects.none()

        # Ensure we have a valid User instance, not AnonymousUser
        if hasattr(user, 'is_anonymous') and user.is_anonymous:
            user = User.objects.first()
            if not user:
                return Message.objects.none()

        # Verify user is participant in conversation
        try:
            conversation = Conversation.objects.get(
                id=conversation_id,
                participants=user
            )
        except Conversation.DoesNotExist:
            return Message.objects.none()

        # Mark messages as read (only for authenticated users)
        if self.request.user.is_authenticated:
            unread_messages = conversation.messages.filter(
                is_read=False
            ).exclude(sender=user)

            for message in unread_messages:
                message.mark_as_read(user)

        return conversation.messages.select_related('sender').order_by('-created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return MessageCreateSerializer
        return MessageSerializer

    def create(self, request, *args, **kwargs):
        """Override create to handle conversation assignment"""
        conversation_id = self.kwargs['conversation_id']

        # Get current user (authenticated or default)
        if request.user.is_authenticated:
            current_user = request.user
        else:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            current_user = User.objects.first()

        # Get conversation
        try:
            conversation = Conversation.objects.get(id=conversation_id)
        except Conversation.DoesNotExist:
            from rest_framework.response import Response
            from rest_framework import status
            return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)

        # For authenticated users, verify participation
        if request.user.is_authenticated:
            if not conversation.participants.filter(id=current_user.id).exists():
                from rest_framework.response import Response
                from rest_framework import status
                return Response({'error': 'You are not a participant in this conversation'}, status=status.HTTP_403_FORBIDDEN)

        # Get serializer and validate
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Create message with conversation and sender
        message = serializer.save(conversation=conversation, sender=current_user)

        # Update conversation timestamp
        conversation.save(update_fields=['updated_at'])

        # Return the created message
        response_serializer = MessageSerializer(message, context={'request': request})
        headers = self.get_success_headers(response_serializer.data)
        from rest_framework.response import Response
        from rest_framework import status
        return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)


@api_view(['POST'])
def start_conversation(request, user_id):
    """Start a conversation with a specific user"""

    # Get current user (authenticated or default for testing)
    if request.user.is_authenticated:
        current_user = request.user
    else:
        current_user = User.objects.first()
        if not current_user:
            return Response({'error': 'No users available'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        other_user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    if other_user == current_user:
        return Response({'error': 'Cannot start conversation with yourself'}, status=status.HTTP_400_BAD_REQUEST)

    # Check if conversation already exists
    existing_conversation = Conversation.objects.filter(
        participants=current_user
    ).filter(
        participants=other_user
    ).first()

    if existing_conversation:
        serializer = ConversationSerializer(existing_conversation, context={'request': request})
        return Response(serializer.data)

    # Create new conversation
    conversation = Conversation.objects.create()
    conversation.participants.add(current_user, other_user)

    serializer = ConversationSerializer(conversation, context={'request': request})
    return Response(serializer.data, status=status.HTTP_201_CREATED)


@api_view(['POST'])
def start_ride_conversation(request, ride_id):
    """Start a conversation related to a specific ride"""

    # Get current user (authenticated or default for testing)
    if request.user.is_authenticated:
        current_user = request.user
    else:
        current_user = User.objects.first()
        if not current_user:
            return Response({'error': 'No users available'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        ride = Ride.objects.get(id=ride_id)
    except Ride.DoesNotExist:
        return Response({'error': 'Ride not found'}, status=status.HTTP_404_NOT_FOUND)

    # Determine the other participant
    # If current user is the driver, they can chat with any passenger
    # If current user is a passenger, they chat with the driver
    if current_user == ride.driver:
        # Driver wants to chat - need to specify passenger
        passenger_id = request.data.get('passenger_id')
        if not passenger_id:
            return Response({'error': 'Passenger ID required for driver'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            other_user = User.objects.get(id=passenger_id)
            # Verify this user has booked the ride
            if not ride.bookings.filter(user=other_user, status='confirmed').exists():
                return Response({'error': 'User has not booked this ride'}, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            return Response({'error': 'Passenger not found'}, status=status.HTTP_404_NOT_FOUND)
    else:
        # Passenger wants to chat with driver
        other_user = ride.driver
        # Verify current user has booked this ride
        if request.user.is_authenticated and not ride.bookings.filter(user=current_user, status='confirmed').exists():
            return Response({'error': 'You have not booked this ride'}, status=status.HTTP_400_BAD_REQUEST)

    # Check if ride-specific conversation already exists
    existing_conversation = Conversation.objects.filter(
        participants=current_user,
        related_ride=ride
    ).filter(
        participants=other_user
    ).first()

    if existing_conversation:
        serializer = ConversationSerializer(existing_conversation, context={'request': request})
        return Response(serializer.data)

    # Create new ride-specific conversation
    conversation = Conversation.objects.create(related_ride=ride)
    conversation.participants.add(current_user, other_user)

    # Send a system message to start the conversation
    Message.objects.create(
        conversation=conversation,
        sender=current_user,
        content=f"Started conversation about ride: {ride.origin} → {ride.destination}",
        message_type='system'
    )

    serializer = ConversationSerializer(conversation, context={'request': request})
    return Response(serializer.data, status=status.HTTP_201_CREATED)


@api_view(['POST'])
def mark_conversation_read(request, conversation_id):
    """Mark all messages in a conversation as read"""

    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)

    # Mark all unread messages as read
    unread_messages = conversation.messages.filter(
        is_read=False
    ).exclude(sender=request.user)

    for message in unread_messages:
        message.mark_as_read(request.user)

    return Response({'message': 'Conversation marked as read'})


@api_view(['GET'])
def unread_count(request):
    """Get total unread message count for user"""

    unread_count = Message.objects.filter(
        conversation__participants=request.user,
        is_read=False
    ).exclude(sender=request.user).count()

    return Response({'unread_count': unread_count})


class BlockedUserListView(generics.ListAPIView):
    """List blocked users"""

    serializer_class = BlockedUserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return BlockedUser.objects.filter(blocker=self.request.user)


@api_view(['POST'])
def block_user(request):
    """Block a user"""

    serializer = BlockUserSerializer(data=request.data, context={'request': request})
    serializer.is_valid(raise_exception=True)

    user_id = serializer.validated_data['user_id']
    reason = serializer.validated_data.get('reason', '')

    try:
        user_to_block = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Check if already blocked
    if BlockedUser.objects.filter(blocker=request.user, blocked=user_to_block).exists():
        return Response({'error': 'User already blocked'}, status=status.HTTP_400_BAD_REQUEST)

    # Create block record
    BlockedUser.objects.create(
        blocker=request.user,
        blocked=user_to_block,
        reason=reason
    )

    return Response({'message': 'User blocked successfully'})


@api_view(['POST'])
def unblock_user(request, user_id):
    """Unblock a user"""

    try:
        blocked_user = BlockedUser.objects.get(
            blocker=request.user,
            blocked_id=user_id
        )
        blocked_user.delete()
        return Response({'message': 'User unblocked successfully'})
    except BlockedUser.DoesNotExist:
        return Response({'error': 'User not blocked'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['DELETE'])
def delete_conversation(request, conversation_id):
    """Delete a conversation (for current user only)"""

    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )

        # Remove user from conversation
        conversation.participants.remove(request.user)

        # If no participants left, delete the conversation
        if conversation.participants.count() == 0:
            conversation.delete()

        return Response({'message': 'Conversation deleted'})
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
