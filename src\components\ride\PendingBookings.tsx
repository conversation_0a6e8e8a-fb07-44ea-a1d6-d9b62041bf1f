import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Clock, User, MapPin, MessageSquare } from "lucide-react";
import { toast } from "sonner";

interface PendingBooking {
  id: string;
  ride: {
    id: string;
    origin: string;
    destination: string;
    departure_time: string;
    price: number;
  };
  passenger: {
    id: string;
    full_name: string;
    phone_number: string;
    rating: number;
  };
  seats_requested: number;
  pickup_location: string;
  dropoff_location: string;
  passenger_name: string;
  passenger_phone: string;
  special_requests: string;
  status: string;
  created_at: string;
}

interface PendingBookingsProps {
  userRole: 'driver' | 'passenger';
}

const PendingBookings: React.FC<PendingBookingsProps> = ({ userRole }) => {
  const [pendingBookings, setPendingBookings] = useState<PendingBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [responseText, setResponseText] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchPendingBookings();
  }, []);

  const fetchPendingBookings = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/rides/pending/', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setPendingBookings(data);
      }
    } catch (error) {
      console.error('Error fetching pending bookings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResponse = async (pendingId: string, action: 'confirm' | 'reject') => {
    try {
      const response = await fetch(`http://localhost:8000/api/rides/pending/${pendingId}/respond/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action,
          driver_response: responseText[pendingId] || '',
        }),
      });

      if (response.ok) {
        toast.success(`Booking ${action}ed successfully!`);
        fetchPendingBookings(); // Refresh the list
        setResponseText(prev => ({ ...prev, [pendingId]: '' }));
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || `Failed to ${action} booking`);
      }
    } catch (error) {
      toast.error(`Error ${action}ing booking`);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (pendingBookings.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Pending Bookings
          </h3>
          <p className="text-gray-500">
            {userRole === 'driver' 
              ? "You don't have any pending booking requests at the moment."
              : "You don't have any pending booking requests at the moment."
            }
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">
        {userRole === 'driver' ? 'Booking Requests' : 'My Pending Bookings'}
      </h2>
      
      {pendingBookings.map((booking) => (
        <Card key={booking.id}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">
                  {booking.ride.origin} → {booking.ride.destination}
                </CardTitle>
                <CardDescription>
                  {formatDate(booking.ride.departure_time)} • ${booking.ride.price}
                </CardDescription>
              </div>
              <Badge variant="outline">
                {booking.status}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{booking.passenger_name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Phone:</span>
                  <span className="text-sm">{booking.passenger_phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Seats:</span>
                  <span className="text-sm">{booking.seats_requested}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                {booking.pickup_location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Pickup: {booking.pickup_location}</span>
                  </div>
                )}
                {booking.dropoff_location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Dropoff: {booking.dropoff_location}</span>
                  </div>
                )}
              </div>
            </div>

            {booking.special_requests && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Special Requests:</span>
                </div>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                  {booking.special_requests}
                </p>
              </div>
            )}

            {userRole === 'driver' && booking.status === 'pending' && (
              <div className="space-y-3 border-t pt-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Response Message (Optional)</label>
                  <Textarea
                    placeholder="Add a message for the passenger..."
                    value={responseText[booking.id] || ''}
                    onChange={(e) => setResponseText(prev => ({ 
                      ...prev, 
                      [booking.id]: e.target.value 
                    }))}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleResponse(booking.id, 'confirm')}
                    className="flex-1"
                  >
                    Accept Booking
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleResponse(booking.id, 'reject')}
                    className="flex-1"
                  >
                    Decline
                  </Button>
                </div>
              </div>
            )}

            <div className="text-xs text-gray-500">
              Requested {formatDate(booking.created_at)}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default PendingBookings;
