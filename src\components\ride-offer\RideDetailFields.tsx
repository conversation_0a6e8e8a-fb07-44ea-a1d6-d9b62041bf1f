
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Users, CreditCard } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Control } from "react-hook-form";

interface RideDetailFieldsProps {
  control: Control<any>;
  isRTL: boolean;
}

const RideDetailFields = ({ control, isRTL }: RideDetailFieldsProps) => {
  const { translations } = useLanguage();

  return (
    <>
      <FormField
        control={control}
        name="availableSeats"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.availableSeats}</FormLabel>
            <div className="relative">
              <Users className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
              <FormControl>
                <select 
                  className={`${isRTL ? 'pr-10 text-right' : 'pl-10'} w-full h-10 rounded-md border border-input bg-background px-3 py-2 dark:bg-gray-700 dark:border-gray-600`}
                  {...field}
                >
                  {[1, 2, 3, 4, 5, 6].map(num => (
                    <option key={num} value={num}>{num} {num > 1 ? translations.passengers : translations.passengers.slice(0, -1)}</option>
                  ))}
                </select>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name="price"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.pricePerSeat}</FormLabel>
            <div className="relative">
              <CreditCard className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
              <FormControl>
                <Input type="number" min="0" step="0.5" placeholder="e.g. 25" className={isRTL ? 'pr-10 text-right' : 'pl-10'} {...field} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default RideDetailFields;
