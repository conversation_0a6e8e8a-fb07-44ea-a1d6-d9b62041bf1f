import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { rideService } from "@/services/rideService";
import { bookingService } from "@/services/bookingService";
import { toast } from "sonner";
import {
  MapPin,
  Clock,
  Users,
  Car,
  Star,
  CreditCard,
  MessageCircle,
  ArrowLeft,
  Calendar,
  DollarSign
} from "lucide-react";

interface Ride {
  id: string;
  origin: string;
  destination: string;
  departure_time: string;
  arrival_time: string;
  price: number;
  distance: string;
  seats_available: number;
  car_model: string;
  car_color: string;
  driver: {
    id: string;
    full_name: string;
    email: string;
    rating: number;
    total_ratings: number;
  };
  features: string[];
}

const BookRidePage = () => {
  const { rideId } = useParams<{ rideId: string }>();
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const { user, addLoyaltyPoints } = useUser();
  const isRTL = language === "ar";

  const [ride, setRide] = useState<Ride | null>(null);
  const [loading, setLoading] = useState(true);
  const [booking, setBooking] = useState(false);
  const [bookingData, setBookingData] = useState({
    seats_requested: 1,
    pickup_location: "",
    dropoff_location: "",
    passenger_name: user?.full_name || "",
    passenger_phone: user?.phone_number || "",
    special_requests: "",
  });

  useEffect(() => {
    if (rideId) {
      fetchRide();
    }
  }, [rideId]);

  const fetchRide = async () => {
    try {
      setLoading(true);
      const response = await rideService.getRide(rideId!);
      if (response) {
        setRide(response);
      } else {
        console.error("Ride not found for ID:", rideId);
        toast.error("Ride not found");
        navigate("/search");
      }
    } catch (error) {
      console.error("Error fetching ride:", error);
      toast.error("Failed to load ride details");
      navigate("/search");
    } finally {
      setLoading(false);
    }
  };

  const handleBooking = async () => {
    if (!ride || !user) return;

    // Check if user is a passenger
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    if (userData.role !== 'passenger') {
      toast.error("Only passengers can book rides");
      return;
    }

    // Validate booking data
    if (bookingData.seats_requested < 1 || bookingData.seats_requested > ride.seats_available) {
      toast.error("Please select a valid number of seats");
      return;
    }

    if (!bookingData.passenger_name.trim()) {
      toast.error("Please enter your name");
      return;
    }

    if (!bookingData.passenger_phone.trim()) {
      toast.error("Please enter your phone number");
      return;
    }

    try {
      setBooking(true);

      // Create pending booking request
      const pendingBooking = await bookingService.requestBooking(ride.id, {
        seats_requested: bookingData.seats_requested,
        pickup_location: bookingData.pickup_location,
        dropoff_location: bookingData.dropoff_location,
        passenger_name: bookingData.passenger_name,
        passenger_phone: bookingData.passenger_phone,
        special_requests: bookingData.special_requests,
      });

      toast.success("Booking request sent!", {
        description: "Your booking request has been sent to the driver. You will be notified when they respond.",
        duration: 5000,
      });

      // Navigate to pending bookings page
      navigate("/profile?tab=pending");
    } catch (error: any) {
      console.error("Error requesting booking:", error);
      const errorMessage = error.message || "Failed to send booking request";
      toast.error(errorMessage, {
        description: "Please try again or contact support if the problem persists.",
      });
    } finally {
      setBooking(false);
    }
  };

  const handleStartChat = async () => {
    if (!ride) return;

    if (!user) {
      toast.info("Please login to contact the driver");
      navigate("/login?redirect=" + encodeURIComponent(`/rides/${rideId}/book`));
      return;
    }

    try {
      // Navigate to messages page for now
      // In a real implementation, this would create a conversation with the driver
      navigate("/messages");
      toast.success("Redirected to messages. You can start a conversation with the driver there.");
    } catch (error) {
      console.error("Error starting chat:", error);
      toast.error("Failed to start conversation. Please try again.");
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " at " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const totalAmount = ride ? ride.price * bookingData.seats_requested : 0;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!ride) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Ride not found
          </h2>
          <Button onClick={() => navigate("/search")}>
            Back to Search
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Book Ride
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Ride Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Trip Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Trip Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-lg">{ride.origin}</p>
                    <p className="text-sm text-gray-500">Departure</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
                    <p className="text-xs text-gray-500 mt-1">{ride.distance}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-lg">{ride.destination}</p>
                    <p className="text-sm text-gray-500">Arrival</p>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Departure</p>
                      <p className="font-medium">{formatDateTime(ride.departureTime)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Arrival</p>
                      <p className="font-medium">{formatDateTime(ride.arrivalTime)}</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{ride.seatsAvailable} seats available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-semibold">{ride.price} TND per seat</span>
                  </div>
                </div>

                {ride.features.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Features</p>
                    <div className="flex flex-wrap gap-2">
                      {ride.features.map((feature, index) => (
                        <Badge key={index} variant="secondary">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Driver Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Car className="h-5 w-5" />
                  Driver & Vehicle
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-300 font-semibold">
                        {ride.driver.name?.charAt(0) || 'D'}
                      </span>
                    </div>
                    <div>
                      <p className="font-semibold">{ride.driver.name || 'Driver'}</p>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm">
                          {ride.driver.rating} ({ride.driver.rideCount || 0} rides)
                        </span>
                      </div>
                      <p className="text-sm text-gray-500">
                        {ride.carColor} {ride.carModel}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleStartChat}
                    className="flex items-center gap-2"
                  >
                    <MessageCircle className="h-4 w-4" />
                    Chat
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Booking Form */}
          <div>
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Book This Ride</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Number of Seats
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max={ride.seats_available}
                    value={bookingData.seats_requested}
                    onChange={(e) =>
                      setBookingData({
                        ...bookingData,
                        seats_requested: parseInt(e.target.value) || 1,
                      })
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Your Name *
                  </label>
                  <Input
                    placeholder="Full name"
                    value={bookingData.passenger_name}
                    onChange={(e) =>
                      setBookingData({
                        ...bookingData,
                        passenger_name: e.target.value,
                      })
                    }
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Your Phone Number *
                  </label>
                  <Input
                    placeholder="+216 XX XXX XXX"
                    value={bookingData.passenger_phone}
                    onChange={(e) =>
                      setBookingData({
                        ...bookingData,
                        passenger_phone: e.target.value,
                      })
                    }
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Pickup Location (Optional)
                  </label>
                  <Input
                    placeholder="Specific pickup point"
                    value={bookingData.pickup_location}
                    onChange={(e) =>
                      setBookingData({
                        ...bookingData,
                        pickup_location: e.target.value,
                      })
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Dropoff Location (Optional)
                  </label>
                  <Input
                    placeholder="Specific dropoff point"
                    value={bookingData.dropoff_location}
                    onChange={(e) =>
                      setBookingData({
                        ...bookingData,
                        dropoff_location: e.target.value,
                      })
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Special Requests (Optional)
                  </label>
                  <Textarea
                    placeholder="Any special requests or notes"
                    value={bookingData.special_requests}
                    onChange={(e) =>
                      setBookingData({
                        ...bookingData,
                        special_requests: e.target.value,
                      })
                    }
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Price per seat:</span>
                    <span>{ride.price} TND</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Seats:</span>
                    <span>{bookingData.seats_requested}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total:</span>
                    <span>{totalAmount} TND</span>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <CreditCard className="h-4 w-4" />
                  <span>Payment in cash when you meet the driver</span>
                </div>

                <Button
                  onClick={handleBooking}
                  disabled={booking}
                  className="w-full"
                  size="lg"
                >
                  {booking ? "Sending Request..." : `Request Booking for ${totalAmount} TND`}
                </Button>

                <p className="text-xs text-gray-500 text-center">
                  Your request will be sent to the driver for approval
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookRidePage;
