from django.contrib import admin
from .models import TunisiaLocation, Route, LocationSearch


@admin.register(TunisiaLocation)
class TunisiaLocationAdmin(admin.ModelAdmin):
    """Admin configuration for TunisiaLocation model"""
    
    list_display = [
        'name', 'location_type', 'governorate', 'delegation',
        'is_popular', 'created_at'
    ]
    list_filter = [
        'location_type', 'governorate', 'is_popular', 'created_at'
    ]
    search_fields = ['name', 'name_ar', 'name_fr', 'governorate', 'delegation']
    ordering = ['name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'name_fr', 'location_type')
        }),
        ('Location', {
            'fields': ('governorate', 'delegation', 'postal_code')
        }),
        ('Coordinates', {
            'fields': ('latitude', 'longitude') if not hasattr(TunisiaLocation, 'location') else ('location',)
        }),
        ('Additional Info', {
            'fields': ('population', 'is_popular')
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    """Admin configuration for Route model"""
    
    list_display = [
        'origin', 'destination', 'distance_km',
        'estimated_duration_minutes', 'search_count',
        'is_popular', 'created_at'
    ]
    list_filter = ['is_popular', 'created_at']
    search_fields = [
        'origin__name', 'destination__name',
        'origin__governorate', 'destination__governorate'
    ]
    ordering = ['-search_count']
    
    readonly_fields = ['search_count', 'created_at', 'updated_at']


@admin.register(LocationSearch)
class LocationSearchAdmin(admin.ModelAdmin):
    """Admin configuration for LocationSearch model"""
    
    list_display = [
        'query', 'result_count', 'selected_location',
        'user_ip', 'created_at'
    ]
    list_filter = ['result_count', 'created_at']
    search_fields = ['query', 'selected_location__name']
    ordering = ['-created_at']
    
    readonly_fields = ['created_at']
