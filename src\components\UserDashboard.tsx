
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useUser } from '@/contexts/UserContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Car, MapPin, Calendar, Clock, Users, CreditCard } from 'lucide-react';
import { Ride } from '@/data/rides';
import { useLanguage } from '@/contexts/LanguageContext';
import { format } from 'date-fns';

const UserDashboard = () => {
  const { user, getBookedRides, getUserRides } = useUser();
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  const [bookedRides, setBookedRides] = useState<Ride[]>([]);
  const [offeredRides, setOfferedRides] = useState<Ride[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      // Get both booked and offered rides
      const fetchRides = async () => {
        try {
          setLoading(true);
          const [booked, offered] = await Promise.all([
            getUserRides('booked'),
            getUserRides('offered')
          ]);
          setBookedRides(booked);
          setOfferedRides(offered);
        } catch (error) {
          console.error("Error fetching rides:", error);
        } finally {
          setLoading(false);
        }
      };

      fetchRides();
    }
  }, [user, getUserRides]);

  if (!user) return null;

  return (
    <Tabs defaultValue="activity" className="w-full">
      <TabsList className="grid w-full max-w-md mx-auto grid-cols-3 mb-6">
        <TabsTrigger value="activity">{translations.activity || "Activity"}</TabsTrigger>
        <TabsTrigger value="bookedRides">{translations.bookedRides || "Booked Rides"}</TabsTrigger>
        <TabsTrigger value="offeredRides">{translations.offeredRides || "Offered Rides"}</TabsTrigger>
      </TabsList>

      <TabsContent value="activity">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>{translations.stats || "Statistics"}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {translations.ridesOffered || "Rides Offered"}
                  </h3>
                  <p className="text-2xl font-bold mt-2">{user.ridesOffered || 0}</p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {translations.ridesCompleted || "Rides Completed"}
                  </h3>
                  <p className="text-2xl font-bold mt-2">{user.ridesCompleted || 0}</p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {translations.loyaltyPoints || "Loyalty Points"}
                  </h3>
                  <p className="text-2xl font-bold mt-2">{user.loyaltyPoints || 0}</p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {translations.referrals || "Referrals"}
                  </h3>
                  <p className="text-2xl font-bold mt-2">{user.referrals || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{translations.recentActivity || "Recent Activity"}</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : bookedRides.length > 0 || offeredRides.length > 0 ? (
                <div className="space-y-4">
                  {bookedRides.slice(0, 3).map((ride, index) => (
                    <div key={`booked-${index}`} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4" />
                      </div>
                      <div>
                        <h4 className="font-medium">{translations.bookedARide || "Booked a ride"}</h4>
                        <p className="text-sm text-gray-500">
                          {ride.origin} → {ride.destination}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {format(new Date(ride.departureTime), 'PPP')}
                        </p>
                      </div>
                    </div>
                  ))}

                  {offeredRides.slice(0, 3).map((ride, index) => (
                    <div key={`offered-${index}`} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center">
                        <Car className="h-4 w-4" />
                      </div>
                      <div>
                        <h4 className="font-medium">{translations.offeredARide || "Offered a ride"}</h4>
                        <p className="text-sm text-gray-500">
                          {ride.origin} → {ride.destination}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {format(new Date(ride.departureTime), 'PPP')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-gray-500 dark:text-gray-400">
                    {translations.noActivity || "No recent activity"}
                  </p>
                  <Button className="mt-4" asChild>
                    <Link to="/offer-ride">
                      {translations.offerRide || "Offer a Ride"}
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="bookedRides">
        <Card>
          <CardHeader>
            <CardTitle>{translations.bookedRides || "Booked Rides"}</CardTitle>
            <CardDescription>
              {translations.ridesYouBooked || "Rides you've booked as a passenger"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : bookedRides.length > 0 ? (
              <div className="space-y-4">
                {bookedRides.map((ride, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    <div className="flex flex-col sm:flex-row justify-between gap-4">
                      <div className="space-y-2">
                        <h3 className="font-bold flex items-center">
                          <MapPin className={`h-5 w-5 text-primary flex-shrink-0 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          <span>{ride.origin} → {ride.destination}</span>
                        </h3>

                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          <span>{format(new Date(ride.departureTime), 'PPP')}</span>
                        </div>

                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          <span>{format(new Date(ride.departureTime), 'p')}</span>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center mb-2">
                          <Avatar className="h-8 w-8 mr-2">
                            <AvatarImage src={ride.driver.avatar} />
                            <AvatarFallback>{ride.driver.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{ride.driver.name}</span>
                        </div>

                        <div className="flex items-center text-sm text-gray-500">
                          <Car className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          <span>{ride.carModel} · {ride.carColor}</span>
                        </div>

                        <div className="flex items-center text-sm font-medium mt-2">
                          <CreditCard className={`h-4 w-4 text-primary ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          <span>{ride.price} {translations.dnt || "DNT"}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  {translations.noBookedRides || "You haven't booked any rides yet"}
                </p>
                <Button className="mt-4" asChild>
                  <Link to="/">
                    {translations.findRide || "Find a Ride"}
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="offeredRides">
        <Card>
          <CardHeader>
            <CardTitle>{translations.offeredRides || "Offered Rides"}</CardTitle>
            <CardDescription>
              {translations.ridesYouOffered || "Rides you've offered as a driver"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : offeredRides.length > 0 ? (
              <div className="space-y-4">
                {offeredRides.map((ride, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    {/* Similar ride cards as above but for offered rides */}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  {translations.noOfferedRides || "You haven't offered any rides yet"}
                </p>
                <Button className="mt-4" asChild>
                  <Link to="/offer-ride">
                    {translations.offerRide || "Offer a Ride"}
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default UserDashboard;
