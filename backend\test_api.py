#!/usr/bin/env python3
"""
Simple test script to verify the API endpoints are working
"""

import requests
import json
import sys

BASE_URL = 'http://localhost:8000/api'

def test_api_health():
    """Test if the API is responding"""
    try:
        response = requests.get(f'{BASE_URL}/auth/user/', timeout=5)
        print(f"✓ API is responding (Status: {response.status_code})")
        return True
    except requests.exceptions.ConnectionError:
        print("✗ API is not responding. Make sure Django server is running.")
        return False
    except Exception as e:
        print(f"✗ Error connecting to API: {e}")
        return False

def test_driver_registration():
    """Test driver registration endpoint"""
    driver_data = {
        "email": "<EMAIL>",
        "username": "testdriver",
        "full_name": "Test Driver",
        "password": "testpassword123",
        "password_confirm": "testpassword123",
        "phone_number": "+216 12 345 678",
        "city": "Tunis",
        "country": "Tunisia",
        "driver_license_number": "DL123456789",
        "license_expiry_date": "2025-12-31",
        "vehicle_make": "Toyota",
        "vehicle_model": "Corolla",
        "vehicle_year": 2020,
        "vehicle_color": "White",
        "vehicle_license_plate": "123 TUN 456",
        "vehicle_type": "sedan",
        "vehicle_seats": 4
    }
    
    try:
        response = requests.post(
            f'{BASE_URL}/auth/register/driver/',
            json=driver_data,
            timeout=10
        )
        
        if response.status_code == 201:
            print("✓ Driver registration endpoint working")
            return True
        else:
            print(f"✗ Driver registration failed (Status: {response.status_code})")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing driver registration: {e}")
        return False

def test_passenger_registration():
    """Test passenger registration endpoint"""
    passenger_data = {
        "email": "<EMAIL>",
        "username": "testpassenger",
        "full_name": "Test Passenger",
        "password": "testpassword123",
        "password_confirm": "testpassword123",
        "phone_number": "+216 98 765 432",
        "city": "Sfax",
        "country": "Tunisia",
        "id_card_number": "12345678"
    }
    
    try:
        response = requests.post(
            f'{BASE_URL}/auth/register/passenger/',
            json=passenger_data,
            timeout=10
        )
        
        if response.status_code == 201:
            print("✓ Passenger registration endpoint working")
            return True
        else:
            print(f"✗ Passenger registration failed (Status: {response.status_code})")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing passenger registration: {e}")
        return False

def test_rides_endpoint():
    """Test rides listing endpoint"""
    try:
        response = requests.get(f'{BASE_URL}/rides/', timeout=5)
        
        if response.status_code == 200:
            print("✓ Rides endpoint working")
            return True
        else:
            print(f"✗ Rides endpoint failed (Status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"✗ Error testing rides endpoint: {e}")
        return False

def test_notifications_endpoint():
    """Test notifications endpoint"""
    try:
        response = requests.get(f'{BASE_URL}/notifications/', timeout=5)
        
        # This should return 401 (unauthorized) since we're not logged in
        if response.status_code in [200, 401]:
            print("✓ Notifications endpoint working")
            return True
        else:
            print(f"✗ Notifications endpoint failed (Status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"✗ Error testing notifications endpoint: {e}")
        return False

def main():
    """Run all API tests"""
    print("🧪 Testing CoJourneyHub API Endpoints")
    print("=" * 40)
    
    tests = [
        ("API Health Check", test_api_health),
        ("Rides Endpoint", test_rides_endpoint),
        ("Notifications Endpoint", test_notifications_endpoint),
        ("Driver Registration", test_driver_registration),
        ("Passenger Registration", test_passenger_registration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"   ❌ {test_name} failed")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! API is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the Django server and database.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
