
import { useEffect, useState, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import LoyaltyPoints from "@/components/LoyaltyPoints";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CalendarIcon, LogOut, User, MapPin, Pencil, Check, X, Camera, Trash } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import EmailChangeForm from "@/components/EmailChangeForm";
import UserDashboard from "@/components/UserDashboard";
import ProfileManagement from "@/components/profile/ProfileManagement";

const ProfilePage = () => {
  const { user, logout, setUser } = useUser();
  const navigate = useNavigate();
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Editing states
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState({
    fullName: "",
    email: "",
    city: "",
    country: "",
    dateOfBirth: "",
    bio: "",
    phoneNumber: "",
    profilePicture: ""
  });
  const [previewImage, setPreviewImage] = useState("");

  useEffect(() => {
    if (!user) {
      navigate("/login");
    } else {
      setEditedUser({
        fullName: user.fullName || "",
        email: user.email,
        city: user.city || "",
        country: user.country || "",
        dateOfBirth: user.dateOfBirth || "",
        bio: user.bio || "",
        phoneNumber: user.phoneNumber || "",
        profilePicture: user.profilePicture || ""
      });
      setPreviewImage(user.profilePicture || "");
    }
  }, [user, navigate]);

  if (!user) {
    return null;
  }

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const getInitials = (name: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map(part => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Cancel editing
      setEditedUser({
        fullName: user.fullName || "",
        email: user.email,
        city: user.city || "",
        country: user.country || "",
        dateOfBirth: user.dateOfBirth || "",
        bio: user.bio || "",
        phoneNumber: user.phoneNumber || "",
        profilePicture: user.profilePicture || ""
      });
      setPreviewImage(user.profilePicture || "");
    }
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedUser(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProfilePictureClick = () => {
    if (isEditing && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, this would upload to a server
      // For now, we'll just use a data URL
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        setPreviewImage(result);
        setEditedUser(prev => ({
          ...prev,
          profilePicture: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveProfilePicture = () => {
    setPreviewImage("");
    setEditedUser(prev => ({
      ...prev,
      profilePicture: ""
    }));
  };

  const handleSaveProfile = () => {
    const updatedUser = {
      ...user,
      fullName: editedUser.fullName,
      city: editedUser.city,
      country: editedUser.country,
      dateOfBirth: editedUser.dateOfBirth,
      bio: editedUser.bio,
      phoneNumber: editedUser.phoneNumber,
      profilePicture: previewImage
    };

    setUser(updatedUser);
    setIsEditing(false);
    toast.success(translations.profileUpdated || "Profile updated successfully");
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-1 bg-gray-50 dark:bg-gray-900 py-12">
        <div className="container mx-auto px-4 max-w-5xl">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-8">
            <div className="h-32 bg-gradient-to-r from-primary to-blue-600"></div>
            <div className="px-6 py-4 relative">
              <div className="flex flex-col sm:flex-row gap-4 items-center sm:items-start">
                <div className="relative">
                  <Avatar
                    className={`w-24 h-24 -mt-16 border-4 border-white dark:border-gray-800 bg-white ${isEditing ? 'cursor-pointer' : ''}`}
                    onClick={handleProfilePictureClick}
                  >
                    <AvatarImage src={isEditing ? previewImage : user.profilePicture || `https://ui-avatars.com/api/?name=${user.fullName || user.email}&background=0D8ABC&color=fff`} />
                    <AvatarFallback>{getInitials(user.fullName || user.email)}</AvatarFallback>
                    {isEditing && (
                      <div className="absolute inset-0 bg-black/40 rounded-full flex items-center justify-center">
                        <Camera className="h-6 w-6 text-white" />
                      </div>
                    )}
                  </Avatar>
                  {isEditing && previewImage && (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute -bottom-2 -right-2 h-7 w-7 rounded-full"
                      onClick={handleRemoveProfilePicture}
                    >
                      <Trash className="h-3 w-3" />
                    </Button>
                  )}
                  {isEditing && (
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      accept="image/*"
                      className="hidden"
                    />
                  )}
                </div>

                <div className={`flex-1 text-center sm:text-left ${isRTL ? 'sm:text-right' : ''}`}>
                  <div className="flex items-center justify-center sm:justify-start gap-2">
                    <h1 className="text-2xl font-bold dark:text-white">
                      {user.fullName || user.email.split('@')[0]}
                    </h1>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="rounded-full"
                      onClick={handleEditToggle}
                    >
                      {isEditing ? <X className="h-4 w-4" /> : <Pencil className="h-4 w-4" />}
                    </Button>
                  </div>

                  <p className="text-gray-500 dark:text-gray-400 flex items-center justify-center sm:justify-start gap-1 mt-1">
                    <User className="h-4 w-4" />
                    {user.email}
                  </p>

                  {(user.city || user.country) && (
                    <p className="text-gray-500 dark:text-gray-400 flex items-center justify-center sm:justify-start gap-1 mt-1">
                      <MapPin className="h-4 w-4" />
                      {[user.city, user.country].filter(Boolean).join(", ")}
                    </p>
                  )}

                  <p className="text-gray-500 dark:text-gray-400 flex items-center justify-center sm:justify-start gap-1 mt-1">
                    <CalendarIcon className="h-4 w-4" />
                    {translations.memberSince || "Member since"}: {format(user.joinDate, 'MMMM yyyy')}
                  </p>
                </div>

                <div>
                  <Button variant="outline" size="sm" onClick={handleLogout} className="gap-1">
                    <LogOut className="h-4 w-4" />
                    <span>{translations.logout}</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <Tabs defaultValue="dashboard" className="w-full">
            <TabsList className="grid w-full max-w-lg mx-auto grid-cols-4">
              <TabsTrigger value="dashboard">{translations.dashboard || "Dashboard"}</TabsTrigger>
              <TabsTrigger value="profile">{translations.profile || "Profile"}</TabsTrigger>
              <TabsTrigger value="manage">Manage</TabsTrigger>
              <TabsTrigger value="settings">{translations.settings || "Settings"}</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="mt-6">
              <UserDashboard />
            </TabsContent>

            <TabsContent value="manage" className="mt-6">
              <ProfileManagement />
            </TabsContent>

            <TabsContent value="profile" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="md:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.profileInformation || "Profile Information"}</CardTitle>
                      <CardDescription>
                        {translations.manageYourProfileDetails || "Manage your profile details and personal information"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {isEditing ? (
                        // Edit form
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="fullName">{translations.fullName || "Full Name"}</Label>
                              <Input
                                id="fullName"
                                name="fullName"
                                value={editedUser.fullName}
                                onChange={handleInputChange}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="email">{translations.email || "Email"}</Label>
                              <HoverCard>
                                <HoverCardTrigger asChild>
                                  <div>
                                    <Input
                                      id="email"
                                      name="email"
                                      value={editedUser.email}
                                      disabled
                                    />
                                  </div>
                                </HoverCardTrigger>
                                <HoverCardContent className="w-80">
                                  <div className="space-y-2">
                                    <h4 className="text-sm font-semibold">{translations.changeEmail || "Change Email"}</h4>
                                    <p className="text-sm">
                                      {translations.changeEmailInSettings || "To change your email address, please go to the Settings tab."}
                                    </p>
                                  </div>
                                </HoverCardContent>
                              </HoverCard>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="phoneNumber">{translations.phoneNumber || "Phone Number"}</Label>
                              <Input
                                id="phoneNumber"
                                name="phoneNumber"
                                value={editedUser.phoneNumber}
                                onChange={handleInputChange}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="dateOfBirth">{translations.dateOfBirth || "Date of Birth"}</Label>
                              <Input
                                id="dateOfBirth"
                                name="dateOfBirth"
                                type="date"
                                value={editedUser.dateOfBirth}
                                onChange={handleInputChange}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="city">{translations.city || "City"}</Label>
                              <Input
                                id="city"
                                name="city"
                                value={editedUser.city}
                                onChange={handleInputChange}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="country">{translations.country || "Country"}</Label>
                              <Input
                                id="country"
                                name="country"
                                value={editedUser.country}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bio">{translations.bio || "Bio"}</Label>
                            <Textarea
                              id="bio"
                              name="bio"
                              value={editedUser.bio}
                              onChange={handleInputChange}
                              placeholder="Tell us a bit about yourself..."
                              className="min-h-[100px]"
                            />
                          </div>

                          <div className="flex justify-end gap-2 mt-4">
                            <Button
                              variant="outline"
                              onClick={handleEditToggle}
                            >
                              {translations.cancel || "Cancel"}
                            </Button>
                            <Button onClick={handleSaveProfile}>
                              {translations.saveChanges || "Save Changes"}
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // View mode
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {translations.fullName || "Full Name"}
                              </h3>
                              <p className="mt-1">{user.fullName || "-"}</p>
                            </div>

                            <div>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {translations.email || "Email"}
                              </h3>
                              <p className="mt-1">{user.email}</p>
                            </div>

                            <div>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {translations.phoneNumber || "Phone Number"}
                              </h3>
                              <p className="mt-1">{user.phoneNumber || "-"}</p>
                            </div>

                            <div>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {translations.dateOfBirth || "Date of Birth"}
                              </h3>
                              <p className="mt-1">{user.dateOfBirth || "-"}</p>
                            </div>

                            <div>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {translations.city || "City"}
                              </h3>
                              <p className="mt-1">{user.city || "-"}</p>
                            </div>

                            <div>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {translations.country || "Country"}
                              </h3>
                              <p className="mt-1">{user.country || "-"}</p>
                            </div>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              {translations.bio || "Bio"}
                            </h3>
                            <p className="mt-1">{user.bio || "-"}</p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.loyaltyPoints || "Loyalty Points"}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <LoyaltyPoints isDialog={true} />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="md:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.accountSettings || "Account Settings"}</CardTitle>
                      <CardDescription>
                        {translations.manageAccountSettings || "Manage your account settings and preferences"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="space-y-2">
                          <h3 className="text-lg font-medium">{translations.emailPreferences || "Email Preferences"}</h3>
                          <p className="text-sm text-muted-foreground">
                            {translations.emailPreferencesDesc || "Manage your email preferences"}
                          </p>
                        </div>

                        <EmailChangeForm />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle>{translations.dangerZone || "Danger Zone"}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <h3 className="text-sm font-medium text-destructive">
                            {translations.deleteAccount || "Delete Account"}
                          </h3>
                          <p className="text-xs text-muted-foreground">
                            {translations.deleteAccountWarning || "This action cannot be undone. This will permanently delete your account and remove your data from our servers."}
                          </p>
                        </div>

                        <Button variant="destructive" size="sm">
                          {translations.deleteAccount || "Delete Account"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ProfilePage;
