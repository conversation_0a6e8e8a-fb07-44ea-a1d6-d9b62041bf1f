
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      rides: {
        Row: {
          id: string
          origin: string
          destination: string
          departure_time: string
          arrival_time: string
          price: number
          distance: string
          seats_available: number
          car_model: string
          car_color: string
          driver_id: string
          features: string[] | null
          created_at: string
        }
        Insert: {
          id?: string
          origin: string
          destination: string
          departure_time: string
          arrival_time: string
          price: number
          distance: string
          seats_available: number
          car_model: string
          car_color: string
          driver_id: string
          features?: string[] | null
          created_at?: string
        }
        Update: {
          id?: string
          origin?: string
          destination?: string
          departure_time?: string
          arrival_time?: string
          price?: number
          distance?: string
          seats_available?: number
          car_model?: string
          car_color?: string
          driver_id?: string
          features?: string[] | null
          created_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          full_name: string | null
          avatar_url: string | null
          rating: number | null
          ride_count: number | null
          join_date: string | null
          rides_completed: number | null
          rides_offered: number | null
          loyalty_points: number | null
          referrals: number | null
          city: string | null
          country: string | null
          date_of_birth: string | null
          bio: string | null
          phone_number: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          full_name?: string | null
          avatar_url?: string | null
          rating?: number | null
          ride_count?: number | null
          join_date?: string | null
          rides_completed?: number | null
          rides_offered?: number | null
          loyalty_points?: number | null
          referrals?: number | null
          city?: string | null
          country?: string | null
          date_of_birth?: string | null
          bio?: string | null
          phone_number?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          full_name?: string | null
          avatar_url?: string | null
          rating?: number | null
          ride_count?: number | null
          join_date?: string | null
          rides_completed?: number | null
          rides_offered?: number | null
          loyalty_points?: number | null
          referrals?: number | null
          city?: string | null
          country?: string | null
          date_of_birth?: string | null
          bio?: string | null
          phone_number?: string | null
          updated_at?: string | null
        }
      }
      ride_bookings: {
        Row: {
          id: string
          ride_id: string
          user_id: string
          created_at: string
        }
        Insert: {
          id?: string
          ride_id: string
          user_id: string
          created_at?: string
        }
        Update: {
          id?: string
          ride_id?: string
          user_id?: string
          created_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          sender_id: string
          recipient_id: string
          content: string
          timestamp: string
          read: boolean
        }
        Insert: {
          id?: string
          sender_id: string
          recipient_id: string
          content: string
          timestamp?: string
          read?: boolean
        }
        Update: {
          id?: string
          sender_id?: string
          recipient_id?: string
          content?: string
          timestamp?: string
          read?: boolean
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          content: string
          timestamp: string
          read: boolean
          type: string
          related_id: string | null
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          content: string
          timestamp?: string
          read?: boolean
          type: string
          related_id?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string
          timestamp?: string
          read?: boolean
          type?: string
          related_id?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
