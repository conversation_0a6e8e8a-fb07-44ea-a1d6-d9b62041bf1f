# 📚 CoJourneyHub API Documentation

Complete API reference for the CoJourneyHub ride-sharing platform.

## 🔗 **Base URL**
```
Development: http://localhost:8000/api
Production: https://your-domain.com/api
```

## 🔐 **Authentication**

The API uses session-based authentication. After login, session cookies are automatically handled.

### Register User
```http
POST /auth/register/
Content-Type: application/json

{
  "username": "johndo<PERSON>",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "password_confirm": "securepassword123",
  "full_name": "John Doe",
  "city": "Tunis",
  "country": "Tunisia"
}
```

**Response (201):**
```json
{
  "user": {
    "id": 1,
    "username": "johndo<PERSON>",
    "email": "<EMAIL>",
    "full_name": "<PERSON> Doe",
    "city": "Tunis",
    "country": "Tunisia",
    "rating": "5.00",
    "is_verified": false
  },
  "message": "User registered successfully"
}
```

### Login User
```http
POST /auth/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response (200):**
```json
{
  "user": {
    "id": 1,
    "username": "johndoe",
    "email": "<EMAIL>",
    "full_name": "John Doe"
  },
  "message": "Login successful"
}
```

### Logout User
```http
POST /auth/logout/
```

## 🗺️ **Locations**

### Get All Locations
```http
GET /locations/
```

**Response (200):**
```json
{
  "count": 53,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Tunis",
      "latitude": 36.8065,
      "longitude": 10.1815,
      "type": "city"
    }
  ]
}
```

## 🚗 **Rides**

### List/Search Rides
```http
GET /rides/?origin=Tunis&destination=Sousse&date=2024-01-15
```

**Query Parameters:**
- `origin` (optional): Origin city
- `destination` (optional): Destination city
- `date` (optional): Departure date (YYYY-MM-DD)
- `min_price` (optional): Minimum price
- `max_price` (optional): Maximum price

**Response (200):**
```json
[
  {
    "id": 1,
    "driver": {
      "id": 2,
      "full_name": "Jane Driver",
      "rating": "4.8"
    },
    "origin": "Tunis",
    "destination": "Sousse",
    "departure_time": "2024-01-15T14:00:00Z",
    "arrival_time": "2024-01-15T16:00:00Z",
    "price": 25.0,
    "seats_available": 3,
    "car_model": "Toyota Corolla",
    "car_color": "Blue",
    "distance": "140 km"
  }
]
```

### Create Ride
```http
POST /rides/
Content-Type: application/json

{
  "origin": "Tunis",
  "destination": "Sousse",
  "departure_time": "2024-01-15T14:00:00Z",
  "arrival_time": "2024-01-15T16:00:00Z",
  "price": 25.0,
  "seats_available": 3,
  "car_model": "Toyota Corolla",
  "car_color": "Blue",
  "distance": "140 km"
}
```

### Get Ride Details
```http
GET /rides/{id}/
```

### Book Ride
```http
POST /rides/{id}/book/
Content-Type: application/json

{
  "seats_requested": 1,
  "pickup_location": "Downtown Tunis",
  "notes": "I'll be waiting near the main entrance"
}
```

## 💬 **Messaging**

### List Conversations
```http
GET /messaging/conversations/
```

**Response (200):**
```json
[
  {
    "id": 1,
    "participants": [
      {
        "id": 1,
        "username": "johndoe",
        "full_name": "John Doe"
      },
      {
        "id": 2,
        "username": "janedoe",
        "full_name": "Jane Doe"
      }
    ],
    "last_message": {
      "id": 5,
      "content": "See you tomorrow!",
      "created_at": "2024-01-14T10:30:00Z"
    },
    "unread_count": 2,
    "other_participant": {
      "id": 2,
      "username": "janedoe",
      "full_name": "Jane Doe"
    },
    "created_at": "2024-01-14T09:00:00Z"
  }
]
```

### Create Conversation
```http
POST /messaging/conversations/
Content-Type: application/json

{
  "participant_id": 2
}
```

### Get Messages
```http
GET /messaging/conversations/{conversation_id}/messages/
```

**Response (200):**
```json
[
  {
    "id": 1,
    "sender": {
      "id": 1,
      "username": "johndoe",
      "full_name": "John Doe"
    },
    "content": "Hi! I'm interested in your ride to Sousse.",
    "message_type": "text",
    "is_read": true,
    "created_at": "2024-01-14T09:15:00Z"
  }
]
```

### Send Message
```http
POST /messaging/conversations/{conversation_id}/messages/
Content-Type: application/json

{
  "content": "Hello! Yes, I have seats available.",
  "message_type": "text"
}
```

### Mark Message as Read
```http
POST /messaging/messages/{message_id}/read/
```

## 👤 **User Profile**

### Get User Profile
```http
GET /auth/profile/
```

### Update User Profile
```http
PUT /auth/profile/
Content-Type: application/json

{
  "full_name": "John Updated Doe",
  "bio": "Love traveling and meeting new people!",
  "city": "Tunis"
}
```

## 📊 **Error Responses**

### 400 Bad Request
```json
{
  "error": "Validation failed",
  "details": {
    "email": ["This field is required."]
  }
}
```

### 401 Unauthorized
```json
{
  "error": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "error": "Permission denied"
}
```

### 404 Not Found
```json
{
  "error": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error"
}
```

## 📝 **Request/Response Headers**

### Required Headers
```http
Content-Type: application/json
```

### Authentication
Session-based authentication uses cookies automatically after login.

## 🔄 **Rate Limiting**

- **Authentication endpoints**: 5 requests per minute
- **General API**: 100 requests per minute
- **Messaging**: 50 requests per minute

## 📱 **Mobile App Integration**

The API is designed to work seamlessly with mobile applications:

- RESTful design principles
- JSON responses
- Proper HTTP status codes
- Session-based authentication
- CORS enabled for web apps

## 🧪 **Testing the API**

### Using cURL
```bash
# Register user
curl -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"testpass123","password_confirm":"testpass123","full_name":"Test User","city":"Tunis","country":"Tunisia"}'

# Login user
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{"email":"<EMAIL>","password":"testpass123"}'

# Get locations (using saved cookies)
curl -X GET http://localhost:8000/api/locations/ \
  -b cookies.txt
```

### Using Postman
1. Import the API endpoints
2. Set base URL to `http://localhost:8000/api`
3. Use session cookies for authentication
4. Test all endpoints with sample data

---

**📚 This API documentation covers all available endpoints for the CoJourneyHub platform.**
