import { apiClient } from '@/lib/supabase';
import { Ride, Driver } from '@/data/rides';

export interface RideSearchParams {
  origin?: string;
  destination?: string;
  departureDate?: string;
  passengers?: number;
  maxPrice?: number;
  features?: string[];
}

export interface CreateRideData {
  origin: string;
  destination: string;
  departureTime: string;
  arrivalTime: string;
  price: number;
  distance: string;
  seatsAvailable: number;
  totalSeats?: number;
  carModel: string;
  carColor: string;
  features?: string[];
  originLat?: number;
  originLng?: number;
  destinationLat?: number;
  destinationLng?: number;
}

class RideService {
  async searchRides(params: RideSearchParams): Promise<Ride[]> {
    try {
      console.log('Searching rides with params:', params);

      const searchData = {
        origin: params.origin,
        destination: params.destination,
        departure_date: params.departureDate,
        max_price: params.maxPrice,
        min_seats: params.passengers,
        features: params.features
      };

      console.log('Sending search request to backend:', searchData);
      const response = await apiClient.post('/rides/search/', searchData);
      console.log('Backend search response:', response);

      const transformedRides = this.transformDjangoRides(response);
      console.log('Transformed rides:', transformedRides);

      return transformedRides;
    } catch (error) {
      console.error('Error searching rides:', error);
      console.log('API Error details:', error);
      console.log('Falling back to mock data due to API error');
      return this.getMockRides(params);
    }
  }

  async createRide(rideData: CreateRideData, driverId?: string): Promise<Ride | null> {
    try {
      const createData = {
        origin: rideData.origin,
        destination: rideData.destination,
        departure_time: rideData.departureTime,
        arrival_time: rideData.arrivalTime,
        price: parseFloat(rideData.price.toString()),
        distance: rideData.distance,
        seats_available: parseInt(rideData.seatsAvailable.toString()),
        total_seats: parseInt((rideData.totalSeats || rideData.seatsAvailable).toString()),
        car_model: rideData.carModel,
        car_color: rideData.carColor,
        features: rideData.features || [],
        notes: rideData.notes || ''
      };

      console.log('Creating ride with data:', createData);
      const response = await apiClient.post('/rides/', createData);
      console.log('Ride created successfully:', response);

      const transformedRide = this.transformDjangoRide(response);
      console.log('Transformed created ride:', transformedRide);

      return transformedRide;
    } catch (error) {
      console.error('Error creating ride:', error);
      // For development/demo purposes, simulate successful ride creation
      console.log('Simulating successful ride creation with data:', rideData);

      const mockRide: Ride = {
        id: `mock-created-${Date.now()}`,
        origin: rideData.origin,
        destination: rideData.destination,
        departureTime: rideData.departureTime,
        arrivalTime: rideData.arrivalTime,
        price: parseFloat(rideData.price.toString()),
        distance: rideData.distance,
        seatsAvailable: parseInt(rideData.seatsAvailable.toString()),
        carModel: rideData.carModel,
        carColor: rideData.carColor,
        driver: {
          id: driverId || 'current-user',
          name: 'Current User',
          avatar: '',
          rating: 4.5,
          rideCount: 1,
          ridesCompleted: 0,
          ridesOffered: 1
        },
        features: rideData.features || [],
        driverId: driverId || 'current-user'
      };

      return mockRide;
    }
  }

  async getRideById(rideId: string): Promise<Ride | null> {
    try {
      const response = await apiClient.get(`/rides/${rideId}/`);
      return this.transformDjangoRide(response);
    } catch (error) {
      console.error('Error fetching ride:', error);
      console.log('API Error details:', error);
      console.log('Falling back to mock data due to API error');
      return this.getMockRideById(rideId);
    }
  }

  // Alias method for consistency
  async getRide(rideId: string): Promise<Ride | null> {
    return this.getRideById(rideId);
  }

  async getUserRides(userId: string, type: 'offered' | 'booked' = 'offered'): Promise<Ride[]> {
    try {
      console.log(`Fetching ${type} rides for user:`, userId);

      const endpoint = type === 'offered' ? '/rides/my-rides/' : '/rides/bookings/';
      console.log('API endpoint:', endpoint);

      const response = await apiClient.get(endpoint);
      console.log(`${type} rides response:`, response);

      if (type === 'booked') {
        // Extract rides from bookings
        const rides = response.map((booking: any) => this.transformDjangoRide(booking.ride));
        console.log('Transformed booked rides:', rides);
        return rides;
      }

      const rides = this.transformDjangoRides(response.results || response);
      console.log('Transformed offered rides:', rides);
      return rides;
    } catch (error) {
      console.error('Error fetching user rides:', error);
      console.log('Falling back to mock data for development');
      return this.getMockUserRides(userId, type);
    }
  }

  private getMockUserRides(userId: string, type: 'offered' | 'booked'): Ride[] {
    const allMockRides = this.getAllMockRides();

    if (type === 'offered') {
      // Return rides where the user is the driver
      return allMockRides.filter(ride => ride.driverId === userId);
    } else {
      // Return some mock booked rides
      return allMockRides.slice(0, 2); // Return first 2 rides as booked
    }
  }

  async bookRide(rideId: string, bookingData: any): Promise<any> {
    try {
      console.log('Booking ride:', rideId, 'with data:', bookingData);
      const response = await apiClient.post(`/rides/${rideId}/book/`, bookingData);
      console.log('Booking successful:', response);
      return response;
    } catch (error) {
      console.error('Error booking ride:', error);
      // For development/demo purposes, simulate successful booking
      console.log('Simulating successful booking for ride:', rideId, 'with data:', bookingData);
      return {
        id: `booking-${Date.now()}`,
        ride_id: rideId,
        seats_booked: bookingData.seats_booked,
        pickup_location: bookingData.pickup_location,
        dropoff_location: bookingData.dropoff_location,
        special_requests: bookingData.special_requests,
        status: 'confirmed',
        created_at: new Date().toISOString()
      };
    }
  }

  async getBookings(): Promise<any[]> {
    try {
      const response = await apiClient.get('/rides/bookings/');
      return response;
    } catch (error) {
      console.error('Error fetching bookings:', error);
      return [];
    }
  }

  private transformDjangoRides(djangoRides: any[]): Ride[] {
    return djangoRides.map(ride => this.transformDjangoRide(ride));
  }

  private transformDjangoRide(djangoRide: any): Ride {
    console.log('Transforming Django ride:', djangoRide);

    return {
      id: djangoRide.id.toString(),
      origin: djangoRide.origin,
      destination: djangoRide.destination,
      departureTime: djangoRide.departure_time,
      arrivalTime: djangoRide.arrival_time,
      price: parseFloat(djangoRide.price),
      distance: djangoRide.distance,
      seatsAvailable: djangoRide.seats_available || djangoRide.seats_remaining,
      carModel: djangoRide.car_model,
      carColor: djangoRide.car_color,
      driver: {
        id: (djangoRide.driver?.id || djangoRide.driver_id || 'unknown').toString(),
        name: djangoRide.driver?.full_name || djangoRide.driver?.username || djangoRide.driver_name || 'Unknown Driver',
        avatar: djangoRide.driver?.avatar || djangoRide.driver_avatar || '',
        rating: parseFloat(djangoRide.driver?.rating || djangoRide.driver_rating || '4.5'),
        rideCount: djangoRide.driver?.ride_count || djangoRide.driver?.rides_completed || 0,
        ridesCompleted: djangoRide.driver?.rides_completed || 0,
        ridesOffered: djangoRide.driver?.rides_offered || 0,
      },
      features: djangoRide.features || [],
      driverId: (djangoRide.driver?.id || djangoRide.driver_id || 'unknown').toString()
    };
  }

  private getMockRides(params: RideSearchParams): Ride[] {
    // Return mock data when backend is not configured
    const mockRides: Ride[] = this.getAllMockRides();

    return mockRides.filter(ride => {
      if (params.passengers && ride.seatsAvailable < params.passengers) return false;
      if (params.maxPrice && ride.price > params.maxPrice) return false;
      return true;
    });
  }

  private getMockRideById(rideId: string): Ride | null {
    const mockRides = this.getAllMockRides();
    return mockRides.find(ride => ride.id === rideId) || null;
  }

  private getAllMockRides(): Ride[] {
    return [
      {
        id: 'mock-1',
        origin: 'Tunis',
        destination: 'Sousse',
        departureTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
        arrivalTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),
        price: 25,
        distance: '140 km',
        seatsAvailable: 3,
        carModel: 'Peugeot 308',
        carColor: 'White',
        driver: {
          id: 'mock-driver-1',
          name: 'Ahmed Ben Ali',
          avatar: '',
          rating: 4.8,
          rideCount: 45,
          ridesCompleted: 42,
          ridesOffered: 45
        },
        features: ['Air Conditioning', 'Music', 'Non-Smoking'],
        driverId: 'mock-driver-1'
      },
      {
        id: 'mock-2',
        origin: 'Sfax',
        destination: 'Tunis',
        departureTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
        arrivalTime: new Date(Date.now() + 9 * 60 * 60 * 1000).toISOString(),
        price: 30,
        distance: '270 km',
        seatsAvailable: 2,
        carModel: 'Renault Clio',
        carColor: 'Blue',
        driver: {
          id: 'mock-driver-2',
          name: 'Fatma Trabelsi',
          avatar: '',
          rating: 4.6,
          rideCount: 32,
          ridesCompleted: 30,
          ridesOffered: 32
        },
        features: ['Air Conditioning', 'Non-Smoking'],
        driverId: 'mock-driver-2'
      },
      {
        id: 'mock-3',
        origin: 'Monastir',
        destination: 'Kairouan',
        departureTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        arrivalTime: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
        price: 15,
        distance: '80 km',
        seatsAvailable: 4,
        carModel: 'Dacia Logan',
        carColor: 'Gray',
        driver: {
          id: 'mock-driver-3',
          name: 'Mohamed Sassi',
          avatar: '',
          rating: 4.9,
          rideCount: 67,
          ridesCompleted: 65,
          ridesOffered: 67
        },
        features: ['Music', 'Pet-Friendly'],
        driverId: 'mock-driver-3'
      }
    ];
  }
}

export const rideService = new RideService();
