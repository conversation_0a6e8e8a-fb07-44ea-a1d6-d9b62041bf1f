from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class Notification(models.Model):
    """Model for user notifications"""
    
    NOTIFICATION_TYPES = [
        ('ride_booked', 'Ride Booked'),
        ('ride_cancelled', 'Ride Cancelled'),
        ('booking_cancelled', 'Booking Cancelled'),
        ('new_message', 'New Message'),
        ('ride_reminder', 'Ride Reminder'),
        ('payment_received', 'Payment Received'),
        ('review_received', 'Review Received'),
        ('system', 'System Notification'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=255)
    content = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    
    # Optional related objects
    related_ride = models.ForeignKey(
        'rides.Ride', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True
    )
    related_booking = models.ForeignKey(
        'rides.RideBooking', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True
    )
    related_user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='notifications_about'
    )
    
    # Action URL for frontend navigation
    action_url = models.CharField(max_length=255, blank=True)
    
    # Status
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['is_read']),
            models.Index(fields=['notification_type']),
        ]
    
    def __str__(self):
        return f"{self.user.full_name}: {self.title}"
    
    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = models.timezone.now()
            self.save(update_fields=['is_read', 'read_at'])


class NotificationPreference(models.Model):
    """Model for user notification preferences"""
    
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE, 
        related_name='notification_preferences'
    )
    
    # Email notifications
    email_ride_booked = models.BooleanField(default=True)
    email_ride_cancelled = models.BooleanField(default=True)
    email_new_message = models.BooleanField(default=True)
    email_ride_reminder = models.BooleanField(default=True)
    email_payment_received = models.BooleanField(default=True)
    email_review_received = models.BooleanField(default=True)
    email_system = models.BooleanField(default=True)
    
    # Push notifications (for future mobile app)
    push_ride_booked = models.BooleanField(default=True)
    push_ride_cancelled = models.BooleanField(default=True)
    push_new_message = models.BooleanField(default=True)
    push_ride_reminder = models.BooleanField(default=True)
    push_payment_received = models.BooleanField(default=True)
    push_review_received = models.BooleanField(default=True)
    push_system = models.BooleanField(default=True)
    
    # SMS notifications
    sms_ride_reminder = models.BooleanField(default=False)
    sms_ride_cancelled = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'notification_preferences'
    
    def __str__(self):
        return f"{self.user.full_name} Notification Preferences"
