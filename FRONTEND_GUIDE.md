# 🎨 CoJourneyHub Frontend Development Guide

Complete guide for developing and customizing the CoJourneyHub React frontend.

## 🏗️ **Architecture Overview**

The frontend is built with modern React patterns and TypeScript for type safety.

### Key Technologies
- **React 18** with Hooks and Functional Components
- **TypeScript** for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for utility-first styling
- **React Router** for client-side routing
- **Axios** for API communication
- **Lucide React** for icons

## 📁 **Project Structure**

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI components (buttons, inputs, etc.)
│   ├── layout/          # Layout components (header, footer, sidebar)
│   ├── ride-offer/      # Ride-related components
│   ├── messaging/       # Chat and messaging components
│   └── auth/            # Authentication components
├── pages/               # Page components
│   ├── HomePage.tsx
│   ├── RidesPage.tsx
│   ├── MessagesPage.tsx
│   └── ProfilePage.tsx
├── hooks/               # Custom React hooks
│   ├── use-auth.ts
│   ├── use-messages.ts
│   └── use-rides.ts
├── contexts/            # React contexts for state management
│   ├── AuthContext.tsx
│   └── UserContext.tsx
├── services/            # API service layers
│   ├── apiService.ts
│   ├── authService.ts
│   ├── ridesService.ts
│   └── messagingService.ts
├── types/               # TypeScript type definitions
│   ├── auth.ts
│   ├── rides.ts
│   └── user.ts
├── lib/                 # Utility libraries
│   └── utils.ts
└── data/                # Static data and constants
    └── locations.ts
```

## 🔧 **Development Setup**

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🎨 **Styling Guide**

### Tailwind CSS Classes
The project uses Tailwind CSS for styling. Common patterns:

```tsx
// Layout
<div className="container mx-auto px-4">
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {/* Content */}
  </div>
</div>

// Cards
<div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
  {/* Card content */}
</div>

// Buttons
<button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
  Click me
</button>

// Forms
<input className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
```

### Custom CSS
Custom styles are in `src/index.css` using Tailwind's `@apply` directive:

```css
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors;
}

.card {
  @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow;
}
```

## 🔗 **API Integration**

### Service Layer Pattern
All API calls are abstracted into service layers:

```typescript
// services/ridesService.ts
import { apiService } from './apiService';

export const ridesService = {
  async getRides(params?: RideSearchParams) {
    const response = await apiService.get('/rides/', { params });
    return response.data;
  },

  async createRide(rideData: CreateRideData) {
    const response = await apiService.post('/rides/', rideData);
    return response.data;
  }
};
```

### Custom Hooks for Data Fetching
```typescript
// hooks/use-rides.ts
import { useState, useEffect } from 'react';
import { ridesService } from '../services/ridesService';

export const useRides = (searchParams?: RideSearchParams) => {
  const [rides, setRides] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRides = async () => {
      try {
        setLoading(true);
        const data = await ridesService.getRides(searchParams);
        setRides(data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchRides();
  }, [searchParams]);

  return { rides, loading, error };
};
```

## 🔐 **Authentication Flow**

### Auth Context
```typescript
// contexts/AuthContext.tsx
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const login = async (email: string, password: string) => {
    const response = await authService.login(email, password);
    setUser(response.user);
    return response;
  };

  const logout = async () => {
    await authService.logout();
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### Protected Routes
```typescript
// components/ProtectedRoute.tsx
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (!user) return <Navigate to="/login" />;

  return <>{children}</>;
};
```

## 📱 **Component Development**

### Component Structure
```typescript
// components/RideCard.tsx
interface RideCardProps {
  ride: Ride;
  onBook?: (rideId: string) => void;
  className?: string;
}

export const RideCard: React.FC<RideCardProps> = ({ 
  ride, 
  onBook, 
  className = '' 
}) => {
  return (
    <div className={`card ${className}`}>
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold">
            {ride.origin} → {ride.destination}
          </h3>
          <p className="text-gray-600">
            {formatDate(ride.departure_time)}
          </p>
        </div>
        <span className="text-xl font-bold text-green-600">
          {ride.price} TND
        </span>
      </div>
      
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <User className="w-4 h-4" />
          <span>{ride.driver.full_name}</span>
          <Star className="w-4 h-4 text-yellow-500" />
          <span>{ride.driver.rating}</span>
        </div>
        
        {onBook && (
          <button 
            onClick={() => onBook(ride.id)}
            className="btn-primary"
          >
            Book Ride
          </button>
        )}
      </div>
    </div>
  );
};
```

### Form Handling
```typescript
// components/RideOfferForm.tsx
export const RideOfferForm: React.FC = () => {
  const [formData, setFormData] = useState<RideFormData>({
    origin: '',
    destination: '',
    departure_time: '',
    price: 0,
    seats_available: 1
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await ridesService.createRide(formData);
      // Handle success
    } catch (error) {
      // Handle error
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">
          Origin
        </label>
        <input
          type="text"
          value={formData.origin}
          onChange={(e) => setFormData(prev => ({ 
            ...prev, 
            origin: e.target.value 
          }))}
          className="w-full px-3 py-2 border rounded-md"
          required
        />
      </div>
      {/* More form fields */}
      <button type="submit" className="btn-primary w-full">
        Create Ride
      </button>
    </form>
  );
};
```

## 🔄 **State Management**

### Local State with useState
For component-level state:
```typescript
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
```

### Context for Global State
For app-wide state (user, theme, etc.):
```typescript
// contexts/UserContext.tsx
export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [preferences, setPreferences] = useState<UserPreferences>({});

  return (
    <UserContext.Provider value={{ user, preferences, setUser, setPreferences }}>
      {children}
    </UserContext.Provider>
  );
};
```

## 🧪 **Testing**

### Component Testing
```typescript
// __tests__/RideCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { RideCard } from '../components/RideCard';

const mockRide = {
  id: '1',
  origin: 'Tunis',
  destination: 'Sousse',
  price: 25,
  driver: { full_name: 'John Doe', rating: '4.8' }
};

test('renders ride information correctly', () => {
  render(<RideCard ride={mockRide} />);
  
  expect(screen.getByText('Tunis → Sousse')).toBeInTheDocument();
  expect(screen.getByText('25 TND')).toBeInTheDocument();
  expect(screen.getByText('John Doe')).toBeInTheDocument();
});

test('calls onBook when book button is clicked', () => {
  const mockOnBook = jest.fn();
  render(<RideCard ride={mockRide} onBook={mockOnBook} />);
  
  fireEvent.click(screen.getByText('Book Ride'));
  expect(mockOnBook).toHaveBeenCalledWith('1');
});
```

## 🚀 **Performance Optimization**

### Code Splitting
```typescript
// Lazy load pages
const HomePage = lazy(() => import('../pages/HomePage'));
const RidesPage = lazy(() => import('../pages/RidesPage'));

// Use Suspense
<Suspense fallback={<LoadingSpinner />}>
  <Routes>
    <Route path="/" element={<HomePage />} />
    <Route path="/rides" element={<RidesPage />} />
  </Routes>
</Suspense>
```

### Memoization
```typescript
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return calculateExpensiveValue(data);
}, [data]);

// Memoize components
const MemoizedRideCard = memo(RideCard);
```

## 🎯 **Best Practices**

### TypeScript
- Always define interfaces for props and data
- Use strict mode in tsconfig.json
- Avoid `any` type, use `unknown` instead

### Components
- Keep components small and focused
- Use composition over inheritance
- Extract reusable logic into custom hooks

### Performance
- Use React.memo for expensive components
- Implement proper key props for lists
- Lazy load routes and heavy components

### Accessibility
- Use semantic HTML elements
- Add proper ARIA labels
- Ensure keyboard navigation works

---

**🎨 This guide provides everything needed to develop and customize the CoJourneyHub frontend!**
