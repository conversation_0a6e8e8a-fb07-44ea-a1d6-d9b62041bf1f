from django.urls import path
from . import views

urlpatterns = [
    # Reviews
    path('', views.ReviewListCreateView.as_view(), name='review-list-create'),
    path('<int:pk>/', views.ReviewDetailView.as_view(), name='review-detail'),
    path('my-reviews/', views.my_reviews, name='my-reviews'),
    path('pending/', views.pending_reviews, name='pending-reviews'),
    
    # User review stats
    path('users/<int:user_id>/stats/', views.user_review_stats, name='user-review-stats'),
    
    # Review responses
    path('<int:review_id>/respond/', views.ReviewResponseCreateView.as_view(), name='review-response-create'),
    
    # Review flags
    path('<int:review_id>/flag/', views.flag_review, name='flag-review'),
]
