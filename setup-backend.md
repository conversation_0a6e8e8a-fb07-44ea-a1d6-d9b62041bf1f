# CoJourneyHub Backend Setup Guide

This guide will help you set up the complete backend for the CoJourneyHub ride-sharing application.

## Prerequisites

1. **Node.js** (v18 or higher)
2. **npm** or **yarn**
3. **Supabase Account** (free tier available)

## Step 1: Install Dependencies

Run the following command to install all required dependencies:

```bash
npm install
```

This will install:
- `leaflet` and `react-leaflet` for map functionality
- `@types/leaflet` for TypeScript support
- All existing dependencies

## Step 2: Set Up Supabase Project

1. **Create a Supabase Account**
   - Go to [supabase.com](https://supabase.com)
   - Sign up for a free account

2. **Create a New Project**
   - Click "New Project"
   - Choose your organization
   - Enter project name: "cojourneyhub"
   - Enter a secure database password
   - Choose a region close to your users (e.g., Europe for Tunisia)
   - Click "Create new project"

3. **Get Your Project Credentials**
   - Go to Settings > API
   - Copy the "Project URL" and "anon public" key

## Step 3: Configure Environment Variables

1. **Update the .env file** with your Supabase credentials:

```env
VITE_SUPABASE_URL=your_actual_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
VITE_APP_NAME=CoJourneyHub
VITE_APP_URL=http://localhost:5173
```

## Step 4: Set Up Database Schema

1. **Open Supabase SQL Editor**
   - In your Supabase dashboard, go to "SQL Editor"

2. **Run the Schema Script**
   - Copy the contents of `supabase-schema.sql`
   - Paste it into the SQL Editor
   - Click "Run" to create all tables

3. **Set Up Row Level Security**
   - Copy the contents of `supabase-rls-policies.sql`
   - Paste it into the SQL Editor
   - Click "Run" to set up security policies

4. **Add Sample Data (Optional)**
   - Copy the contents of `supabase-sample-data.sql`
   - Paste it into the SQL Editor
   - Click "Run" to add sample locations and test data

## Step 5: Configure Authentication

1. **Enable Email Authentication**
   - Go to Authentication > Settings
   - Ensure "Enable email confirmations" is turned on
   - Set up email templates if desired

2. **Configure Site URL**
   - In Authentication > Settings
   - Set Site URL to: `http://localhost:5173`
   - Add redirect URLs if needed

## Step 6: Test the Application

1. **Start the Development Server**
   ```bash
   npm run dev
   ```

2. **Test Core Features**
   - Sign up for a new account
   - Search for rides
   - Offer a new ride
   - Book a ride
   - Test messaging system
   - Check notifications

## Features Included

### ✅ Complete Backend Integration
- **Database**: PostgreSQL with Supabase
- **Authentication**: Email/password with Supabase Auth
- **Real-time**: Live updates for rides and messages
- **File Storage**: Avatar uploads (ready for implementation)

### ✅ Core Functionality
- **User Management**: Registration, login, profiles
- **Ride Management**: Create, search, book, cancel rides
- **Messaging**: Real-time chat between users
- **Notifications**: System notifications for ride updates
- **Reviews**: Rate drivers and passengers
- **Loyalty Points**: Reward system for active users

### ✅ Map Integration
- **Free Maps**: OpenStreetMap with Leaflet
- **Location Search**: Tunisia-specific location autocomplete
- **Route Visualization**: Show routes between origin and destination
- **Interactive Maps**: Click to select locations

### ✅ Advanced Features
- **Search Filters**: By location, date, price, seats
- **Real-time Updates**: Live ride availability
- **Mobile Responsive**: Works on all devices
- **Multi-language**: English, French, Arabic support
- **Dark Mode**: Theme switching

## Database Schema

The application includes these main tables:
- `profiles` - User profiles and settings
- `rides` - Ride offers with all details
- `ride_bookings` - Booking relationships
- `messages` - User-to-user messaging
- `notifications` - System notifications
- `reviews` - User ratings and feedback
- `tunisia_locations` - Location data with coordinates

## Security Features

- **Row Level Security (RLS)** on all tables
- **User isolation** - users can only access their own data
- **Secure authentication** with Supabase Auth
- **Input validation** with Zod schemas
- **SQL injection protection** with parameterized queries

## Performance Optimizations

- **Database indexes** on frequently queried columns
- **Efficient queries** with proper joins
- **Caching** for location data
- **Lazy loading** for large datasets
- **Optimized images** and assets

## Troubleshooting

### Common Issues

1. **"Supabase not configured" error**
   - Check your .env file has correct credentials
   - Ensure the Supabase project is active
   - Verify the URL and key are correct

2. **Database connection errors**
   - Check your internet connection
   - Verify Supabase project is running
   - Check if you've exceeded free tier limits

3. **Authentication issues**
   - Ensure email confirmation is set up
   - Check spam folder for confirmation emails
   - Verify site URL in Supabase settings

4. **Map not loading**
   - Check browser console for errors
   - Ensure internet connection for map tiles
   - Verify coordinates are valid

## Next Steps

1. **Production Deployment**
   - Deploy to Vercel, Netlify, or similar
   - Update environment variables for production
   - Configure custom domain

2. **Additional Features**
   - Payment integration (Stripe, PayPal)
   - Push notifications
   - SMS verification
   - Advanced analytics

3. **Mobile App**
   - React Native version
   - Expo development
   - App store deployment

## Support

For issues or questions:
1. Check the browser console for errors
2. Review Supabase logs in the dashboard
3. Ensure all environment variables are set correctly
4. Test with sample data first

The application is now ready for production use with a complete backend infrastructure!
