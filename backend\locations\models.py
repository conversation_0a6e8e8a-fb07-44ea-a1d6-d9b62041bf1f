from django.db import models
import math


class TunisiaLocation(models.Model):
    """Model for Tunisia locations with coordinates"""

    LOCATION_TYPES = [
        ('city', 'City'),
        ('university', 'University'),
        ('hospital', 'Hospital'),
        ('airport', 'Airport'),
        ('landmark', 'Landmark'),
        ('station', 'Station'),
        ('mall', 'Shopping Mall'),
        ('hotel', 'Hotel'),
    ]

    name = models.CharField(max_length=255, unique=True)
    name_ar = models.CharField(max_length=255, blank=True)  # Arabic name
    name_fr = models.CharField(max_length=255, blank=True)  # French name

    # Coordinates (using separate lat/lng fields for simplicity)
    latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    location_type = models.CharField(max_length=20, choices=LOCATION_TYPES, default='city')

    # Administrative divisions
    governorate = models.Char<PERSON><PERSON>(max_length=100, blank=True)  # Wilaya
    delegation = models.Char<PERSON>ield(max_length=100, blank=True)   # Mutamadiyya

    # Additional information
    population = models.PositiveIntegerField(null=True, blank=True)
    postal_code = models.CharField(max_length=10, blank=True)
    is_popular = models.BooleanField(default=False)  # For featured locations

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tunisia_locations'
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['location_type']),
            models.Index(fields=['governorate']),
            models.Index(fields=['is_popular']),
        ]

    def __str__(self):
        return self.name

    @property
    def coordinates(self):
        """Get coordinates as tuple (lat, lng)"""
        if self.latitude and self.longitude:
            return (float(self.latitude), float(self.longitude))
        return None

    def set_coordinates(self, lat, lng):
        """Set coordinates from lat/lng values"""
        self.latitude = lat
        self.longitude = lng


class Route(models.Model):
    """Model for popular routes between locations"""

    origin = models.ForeignKey(
        TunisiaLocation,
        on_delete=models.CASCADE,
        related_name='routes_from'
    )
    destination = models.ForeignKey(
        TunisiaLocation,
        on_delete=models.CASCADE,
        related_name='routes_to'
    )

    # Route information
    distance_km = models.PositiveIntegerField()
    estimated_duration_minutes = models.PositiveIntegerField()

    # Route geometry (stored as JSON for simplicity)
    route_coordinates = models.JSONField(null=True, blank=True)

    # Popularity and statistics
    search_count = models.PositiveIntegerField(default=0)
    is_popular = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'routes'
        unique_together = ['origin', 'destination']
        ordering = ['-search_count']

    def __str__(self):
        return f"{self.origin.name} → {self.destination.name}"

    def increment_search_count(self):
        """Increment search count for this route"""
        self.search_count += 1
        self.save(update_fields=['search_count'])


class LocationSearch(models.Model):
    """Model to track location searches for analytics"""

    query = models.CharField(max_length=255)
    result_count = models.PositiveIntegerField(default=0)
    selected_location = models.ForeignKey(
        TunisiaLocation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    # User context (optional)
    user_ip = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'location_searches'
        ordering = ['-created_at']

    def __str__(self):
        return f"Search: {self.query}"
