import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { enhancedTunisiaLocations } from '@/data/enhancedTunisiaLocations';

interface LocationResult {
  name: string;
  latitude?: number;
  longitude?: number;
  type?: string;
}

interface LocationSearchProps {
  placeholder?: string;
  value?: string;
  onChange?: (location: string, coordinates?: { lat: number; lng: number }) => void;
  onSelect?: (location: LocationResult) => void;
  className?: string;
  disabled?: boolean;
}

const LocationSearch: React.FC<LocationSearchProps> = ({
  placeholder = "Search for a location...",
  value = "",
  onChange,
  onSelect,
  className = "",
  disabled = false
}) => {
  const [searchTerm, setSearchTerm] = useState(value);
  const [suggestions, setSuggestions] = useState<LocationResult[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Tunisia location coordinates mapping
  const locationCoordinates: Record<string, { lat: number; lng: number }> = {
    'Tunis': { lat: 36.8065, lng: 10.1815 },
    'Sfax': { lat: 34.7406, lng: 10.7603 },
    'Sousse': { lat: 35.8256, lng: 10.6369 },
    'Kairouan': { lat: 35.6781, lng: 10.0963 },
    'Bizerte': { lat: 37.2744, lng: 9.8739 },
    'Gabès': { lat: 33.8815, lng: 10.0982 },
    'Ariana': { lat: 36.8625, lng: 10.1956 },
    'Gafsa': { lat: 34.4250, lng: 8.7842 },
    'Monastir': { lat: 35.7643, lng: 10.8113 },
    'Ben Arous': { lat: 36.7544, lng: 10.2181 },
    'Kasserine': { lat: 35.1674, lng: 8.8363 },
    'Médenine': { lat: 33.3549, lng: 10.5055 },
    'Nabeul': { lat: 36.4561, lng: 10.7376 },
    'Tataouine': { lat: 32.9297, lng: 10.4517 },
    'Béja': { lat: 36.7256, lng: 9.1816 },
    'Jendouba': { lat: 36.5014, lng: 8.7800 },
    'El Kef': { lat: 36.1699, lng: 8.7049 },
    'Mahdia': { lat: 35.5047, lng: 11.0622 },
    'Sidi Bouzid': { lat: 35.0381, lng: 9.4858 },
    'Tozeur': { lat: 33.9197, lng: 8.1335 },
    'La Marsa': { lat: 36.8708, lng: 10.3469 },
    'Sidi Bou Said': { lat: 36.8708, lng: 10.3469 },
    'Carthage': { lat: 36.8531, lng: 10.3208 },
    'Gammarth': { lat: 36.8989, lng: 10.3208 },
    'Aéroport Tunis-Carthage': { lat: 36.8510, lng: 10.2272 },
    'Aéroport Enfidha-Hammamet': { lat: 36.0758, lng: 10.4386 },
    'Université de Tunis': { lat: 36.8485, lng: 10.1856 },
    'INSAT': { lat: 36.8442, lng: 10.1929 },
    'ESPRIT': { lat: 36.8989, lng: 10.1897 }
  };

  useEffect(() => {
    setSearchTerm(value);
  }, [value]);

  useEffect(() => {
    if (searchTerm.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const filtered = enhancedTunisiaLocations
      .filter(location => 
        location.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .slice(0, 8)
      .map(location => {
        const coords = locationCoordinates[location];
        return {
          name: location,
          latitude: coords?.lat,
          longitude: coords?.lng,
          type: getLocationType(location)
        };
      });

    setSuggestions(filtered);
    setShowSuggestions(filtered.length > 0);
    setSelectedIndex(-1);
  }, [searchTerm]);

  const getLocationType = (location: string): string => {
    if (location.includes('Université') || location.includes('INSAT') || location.includes('ESPRIT')) {
      return 'university';
    }
    if (location.includes('Aéroport')) {
      return 'airport';
    }
    if (location.includes('Hôpital') || location.includes('Clinique')) {
      return 'hospital';
    }
    if (location.includes('Médina') || location.includes('Amphithéâtre')) {
      return 'landmark';
    }
    return 'city';
  };

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'university':
        return '🎓';
      case 'airport':
        return '✈️';
      case 'hospital':
        return '🏥';
      case 'landmark':
        return '🏛️';
      default:
        return '📍';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    onChange?.(newValue);
  };

  const handleSuggestionClick = (suggestion: LocationResult) => {
    setSearchTerm(suggestion.name);
    setShowSuggestions(false);
    
    const coordinates = suggestion.latitude && suggestion.longitude 
      ? { lat: suggestion.latitude, lng: suggestion.longitude }
      : undefined;
    
    onChange?.(suggestion.name, coordinates);
    onSelect?.(suggestion);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionClick(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 200);
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
          className="pl-10 pr-4"
          disabled={disabled}
        />
        {searchTerm && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => {
              setSearchTerm('');
              onChange?.('');
              inputRef.current?.focus();
            }}
          >
            ×
          </Button>
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.name}-${index}`}
              className={`px-4 py-2 cursor-pointer flex items-center space-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 ${
                index === selectedIndex ? 'bg-gray-100 dark:bg-gray-700' : ''
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <span className="text-lg">
                {getLocationIcon(suggestion.type || 'city')}
              </span>
              <div className="flex-1">
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {suggestion.name}
                </div>
                {suggestion.type && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                    {suggestion.type}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LocationSearch;
