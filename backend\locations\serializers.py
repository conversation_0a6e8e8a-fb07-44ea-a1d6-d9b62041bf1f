from rest_framework import serializers
from .models import TunisiaLocation, Route, LocationSearch


class TunisiaLocationSerializer(serializers.ModelSerializer):
    """Serializer for Tunisia locations"""
    
    coordinates = serializers.ReadOnlyField()
    
    class Meta:
        model = TunisiaLocation
        fields = [
            'id', 'name', 'name_ar', 'name_fr', 'coordinates',
            'location_type', 'governorate', 'delegation',
            'population', 'postal_code', 'is_popular'
        ]


class LocationSearchSerializer(serializers.ModelSerializer):
    """Serializer for location search"""
    
    class Meta:
        model = LocationSearch
        fields = ['query', 'result_count', 'selected_location']


class RouteSerializer(serializers.ModelSerializer):
    """Serializer for routes"""
    
    origin = TunisiaLocationSerializer(read_only=True)
    destination = TunisiaLocationSerializer(read_only=True)
    
    class Meta:
        model = Route
        fields = [
            'id', 'origin', 'destination', 'distance_km',
            'estimated_duration_minutes', 'search_count', 'is_popular'
        ]


class LocationAutocompleteSerializer(serializers.Serializer):
    """Serializer for location autocomplete response"""
    
    id = serializers.IntegerField()
    name = serializers.CharField()
    type = serializers.CharField()
    governorate = serializers.CharField()
    coordinates = serializers.ListField(child=serializers.FloatField())


class RouteCalculationSerializer(serializers.Serializer):
    """Serializer for route calculation request"""
    
    origin_lat = serializers.DecimalField(max_digits=10, decimal_places=8)
    origin_lng = serializers.DecimalField(max_digits=11, decimal_places=8)
    destination_lat = serializers.DecimalField(max_digits=10, decimal_places=8)
    destination_lng = serializers.DecimalField(max_digits=11, decimal_places=8)


class RouteResponseSerializer(serializers.Serializer):
    """Serializer for route calculation response"""
    
    distance_km = serializers.FloatField()
    duration_minutes = serializers.IntegerField()
    route_coordinates = serializers.ListField(
        child=serializers.ListField(child=serializers.FloatField()),
        required=False
    )
