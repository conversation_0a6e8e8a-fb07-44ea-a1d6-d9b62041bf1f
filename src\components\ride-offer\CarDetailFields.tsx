
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Car, Users } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Control } from "react-hook-form";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  color: string;
  license_plate: string;
  seats: number;
  vehicle_type: string;
}

interface CarDetailFieldsProps {
  control: Control<any>;
  isRTL: boolean;
  vehicles: Vehicle[];
  selectedVehicle: Vehicle | null;
  onVehicleSelect: (vehicle: Vehicle) => void;
}

const CarDetailFields = ({ control, isRTL, vehicles, selectedVehicle, onVehicleSelect }: CarDetailFieldsProps) => {
  const { translations } = useLanguage();

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name="vehicleId"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">Select Vehicle</FormLabel>
            <Select
              onValueChange={(value) => {
                field.onChange(value);
                const vehicle = vehicles.find(v => v.id.toString() === value);
                if (vehicle) {
                  onVehicleSelect(vehicle);
                }
              }}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Choose your vehicle" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {vehicles.map((vehicle) => (
                  <SelectItem key={vehicle.id} value={vehicle.id.toString()}>
                    <div className="flex items-center gap-2">
                      <Car className="w-4 h-4" />
                      <span>
                        {vehicle.year} {vehicle.make} {vehicle.model} ({vehicle.color})
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {selectedVehicle && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Car className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold">
                  {selectedVehicle.year} {selectedVehicle.make} {selectedVehicle.model}
                </h4>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Badge variant="outline">{selectedVehicle.color}</Badge>
                  <Badge variant="outline">{selectedVehicle.license_plate}</Badge>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{selectedVehicle.seats} seats</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {vehicles.length === 0 && (
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <Car className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
          <p className="text-yellow-800">No vehicles found. Please add a vehicle to your profile first.</p>
        </div>
      )}
    </div>
  );
};

export default CarDetailFields;
