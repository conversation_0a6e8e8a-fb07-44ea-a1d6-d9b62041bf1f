
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Car } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Control } from "react-hook-form";

interface CarDetailFieldsProps {
  control: Control<any>;
  isRTL: boolean;
}

const CarDetailFields = ({ control, isRTL }: CarDetailFieldsProps) => {
  const { translations } = useLanguage();

  return (
    <>
      <FormField
        control={control}
        name="carModel"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.carModel}</FormLabel>
            <div className="relative">
              <Car className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
              <FormControl>
                <Input placeholder="e.g. Volkswagen Polo" className={isRTL ? 'pr-10 text-right' : 'pl-10'} {...field} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name="carColor"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.carColor}</FormLabel>
            <FormControl>
              <Input placeholder="e.g. White" className={isRTL ? 'text-right' : ''} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default CarDetailFields;
