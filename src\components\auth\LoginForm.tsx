
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail, Lock } from "lucide-react";
import { toast } from "sonner";
import { useUser } from "@/contexts/UserContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

type LoginFormProps = {
  redirectPath: string | null;
};

const LoginForm = ({ redirectPath }: LoginFormProps) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { pendingRideOffer, pendingRideRequest } = useUser();
  const { login } = useAuth();
  const { translations, language } = useLanguage();
  const isRTL = language === "ar";
  const navigate = useNavigate();

  // Handle redirect after successful login
  const handleRedirectAfterLogin = () => {
    if (redirectPath === "offer-ride" && pendingRideOffer) {
      navigate("/offer-ride");
    } else if (redirectPath === "search" && pendingRideRequest) {
      const { origin, destination, date, passengers } = pendingRideRequest;
      navigate(`/?origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&date=${encodeURIComponent(date)}&passengers=${passengers}`);
    } else {
      navigate("/");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('Login form submitted with:', { email, password: '***' });

    if (!email.trim() || !password.trim()) {
      toast.error(translations.allFieldsRequired || "Please enter both email and password");
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Calling login function...');
      // Use Django authentication
      const success = await login(email, password);

      console.log('Login result:', success);

      if (success) {
        console.log('Login successful, redirecting...');
        // Small delay to allow state to update
        setTimeout(handleRedirectAfterLogin, 100);
      } else {
        console.log('Login failed');
        toast.error(translations.loginFailed || "Login failed");
      }

    } catch (error: any) {
      console.error("Login error in form:", error);
      toast.error(
        translations.loginFailed || "Login failed",
        { description: error.message || translations.invalidCredentials || "Invalid credentials" }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md space-y-4">
      {/* Test Credentials Info */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h3 className="font-semibold text-blue-800 mb-2">Test Credentials</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> password123</p>
            <p className="text-xs mt-2">Or use any email/password for development mode</p>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-lg animate-fade-in">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">{translations.login || "Login"}</CardTitle>
          <CardDescription>{translations.enterCredentials || "Enter your credentials to login"}</CardDescription>
        </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">{translations.email || "Email"}</Label>
            <div className="relative">
              <Mail className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4`} />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className={`${isRTL ? 'pr-10' : 'pl-10'}`}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="password">{translations.password || "Password"}</Label>
              <Link
                to="/forgot-password"
                className="text-sm text-primary hover:underline"
              >
                {translations.forgotPassword || "Forgot password?"}
              </Link>
            </div>
            <div className="relative">
              <Lock className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4`} />
              <Input
                id="password"
                type="password"
                className={`${isRTL ? 'pr-10' : 'pl-10'}`}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-3">
          <Button
            type="submit"
            className="w-full transition-all hover:scale-[1.01]"
            disabled={isSubmitting}
          >
            {isSubmitting ? translations.loggingIn || "Logging in..." : translations.login || "Login"}
          </Button>
          <div className="text-sm text-center text-gray-500">
            {translations.dontHaveAccount || "Don't have an account?"}{" "}
            <Link to={`/signup${redirectPath ? `?redirect=${redirectPath}` : ''}`} className="text-primary hover:underline">
              {translations.signup || "Sign up"}
            </Link>
          </div>
        </CardFooter>
      </form>
    </Card>
    </div>
  );
};

export default LoginForm;
