
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Calendar, Clock } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Control } from "react-hook-form";

interface DepartureFieldsProps {
  control: Control<any>;
  isRTL: boolean;
}

const DepartureFields = ({ control, isRTL }: DepartureFieldsProps) => {
  const { translations } = useLanguage();

  return (
    <>
      <FormField
        control={control}
        name="departureDate"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.departureDate}</FormLabel>
            <div className="relative">
              <Calendar className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
              <FormControl>
                <Input type="date" className={isRTL ? 'pr-10 text-right' : 'pl-10'} {...field} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name="departureTime"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.departureTime}</FormLabel>
            <div className="relative">
              <Clock className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
              <FormControl>
                <Input type="time" className={isRTL ? 'pr-10 text-right' : 'pl-10'} {...field} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default DepartureFields;
