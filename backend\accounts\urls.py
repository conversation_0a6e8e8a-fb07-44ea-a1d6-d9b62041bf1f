from django.urls import path
from . import views

urlpatterns = [
    # Authentication
    path('register/', views.UserRegistrationView.as_view(), name='user-register'),
    path('login/', views.login_view, name='user-login'),
    path('logout/', views.logout_view, name='user-logout'),
    
    # User profile
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),
    path('profile/update/', views.UserUpdateView.as_view(), name='user-update'),
    path('profile/details/', views.UserProfileDetailView.as_view(), name='user-profile-details'),
    path('change-password/', views.change_password_view, name='change-password'),
    
    # User statistics
    path('stats/', views.UserStatsView.as_view(), name='user-stats'),
    path('loyalty-points/', views.add_loyalty_points_view, name='add-loyalty-points'),
    
    # Vehicles
    path('vehicles/', views.VehicleListCreateView.as_view(), name='vehicle-list-create'),
    path('vehicles/<int:pk>/', views.VehicleDetailView.as_view(), name='vehicle-detail'),
    
    # Public user information
    path('users/<int:user_id>/', views.user_detail_view, name='user-detail'),
]
